import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:venvi/constants/tier.dart';
import 'package:venvi/global_view_models/org_view_model.dart';
import 'package:venvi/global_view_models/permission_view_model.dart';
import 'package:venvi/repositories/con_repository.dart';
import 'package:venvi/screens/org_con_list/org_con_list_state.dart';
import 'package:venvi/screens/org_con_list/org_con_list_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/con_list_item.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/glass_app_bar.dart';

class OrgConListScreen extends HookWidget {
  const OrgConListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<OrgConListViewModel>(
      create:
          (context) => OrgConListViewModel(
            context.read<OrgViewModel>(),
            context.read<PermissionViewModel>(),
            context.read<ConRepository>(),
          ),
      child: Scaffold(
        appBar: GlassAppBar(title: const Text('Con List')),
        body: BlocBuilder<OrgConListViewModel, OrgConListState>(
          builder:
              (context, state) =>
                  state.loadingCons
                      ? const Center(child: CircularProgressIndicator())
                      : ListView.separated(
                        padding: const EdgeInsets.all(AppTheme.screenPadding),
                        itemCount: state.conModels.length,
                        separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                        itemBuilder: (context, index) {
                          final conModel = state.conModels[index];
                          final isPublished = state.publishedConIds?.contains(conModel.id);
                          if (isPublished == null) {
                            return ContentArea(padding: EdgeInsets.zero, child: ConListItem(conModel: conModel));
                          }
                          return ContentArea(
                            child: Row(
                              children: [
                                Expanded(child: ConListItem(conModel: conModel)),
                                Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: AppTheme.widgetPadding),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(isPublished ? Icons.visibility : Icons.visibility_off),
                                      const SizedBox(height: AppTheme.widgetPaddingVerySmall),
                                      Text(isPublished ? 'Published' : 'Unpublished'),
                                      const SizedBox(height: AppTheme.widgetPadding),
                                      Icon(conModel.tier == Tier.premium ? Icons.stars : Icons.stars_outlined),
                                      const SizedBox(height: AppTheme.widgetPaddingVerySmall),
                                      Text(conModel.tier?.label ?? ''),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
        ),
      ),
    );
  }
}
