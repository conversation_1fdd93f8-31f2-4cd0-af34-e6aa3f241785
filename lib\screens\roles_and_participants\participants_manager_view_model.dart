import 'dart:async';

import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/profile_model.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class ParticipantsManagerViewModel extends EditorScaffoldViewModel<List<ParticipantModel>> {
  final ConData _conData;
  final ParticipantRepository _participantRepository;
  late final List<String> initialParticipantIds;

  ParticipantsManagerViewModel(super.initialState, this._conData, this._participantRepository);

  @override
  bool checkChanges(List<ParticipantModel> initialState, List<ParticipantModel> currentState) {
    return initialState != currentState;
  }

  @override
  Future<List<ParticipantModel>> applyChanges(List<ParticipantModel> initialState, List<ParticipantModel> state) async {
    final removedParticipants = initialState.where((element) => !state.contains(element)).toList();
    final addedParticipants = state.where((element) => !initialState.contains(element)).toList();

    final List<Future<bool>> removedFutures = [];
    final List<Future<String?>> addedFutures = [];
    for (final participant in removedParticipants) {
      if (participant.id != null) {
        removedFutures.add(_participantRepository.removeParticipant(_conData, participant.id!));
      }
    }
    for (final participant in addedParticipants) {
      if (participant.id != null) {
        addedFutures.add(_participantRepository.addParticipant(_conData, participant.id!, participant));
      }
    }

    final removeResults = await Future.wait(removedFutures);
    final addResults = await Future.wait(addedFutures);

    if (removeResults.contains(false) || addResults.contains(null)) {
      throw const EditorScaffoldException(['Failed to update participants']);
    }

    return state;
  }

  void addParticipantFromProfile(ProfileModel model) {
    addParticipant(ParticipantModel.fromProfileModel(model, null, null));
  }

  void addParticipant(ParticipantModel model) {
    emit([
      ...state,
      model,
    ]);
  }

  void removeParticipant(String participantId) {
    final participants = List<ParticipantModel>.from(state);
    participants.removeWhere((element) => element.id == participantId);
    emit(participants);
  }
}
