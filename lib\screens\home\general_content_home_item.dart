import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_network_image_platform_interface/cached_network_image_platform_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/general_content_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/shimmer_square.dart';

class GeneralContentHomeItem extends StatelessWidget {
  final GeneralContentModel generalContentModel;

  const GeneralContentHomeItem({super.key, required this.generalContentModel});

  @override
  Widget build(BuildContext context) {
    return ContentArea(
      onTap: () => context.go('${context.read<ConData>().conPath}/home/<USER>/${generalContentModel.id}'),
      child: Row(
        children: [
          generalContentModel.primaryImage?.downloadUrl != null
              ? Container(
                  width: 100,
                  height: 100,
                  clipBehavior: Clip.hardEdge,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(AppTheme.borderRadius),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(AppTheme.borderRadius),
                    child: CachedNetworkImage(
                      imageRenderMethodForWeb: ImageRenderMethodForWeb.HttpGet,
                      fit: BoxFit.cover,
                      imageUrl: generalContentModel.primaryImage!.downloadUrl!,
                      placeholder: (context, url) => const ShimmerSquare(),
                      errorWidget: (context, url, error) => const SizedBox.shrink(),
                    ),
                  ),
                )
              : const SizedBox.shrink(),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.widgetPadding,
                vertical: AppTheme.widgetPaddingSmall,
              ),
              child: Text(
                generalContentModel.title ?? 'Nameless Page',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
              ),
            ),
          ),
          const Icon(Icons.chevron_right),
        ],
      ),
    );
  }
}
