import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/models/org_active_con_model.dart';
import 'package:venvi/models/org_model.dart';
import 'package:venvi/models/user_model.dart';
import 'package:venvi/repositories/org_repository.dart';
import 'package:venvi/screens/con_search/con_search_state.dart';
import 'package:venvi/utils/logger.dart';
import 'package:venvi/utils/search_util.dart';

class ConSearchViewModel extends Cubit<ConSearchState> {
  static const int _maxSearchResults = 10;

  final OrgRepository _orgRepository;

  StreamSubscription? _userSubscription;

  String _latestSearchText = '';
  List<OrgActiveConModel> _favoriteCons = [];
  List<OrgActiveConModel> _authorizedCons = [];
  List<String> _favoriteOrgIds = [];

  ConSearchViewModel(this._orgRepository, UserViewModel userViewModel)
    : super(
        const ConSearchState(
          conSearchModels: [],
          favoriteConModels: [],
          authorizedConModels: [],
          favoriteOrgIds: [],
          isSearching: false,
        ),
      ) {
    try {
      _userSubscription = userViewModel.stream.listen((userModel) {
        _updateSavedConsList(userModel);
      });
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace);
    }
    _updateSavedConsList(userViewModel.state);
  }

  @override
  Future<void> close() async {
    _userSubscription?.cancel();
    return super.close();
  }

  void setSearchText(String searchText) {
    searchText = SearchUtil.sanitizeForFirestoreSearch(searchText);
    if (_latestSearchText == searchText) {
      return;
    }
    _latestSearchText = searchText;
    _searchUpdated(currentSearchText: searchText);
  }

  Future<void> _searchUpdated({String? currentSearchText}) async {
    currentSearchText ??= _latestSearchText;
    // Shows favorite cons if search is empty
    if (_latestSearchText.isEmpty) {
      if (!isClosed) {
        emit(
          ConSearchState(
            conSearchModels: [],
            favoriteConModels: _favoriteCons,
            authorizedConModels: _authorizedCons,
            favoriteOrgIds: _favoriteOrgIds,
            isSearching: false,
          ),
        );
      }
    } else {
      final conSearchModels = await _orgRepository
          .searchForOrgs(_latestSearchText, _maxSearchResults)
          .then((orgs) => orgs?.map((org) => org.activeCon).whereType<OrgActiveConModel>().toList());

      // Makes sure there wasn't a newer search since this one started
      if (_latestSearchText != currentSearchText) {
        return;
      }

      if (!isClosed) {
        emit(
          ConSearchState(
            conSearchModels: conSearchModels ?? [],
            favoriteConModels: _favoriteCons,
            authorizedConModels: _authorizedCons,
            favoriteOrgIds: _favoriteOrgIds,
            isSearching: true,
          ),
        );
      }
    }
  }

  Future<void> _updateSavedConsList(UserModel userModel) async {
    // Gets favorite and authorized orgs
    final favoriteOrgIds = userModel.favoriteOrgs ?? [];
    _favoriteOrgIds = favoriteOrgIds;
    final authorizedOrgIds = userModel.authorizedOrgs ?? [];
    if (favoriteOrgIds.isEmpty && authorizedOrgIds.isEmpty) {
      _favoriteCons = [];
      _authorizedCons = [];
      _searchUpdated();
      return;
    }

    // Creates list of futures
    final List<Future<OrgModel?>> favoriteOrgFutures = [];
    for (final favoriteOrgId in favoriteOrgIds) {
      final favoriteOrgFuture = _orgRepository.getOrg(favoriteOrgId);
      favoriteOrgFutures.add(favoriteOrgFuture);
    }
    final List<Future<OrgModel?>> authorizedOrgFutures = [];
    for (final authorizedOrgId in authorizedOrgIds) {
      final authorizedOrgFuture = _orgRepository.getOrg(authorizedOrgId);
      authorizedOrgFutures.add(authorizedOrgFuture);
    }

    // Waits for futures to finish and loads them into returned list
    final List<OrgActiveConModel> loadedFavoriteCons = [];
    for (final favoriteOrgFuture in favoriteOrgFutures) {
      final loadedFavoriteOrg = await favoriteOrgFuture;
      final activeCon = loadedFavoriteOrg?.activeCon;
      if (activeCon != null) {
        loadedFavoriteCons.add(activeCon);
      }
    }
    final List<OrgActiveConModel> loadedAuthorizedCons = [];
    for (final authorizedOrgFuture in authorizedOrgFutures) {
      final loadedAuthorizedOrg = await authorizedOrgFuture;
      final activeCon = loadedAuthorizedOrg?.activeCon;
      if (activeCon != null) {
        loadedAuthorizedCons.add(activeCon);
      }
    }

    _favoriteCons = loadedFavoriteCons;
    _authorizedCons = loadedAuthorizedCons;
    _searchUpdated();
  }
}
