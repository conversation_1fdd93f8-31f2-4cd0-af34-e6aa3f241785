import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class SnackBars {
  static Future<void> showInfoSnackBar(
    BuildContext context,
    String message,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Text(message),
        showCloseIcon: true,
        behavior: kIsWeb ? SnackBarBehavior.floating : SnackBarBehavior.fixed,
      ),
    );
  }
}
