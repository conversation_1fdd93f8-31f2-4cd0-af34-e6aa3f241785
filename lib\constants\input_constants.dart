class InputConstants {
  // Con
  static const int maxConNameLength = 50;
  static const int maxConLocationLength = 50;
  static const int maxConDays = 7;

  // Events
  static const int maxEventTitleLength = 50;
  static const int maxEventDescLength = 2000;
  static const int maxEventGalleryImages = 8;
  static const int maxEventTags = 3;
  static const int maxParticipants = 20;
  static const Duration defaultEventDuration = Duration(hours: 1);

  // Event properties
  static const int maxTypeNameLength = 20;
  static const int maxConTags = 20;
  static const int maxTagLength = 15;

  // Roles
  static const int maxRoleNameLength = 50;

  // Maps
  static const int maxMaps = 20;
  static const int maxMapNameLength = 50;
  static const int maxVenueNameLength = 50;

  // General content
  static const int maxGeneralContentPages = 10;
  static const int maxGeneralContentSections = 25;
  static const int maxGeneralContentSectionTitleLength = 50;
  static const int maxGeneralContentHeadlineLength = 150;
  static const int maxGeneralContentDetailsLength = 800;

  // Profile
  static const int maxProfileDisplayNameLength = 50;
  static const int maxProfileUsernameLength = 50;
  static const int maxProfileHeadlineLength = 100;
  static const int maxProfileBioLength = 600;
  static final RegExp usernameRegEx = RegExp(r'[^a-zA-Z0-9_.]');
  static const Duration lastUpdatedNameTimeout = Duration(days: 7);

  // Announcements
  static const int maxAnnouncementHeadlineLength = 50;
  static const int maxAnnouncementDetailsLength = 300;

  // Starter tier limits
  static const int maxStarterTierEvents = 100;
}
