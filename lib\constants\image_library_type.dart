import 'package:venvi/constants/image_type.dart';

enum ImageLibraryType {
  homeScreenGallery(imageType: ImageType.homeScreenGallery, firestoreCollection: 'homeScreenGallery'),
  reusableEventPrimary(imageType: ImageType.reusableEventPrimary, firestoreCollection: 'reusablePrimaryEventImages');

  final ImageType imageType;
  final String firestoreCollection;

  const ImageLibraryType({
    required this.imageType,
    required this.firestoreCollection,
  });
}
