import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/rules.dart';
import 'package:venvi/global_view_models/permission_view_model.dart';

class RestrictedView extends StatelessWidget {
  final Rules rule;
  final bool Function()? additionalPermitted;
  final Widget child;

  const RestrictedView({
    super.key,
    required this.rule,
    this.additionalPermitted,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final permissionsState = context.watch<PermissionViewModel>().state;

    if (permissionsState != null && permissionsState.any((userRole) => rule.permissions.contains(userRole)) ||
        (additionalPermitted?.call() ?? false)) {
      return child;
    } else {
      return const SizedBox.shrink();
    }
  }
}
