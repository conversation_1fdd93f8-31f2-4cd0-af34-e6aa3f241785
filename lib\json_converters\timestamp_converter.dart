import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

class TimestampConverter implements JsonConverter<Timestamp?, Timestamp?> {
  const TimestampConverter();

  @override
  Timestamp? from<PERSON>son(Timestamp? timestamp) {
    return timestamp;
  }

  @override
  Timestamp? to<PERSON>son(Timestamp? timestamp) {
    return timestamp;
  }
}
