import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/venue_model.dart';
import 'package:venvi/repositories/venue_repository.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class VenueEditorViewModel extends EditorScaffoldViewModel<List<VenueModel>> {
  final ConData _conData;
  final VenueRepository _venueRepository;

  VenueEditorViewModel(super.initialState, this._conData, this._venueRepository);

  @override
  Future<List<VenueModel>> applyChanges(List<VenueModel> initialState, List<VenueModel> state) async {
    final venuesUpdated = await _venueRepository.updateAllVenues(_conData, state);
    if (venuesUpdated) {
      return state;
    } else {
      throw const EditorScaffoldException(['Failed to update venues']);
    }
  }

  @override
  bool checkChanges(List<VenueModel> initialState, List<VenueModel> currentState) {
    return initialState != currentState;
  }

  void updateVenueName(int index, String name) {
    final newVenues = state.toList();
    newVenues[index] = newVenues[index].copyWith(name: name.trim());
    emit(newVenues);
  }

  void addVenue() {
    final newVenues = state.toList();
    newVenues.add(const VenueModel());
    emit(newVenues);
  }

  void removeVenue(int index) {
    final newVenues = state.toList();
    newVenues.removeAt(index);
    emit(newVenues);
  }
}
