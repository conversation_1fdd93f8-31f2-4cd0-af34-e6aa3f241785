import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/json_converters/required_geo_point_converter.dart';

part 'create_con_location_model.freezed.dart';
part 'create_con_location_model.g.dart';

@freezed
sealed class CreateConLocationModel with _$CreateConLocationModel {
  const factory CreateConLocationModel({
    required String name,
    required String address,
    @RequiredGeoPointConverter() required GeoPoint geoPoint,
    required String timeZoneId,
  }) = _CreateConLocationModel;

  factory CreateConLocationModel.fromJson(Map<String, dynamic> json) => _$CreateConLocationModelFromJson(json);
}
