import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/repositories/profile_repository.dart';
import 'package:venvi/repositories/user_repository.dart';

enum AuthStatus { authenticated, unauthenticated, unknown }

class AuthViewModel extends Cubit<User?> {
  final AuthRepository _repository;
  final UserRepository _userRepository;
  final ProfileRepository _profileRepository;

  late final StreamSubscription<User?> _authSubscription;

  AuthViewModel(this._repository, this._userRepository, this._profileRepository) : super(null) {
    _authSubscription = _repository.authStateChanges.listen((user) async {
      await Future.wait([
        _userRepository.updateUser<PERSON>rom<PERSON>uth(user),
        _profileRepository.updatePro<PERSON>le<PERSON><PERSON><PERSON><PERSON>(user),
      ]);
      if (!isClosed) {
        emit(user);
      }
    });
  }

  @override
  Future<void> close() {
    _authSubscription.cancel();
    return super.close();
  }
}
