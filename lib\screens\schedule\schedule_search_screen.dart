import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:intl/intl.dart';
import 'package:sticky_headers/sticky_headers.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/global_view_models/event_view_model.dart';
import 'package:venvi/global_view_models/named_doc_state.dart';
import 'package:venvi/global_view_models/named_doc_view_model.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/models/event_model.dart';
import 'package:venvi/screens/schedule/schedule_search_state.dart';
import 'package:venvi/screens/schedule/schedule_search_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/accent_label.dart';
import 'package:venvi/widgets/agenda_group.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/search_view.dart';

class ScheduleSearchScreen extends HookWidget {
  const ScheduleSearchScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final searchController = useTextEditingController();

    return BlocProvider(
      create:
          (context) => ScheduleSearchViewModel(
            context.read<ConData>(),
            context.read<UserViewModel>(),
            context.read<EventViewModel>(),
            context.read<ConViewModel>(),
          ),
      child: Builder(
        builder:
            (context) => Scaffold(
              appBar: const GlassAppBar(title: Text('Event Search')),
              body: SearchView(
                autofocus: true,
                controller: searchController,
                onSearchChange: (searchText) => context.read<ScheduleSearchViewModel>().setSearchText(searchText),
                child: BlocBuilder<ScheduleSearchViewModel, ScheduleSearchState>(
                  builder: (context, state) {
                    final events = state.events ?? {};
                    final eventKeys = events.keys.toList();
                    return Column(
                      children: [
                        BlocBuilder<NamedDocViewModel, NamedDocState>(
                          builder: (context, namedDocState) {
                            final eventTypes = context.read<NamedDocViewModel>().getEnabledEventTypes();
                            return SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              padding: const EdgeInsets.symmetric(horizontal: AppTheme.widgetPadding),
                              child: Row(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: AppTheme.widgetPaddingVerySmall),
                                    child: ChoiceChip(
                                      label: const Text('All'),
                                      labelStyle: Theme.of(context).textTheme.labelMedium?.copyWith(
                                        color: Theme.of(context).colorScheme.onSecondary,
                                      ),
                                      checkmarkColor: Theme.of(context).colorScheme.onSecondary,
                                      color: WidgetStateProperty.all<Color>(Theme.of(context).colorScheme.secondary),
                                      selected: state.eventTypeFilter == null,
                                      onSelected: (selected) => context.read<ScheduleSearchViewModel>().setType(null),
                                    ),
                                  ),
                                  ...List.generate(eventTypes.length, (index) {
                                    final eventType = eventTypes[index];
                                    final conEventType = namedDocState.eventProperties?.eventTypes?[eventType];
                                    final eventTypeForegroundColor = eventType.foregroundColor;
                                    return Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: AppTheme.widgetPaddingVerySmall),
                                      child: ChoiceChip(
                                        label: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              conEventType?.icon?.icon ?? eventType.defaultIcon.icon,
                                              color: eventTypeForegroundColor,
                                              size: 16,
                                            ),
                                            const SizedBox(width: AppTheme.widgetPaddingSmall),
                                            Text(
                                              conEventType?.name?.isNotEmpty == true
                                                  ? conEventType!.name!
                                                  : eventType.defaultText,
                                              style: Theme.of(
                                                context,
                                              ).textTheme.labelMedium?.copyWith(color: eventTypeForegroundColor),
                                            ),
                                          ],
                                        ),
                                        checkmarkColor: eventTypeForegroundColor,
                                        color: WidgetStateProperty.all<Color>(
                                          eventType.color.withValues(alpha: AppTheme.coloredContainerOpacity),
                                        ),
                                        selected: state.eventTypeFilter == eventType,
                                        onSelected:
                                            (selected) => context.read<ScheduleSearchViewModel>().setType(eventType),
                                      ),
                                    );
                                  }),
                                ],
                              ),
                            );
                          },
                        ),
                        BlocBuilder<NamedDocViewModel, NamedDocState>(
                          builder: (context, namedDocState) {
                            final allTags = namedDocState.eventProperties?.eventTags?.toList();
                            if (allTags == null || allTags.isEmpty) {
                              return const SizedBox.shrink();
                            }
                            allTags.sort(
                              (a, b) =>
                                  a != null
                                      ? b != null
                                          ? a.compareTo(b)
                                          : 1
                                      : -1,
                            );
                            return SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              padding: const EdgeInsets.symmetric(horizontal: AppTheme.widgetPadding),
                              child: Row(
                                children: List.generate(allTags.length, (index) {
                                  final tag = allTags[index] ?? '';
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 2),
                                    child: ChoiceChip(
                                      visualDensity: VisualDensity.compact,
                                      label: Text(tag),
                                      labelStyle: Theme.of(
                                        context,
                                      ).textTheme.labelSmall?.copyWith(color: Theme.of(context).colorScheme.onTertiary),
                                      checkmarkColor: Theme.of(context).colorScheme.onTertiary,
                                      color: WidgetStateProperty.all<Color>(Theme.of(context).colorScheme.tertiary),
                                      selected: state.tagFilter.contains(tag),
                                      onSelected: (selected) => context.read<ScheduleSearchViewModel>().toggleTag(tag),
                                    ),
                                  );
                                }),
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: AppTheme.widgetPaddingVerySmall),
                        Center(
                          child: SizedBox(
                            width: 280,
                            child: SwitchListTile(
                              dense: true,
                              visualDensity: VisualDensity.compact,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: AppTheme.widgetPadding,
                                vertical: AppTheme.widgetPaddingVerySmall,
                              ),
                              tileColor: Theme.of(context).colorScheme.tertiary,
                              title: Text(
                                'Spotlight Only',
                                style: Theme.of(context).listTileTheme.titleTextStyle?.copyWith(
                                  color: Theme.of(context).colorScheme.onTertiary,
                                ),
                              ),
                              secondary: Icon(
                                state.spotlightOnly == true ? Icons.star : Icons.star_border,
                                color: Theme.of(context).colorScheme.onTertiary,
                              ),
                              value: state.spotlightOnly == true,
                              onChanged: (value) => context.read<ScheduleSearchViewModel>().toggleSpotlightOnly(),
                              activeColor: Theme.of(context).colorScheme.onTertiary,
                              inactiveThumbColor: Theme.of(context).colorScheme.onTertiary,
                              trackOutlineColor: WidgetStateProperty.all(Theme.of(context).colorScheme.onTertiary),
                            ),
                          ),
                        ),
                        const SizedBox(height: AppTheme.widgetPaddingSmall),
                        Expanded(
                          child:
                              events.isNotEmpty
                                  ? ListView.builder(
                                    padding: const EdgeInsets.all(AppTheme.widgetPadding),
                                    itemCount: eventKeys.length,
                                    itemBuilder: (context, index) {
                                      final day = eventKeys[index];
                                      final dayEvents = events[day] ?? {};
                                      final timeKeys = dayEvents.keys.toList();
                                      return StickyHeader(
                                        header: Padding(
                                          padding: const EdgeInsets.all(AppTheme.widgetPadding),
                                          child: AccentLabel(text: _formatDay(day.toDateTime()), isOnContainer: false),
                                        ),
                                        content: ListView.separated(
                                          shrinkWrap: true,
                                          physics: const NeverScrollableScrollPhysics(),
                                          itemCount: timeKeys.length,
                                          separatorBuilder:
                                              (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                                          itemBuilder:
                                              (context, index) => _TimeList(
                                                time: timeKeys[index],
                                                eventsList: dayEvents[timeKeys[index]] ?? [],
                                              ),
                                        ),
                                      );
                                    },
                                  )
                                  : ContentArea(
                                    fillWidth: false,
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Text('No events found'),
                                        const Text('matching the filters'),
                                        if (state.searchTerm.isNotEmpty ||
                                            state.spotlightOnly ||
                                            state.eventTypeFilter != null ||
                                            state.venueFilter != null ||
                                            state.tagFilter.isNotEmpty)
                                          Padding(
                                            padding: const EdgeInsets.only(top: AppTheme.widgetPaddingSmall),
                                            child: ElevatedButton.icon(
                                              onPressed: () {
                                                FocusManager.instance.primaryFocus?.unfocus();
                                                searchController.clear();
                                                context.read<ScheduleSearchViewModel>().setSearchText('');
                                                context.read<ScheduleSearchViewModel>().clearFilters();
                                              },
                                              icon: const Icon(Icons.filter_list),
                                              label: const Text('Clear Filter'),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
      ),
    );
  }

  String _formatDay(DateTime day) {
    return DateFormat.MMMEd().format(day);
  }
}

class _TimeList extends StatelessWidget {
  final TimeOfDay time;
  final List<EventModel> eventsList;

  const _TimeList({required this.time, required this.eventsList});

  @override
  Widget build(BuildContext context) {
    return AgendaGroup(title: time.format(context), events: eventsList);
  }
}
