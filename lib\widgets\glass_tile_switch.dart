import 'package:flutter/material.dart';

class GlassTileSwitch extends StatelessWidget {
  final IconData? icon;
  final String title;
  final String? desc;
  final bool value;
  final void Function(bool)? onChanged;

  const GlassTileSwitch({
    super.key,
    this.icon,
    required this.title,
    this.desc,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SwitchListTile(
      title: Text(title),
      subtitle: desc != null ? Text(desc!) : null,
      secondary: icon != null ? Icon(icon) : null,
      value: value,
      onChanged: onChanged,
    );
  }
}
