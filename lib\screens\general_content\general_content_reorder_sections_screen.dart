import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/general_content_view_model.dart';
import 'package:venvi/models/general_content_model.dart';
import 'package:venvi/repositories/general_content_repository.dart';
import 'package:venvi/screens/general_content/general_content_reorder_sections_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/editor_scaffold.dart';

class GeneralContentReorderSectionsScreen extends StatelessWidget {
  const GeneralContentReorderSectionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return EditorScaffold<GeneralContentReorderSectionsViewModel, List<GeneralContentModel>, GeneralContentViewModel,
        List<GeneralContentModel>?>(
      externalCubit: context.read<GeneralContentViewModel>(),
      buildState: (externalCubitState) => externalCubitState ?? [],
      initViewModel: (state) => GeneralContentReorderSectionsViewModel(
        List<GeneralContentModel>.from(state),
        context.read<ConData>(),
        context.read<GeneralContentRepository>(),
      ),
      title: const Text('Reorder Pages'),
      successMessage: 'Page Order Updated',
      buildContent: (context, viewModel, state) => ListView.separated(
        padding: const EdgeInsets.all(AppTheme.screenPadding),
        itemCount: state.length,
        separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
        itemBuilder: (context, index) {
          final model = state[index];
          return ContentArea(
            key: ValueKey('${context.read<ConData>().conWidgetKey}-${model.id}'),
            child: Row(
              children: [
                Text(
                  model.title ?? '',
                  style: Theme.of(context).listTileTheme.titleTextStyle,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.arrow_upward),
                  onPressed: index > 0 ? () => viewModel.moveTile(index, index - 1) : null,
                ),
                IconButton(
                  icon: const Icon(Icons.arrow_downward),
                  onPressed: index < state.length - 1 ? () => viewModel.moveTile(index, index + 1) : null,
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
