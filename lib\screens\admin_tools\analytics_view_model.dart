import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/event_view_model.dart';
import 'package:venvi/models/analytics_model.dart';
import 'package:venvi/models/event_model.dart';
import 'package:venvi/repositories/named_doc_repository.dart';
import 'package:venvi/screens/admin_tools/analytics_state.dart';
import 'package:venvi/utils/logger.dart';

class AnalyticsViewModel extends Cubit<AnalyticsState> {
  final NamedDocRepository _namedDocRepository;
  final EventViewModel _eventViewModel;

  StreamSubscription? _analyticsSubscription;
  StreamSubscription? _eventSubscription;

  AnalyticsModel? _latestAnalyticsModel;
  List<EventModel>? _latestEventState;

  AnalyticsViewModel(ConData conData, this._namedDocRepository, this._eventViewModel)
      : super(const AnalyticsState(
          analyticsModel: null,
          events: {},
          loading: true,
          updateAvailable: false,
        )) {
    try {
      _analyticsSubscription = _namedDocRepository.getAnalyticsStream(conData).listen((analyticsModel) {
        _latestAnalyticsModel = analyticsModel;
        emit(state.copyWith(updateAvailable: true));
      });

      _latestEventState = _eventViewModel.state;
      _eventSubscription = _eventViewModel.stream.listen((eventModel) {
        _latestEventState = eventModel;
        emit(state.copyWith(updateAvailable: true));
      });
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace);
    }
  }

  @override
  Future<void> close() async {
    _analyticsSubscription?.cancel();
    _eventSubscription?.cancel();
    return super.close();
  }

  void refreshView() {
    final analyticsModel = _latestAnalyticsModel;
    final evenState = _latestEventState;

    final events = <int, List<EventModel>>{};
    if (analyticsModel != null && evenState != null) {
      for (final event in evenState) {
        final favorites = analyticsModel.currentEventFavorites?[event.id] ?? 0;
        if (events.containsKey(favorites)) {
          events[favorites]!.add(event);
        } else {
          events[favorites] = [event];
        }
      }
    }

    emit(AnalyticsState(
      analyticsModel: analyticsModel,
      events: events,
      loading: false,
      updateAvailable: false,
    ));
  }
}
