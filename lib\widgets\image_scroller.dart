import 'dart:math';

import 'package:async/async.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_network_image_platform_interface/cached_network_image_platform_interface.dart';
import 'package:flutter/material.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/shimmer_square.dart';
import 'package:visibility_detector/visibility_detector.dart';

class ImageScroller extends StatefulWidget {
  final List<ImageModel> images;

  const ImageScroller({
    super.key,
    required this.images,
  });

  @override
  State<ImageScroller> createState() => _ImageScrollerState();
}

class _ImageScrollerState extends State<ImageScroller> {
  static const _scrollDuration = Duration(seconds: 10);
  static const _scrollSpeed = 200;
  static const double _imageSize = 150;
  static const double _imagePadding = AppTheme.widgetPaddingVerySmall;

  final ScrollController _scrollController = ScrollController();
  late final RestartableTimer _timer;

  bool _isVisible = false;
  bool _isTimerReady = true;

  @override
  void initState() {
    _timer = RestartableTimer(_scrollDuration, () {
      _isTimerReady = true;
      _tryAnimate();
    });
    _timer.cancel();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _tryAnimate();
    });
    super.initState();
  }

  @override
  void dispose() {
    _timer.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  void _tryAnimate() {
    if (_isVisible && _isTimerReady) {
      _isTimerReady = false;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollController.animateTo(
          _scrollController.offset + _scrollSpeed,
          duration: _scrollDuration,
          curve: Curves.linear,
        );
        _timer.reset();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final downloadUrls = widget.images.map((e) => e.downloadUrl).whereType<String>().toList();
    if (downloadUrls.isEmpty) {
      return const SizedBox.shrink();
    }

    final seed = downloadUrls.hashCode;
    final random = Random(seed);
    downloadUrls.shuffle(random);

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          maxWidth: AppTheme.maxContentWidth,
          maxHeight: _imageSize,
        ),
        child: LayoutBuilder(
          builder: (context, constraints) {
            if (constraints.maxWidth >= (_imageSize + _imagePadding * 2) * downloadUrls.length) {
              return Center(
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  scrollDirection: Axis.horizontal,
                  itemCount: downloadUrls.length,
                  itemBuilder: (context, index) => _buildImageWidget(downloadUrls[index]),
                ),
              );
            }

            _tryAnimate();
            return VisibilityDetector(
              key: ValueKey(seed),
              onVisibilityChanged: (info) {
                final nowVisible = info.visibleFraction > 0;

                // Became visible
                if (nowVisible && !_isVisible) {
                  _isVisible = info.visibleFraction > 0;
                  _tryAnimate();
                } else {
                  _isVisible = info.visibleFraction > 0;
                }
              },
              child: ListView.builder(
                controller: _scrollController,
                physics: const NeverScrollableScrollPhysics(),
                scrollDirection: Axis.horizontal,
                itemBuilder: (context, index) {
                  final listIndex = index % downloadUrls.length;
                  return _buildImageWidget(downloadUrls[listIndex]);
                },
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildImageWidget(String url) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: _imagePadding),
      child: SizedBox(
        width: _imageSize,
        height: _imageSize,
        child: ContentArea(
          padding: EdgeInsets.zero,
          child: CachedNetworkImage(
            imageRenderMethodForWeb: ImageRenderMethodForWeb.HttpGet,
            fit: BoxFit.cover,
            imageUrl: url,
            placeholder: (context, url) => const ShimmerSquare(),
            errorWidget: (context, url, error) => const SizedBox.shrink(),
          ),
        ),
      ),
    );
  }
}
