// ignore_for_file: invalid_annotation_target

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/social_media.dart';
import 'package:venvi/constants/tier.dart';
import 'package:venvi/json_converters/timestamp_converter.dart';
import 'package:venvi/models/base_con.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/models/location_model.dart';

part 'con_model.freezed.dart';
part 'con_model.g.dart';

@freezed
sealed class ConModel with _$ConModel implements BaseCon {
  const factory ConModel({
    String? id,
    String? orgId,
    @JsonKey(unknownEnumValue: JsonKey.nullForUndefinedEnumValue) Tier? tier,
    ImageModel? logo,
    ImageModel? banner,
    String? name,
    String? color,
    LocationModel? location,
    @TimestampConverter() Timestamp? startDate,
    @TimestampConverter() Timestamp? endDate,
    bool? isPublished,
    String? homepageUrl,
    String? registrationUrl,
    Map<SocialMedia, String>? socialMediaLinks,
  }) = _ConModel;

  const ConModel._();

  factory ConModel.fromJson(Map<String, dynamic> json) => _$ConModelFromJson(json);

  @override
  String? get logoUrl => logo?.downloadUrl;
}
