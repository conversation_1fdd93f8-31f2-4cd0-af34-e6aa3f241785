<!DOCTYPE html><html><head>
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Convention attendee app">

  <meta name="google-signin-client_id" content="42378229751-b8enp7e3s0vl53h20u8nbcsq1upvk29b.apps.googleusercontent.com">
  <meta name="apple-itunes-app" content="app-id=6574389209">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="venvi">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png">

  <title>Venvi</title>
  <link rel="manifest" href="manifest.json">
  
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">

  
  
  <!-- cropperjs -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.2/cropper.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.2/cropper.min.js"></script>
  <style id="splash-screen-style">
    html {
      height: 100%
    }

    body {
      margin: 0;
      min-height: 100%;
      background-color: #000000;
          background-size: 100% 100%;
    }

    .center {
      margin: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      -ms-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
    }

    .contain {
      display:block;
      width:100%; height:100%;
      object-fit: contain;
    }

    .stretch {
      display:block;
      width:100%; height:100%;
    }

    .cover {
      display:block;
      width:100%; height:100%;
      object-fit: cover;
    }

    .bottom {
      position: absolute;
      bottom: 0;
      left: 50%;
      -ms-transform: translate(-50%, 0);
      transform: translate(-50%, 0);
    }

    .bottomLeft {
      position: absolute;
      bottom: 0;
      left: 0;
    }

    .bottomRight {
      position: absolute;
      bottom: 0;
      right: 0;
    }
  </style>
  <script id="splash-screen-script">
    function removeSplashFromWeb() {
      document.getElementById("splash")?.remove();
      document.getElementById("splash-branding")?.remove();
      document.body.style.background = "transparent";
    }
  </script>
</head>
<body>
  <script src="flutter_bootstrap.js" async></script>
</body></html>
