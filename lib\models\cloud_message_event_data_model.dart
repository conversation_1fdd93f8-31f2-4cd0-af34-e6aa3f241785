// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/cloud_message_type.dart';

part 'cloud_message_event_data_model.freezed.dart';
part 'cloud_message_event_data_model.g.dart';

@freezed
sealed class CloudMessageEventDataModel with _$CloudMessageEventDataModel {
  const factory CloudMessageEventDataModel({
    @JsonKey(unknownEnumValue: JsonKey.nullForUndefinedEnumValue) CloudMessageType? messageType,
    String? orgId,
    String? conId,
  }) = _CloudMessageEventDataModel;

  factory CloudMessageEventDataModel.fromJson(Map<String, dynamic> json) => _$CloudMessageEventDataModelFromJson(json);
}
