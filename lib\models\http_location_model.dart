import 'package:freezed_annotation/freezed_annotation.dart';

part 'http_location_model.freezed.dart';
part 'http_location_model.g.dart';

@freezed
sealed class HttpLocationModel with _$HttpLocationModel {
  const factory HttpLocationModel({
    required String name,
    required String address,
    required String latitude,
    required String longitude,
    required String timeZoneId,
  }) = _HttpLocationModel;

  factory HttpLocationModel.fromJson(Map<String, dynamic> json) => _$HttpLocationModelFromJson(json);
}
