import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/announcement_view_model.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/screens/home/<USER>';
import 'package:venvi/screens/home/<USER>';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/announcement_item.dart';
import 'package:venvi/widgets/content_group.dart';
import 'package:venvi/widgets/glass_tile_button.dart';

class AnnouncementView extends StatelessWidget {
  final bool isHighPriority;

  const AnnouncementView({super.key, required this.isHighPriority});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (context) => AnnouncementViewViewModel(
            context.read<ConData>(),
            context.read<UserViewModel>(),
            context.read<AnnouncementViewModel>(),
          ),
      child: BlocBuilder<AnnouncementViewViewModel, AnnouncementViewState>(
        builder: (context, state) {
          final hasUnread = state.unreadAnnouncements?.isNotEmpty == true;
          final hasRead = state.readAnnouncements?.isNotEmpty == true;
          if (isHighPriority) {
            if (!hasUnread) {
              return const SizedBox.shrink();
            }
          } else {
            if (hasUnread || !hasRead) {
              return const SizedBox.shrink();
            }
          }
          return Padding(
            padding: const EdgeInsets.fromLTRB(
              AppTheme.screenPadding,
              0,
              AppTheme.screenPadding,
              AppTheme.widgetPadding,
            ),
            child: ContentGroup(
              title: 'Announcements',
              padHorizontally: false,
              padBottom: false,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (state.unreadAnnouncements?.isNotEmpty == true)
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.unreadAnnouncements!.length,
                      itemBuilder:
                          (context, index) => AnnouncementItem(
                            announcement: state.unreadAnnouncements![index],
                            read: false,
                            onTap:
                                () => context.read<UserViewModel>().markAnnouncementRead(
                                  context.read<ConData>(),
                                  state.unreadAnnouncements![index].id!,
                                ),
                          ),
                    ),
                  if (state.showRead && state.readAnnouncements?.isNotEmpty == true)
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.readAnnouncements!.length,
                      itemBuilder:
                          (context, index) =>
                              AnnouncementItem(announcement: state.readAnnouncements![index], read: true),
                    ),
                  if (state.readAnnouncements?.isNotEmpty == true && state.showRead)
                    const SizedBox(height: AppTheme.widgetPaddingVerySmall),
                  if (state.readAnnouncements?.isNotEmpty == true)
                    GlassTileButton(
                      text: state.showRead ? 'Hide Read' : 'Show Read',
                      icon: state.showRead ? Icons.expand_less : Icons.expand_more,
                      isCompact: true,
                      onTap: () => context.read<AnnouncementViewViewModel>().toggleShowRead(),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
