import 'package:url_launcher/url_launcher.dart';
import 'package:venvi/utils/logger.dart';

class UrlHandler {
  static Future<void> open(String? url, {Function()? onFailed}) async {
    if (url == null) {
      return;
    }
    try {
      final uri = Uri.parse(url);
      if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
        if (onFailed != null) {
          onFailed();
        }
        throw 'Could not launch $url';
      }
    } catch (e, stackTrace) {
      Logger.error(
        exception: e,
        stackTrace: stackTrace,
      );
    }
  }
}
