enum AdContentType {
  textOnly(text: 'Text'),
  image(text: 'Image');

  final String text;

  const AdContentType({required this.text});
}

enum AdLocationType {
  scroller(text: 'Scroller'),
  agenda(text: 'Agenda');

  final String text;

  const AdLocationType({required this.text});
}

enum AdActionType {
  url(text: 'Website'),
  event(text: 'Event');

  final String text;

  const AdActionType({required this.text});
}
