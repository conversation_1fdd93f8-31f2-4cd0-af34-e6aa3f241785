import 'package:flutter/material.dart';
import 'package:venvi/json_converters/color_converter.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/gradient_background.dart';

class DemoApp extends StatelessWidget {
  static const double barElementSize = 16;
  static const double detailElementSize = 10;
  static const double elementPadding = 8;
  static const double detailPadding = 2;

  final Brightness brightness;
  final String? colorHex;

  const DemoApp({super.key, required this.brightness, this.colorHex});

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: AppTheme.createThemeData(colorHex, brightness),
      child: Builder(
        builder:
            (context) => Center(
              child: Container(
                constraints: const BoxConstraints(maxHeight: 255),
                clipBehavior: Clip.hardEdge,
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(AppTheme.borderRadius)),
                child: AspectRatio(
                  aspectRatio: 9 / 19.5,
                  child: GradientBackground(
                    color: colorHex != null ? ColorConverter.stringToColor(colorHex!) : null,
                    child: Padding(
                      padding: const EdgeInsets.all(elementPadding),
                      child: Column(
                        children: [
                          Text(
                            brightness == Brightness.light ? 'Light' : 'Dark',
                            style: TextStyle(
                              fontSize: barElementSize,
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).appBarTheme.foregroundColor,
                            ),
                          ),
                          const SizedBox(height: elementPadding / 2),
                          Expanded(
                            child: ListView(
                              physics: const NeverScrollableScrollPhysics(),
                              children: [
                                _agendaArea(context, '3:00 PM', 'Event A', 'Event B'),
                                const SizedBox(height: elementPadding),
                                _agendaArea(context, '3:30 PM', 'Event C', 'Event D'),
                                const SizedBox(height: elementPadding),
                                _agendaArea(context, '4:00 PM', 'Event E', 'Event F'),
                              ],
                            ),
                          ),
                          const SizedBox(height: elementPadding / 2),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Icon(
                                Icons.home,
                                color: Theme.of(context).bottomNavigationBarTheme.unselectedItemColor,
                                size: barElementSize,
                              ),
                              Icon(
                                Icons.calendar_today,
                                color: Theme.of(context).bottomNavigationBarTheme.selectedItemColor,
                                size: barElementSize,
                              ),
                              Icon(
                                Icons.map,
                                color: Theme.of(context).bottomNavigationBarTheme.unselectedItemColor,
                                size: barElementSize,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
      ),
    );
  }

  Widget _agendaArea(BuildContext context, String time, String event1, String event2) {
    return ContentArea(
      padding: const EdgeInsets.all(elementPadding),
      child: Column(
        children: [
          Text(
            time,
            style: TextStyle(fontSize: detailElementSize, color: Theme.of(context).colorScheme.onPrimaryContainer),
          ),
          const SizedBox(height: detailPadding),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Text(
              event1,
              style: TextStyle(fontSize: detailElementSize, color: Theme.of(context).colorScheme.onPrimaryContainer),
            ),
          ),
          const SizedBox(height: detailPadding),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Text(
              event2,
              style: TextStyle(fontSize: detailElementSize, color: Theme.of(context).colorScheme.onPrimaryContainer),
            ),
          ),
        ],
      ),
    );
  }
}
