import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/analytics_model.dart';
import 'package:venvi/models/event_model.dart';

part 'analytics_state.freezed.dart';

@freezed
sealed class AnalyticsState with _$AnalyticsState {
  const factory AnalyticsState({
    required AnalyticsModel? analyticsModel,
    required Map<int, List<EventModel>> events,
    required bool loading,
    required bool updateAvailable,
  }) = _AnalyticsState;
}
