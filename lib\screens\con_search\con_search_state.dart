import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/org_active_con_model.dart';

part 'con_search_state.freezed.dart';

@freezed
sealed class ConSearchState with _$ConSearchState {
  const factory ConSearchState({
    required List<OrgActiveConModel> conSearchModels,
    required List<OrgActiveConModel> favoriteConModels,
    required List<OrgActiveConModel> authorizedConModels,
    required List<String> favoriteOrgIds,
    required bool isSearching,
  }) = _ConSearchState;
}
