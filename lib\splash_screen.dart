import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/repositories/user_repository.dart';
import 'package:venvi/utils/logger.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) => _goToInitialScreen());
    super.initState();
  }

  Future<void> _goToInitialScreen() async {
    final context = this.context;

    final userId = context.read<AuthRepository>().currentUserId;
    final userModel = await context.read<UserRepository>().attemptCachedUser(userId);
    final lastCon = userModel?.lastCon;

    final segments = lastCon?.split('_');
    final lastConData = ConData.tryCreate(
      orgId: segments != null && segments.isNotEmpty ? segments[0] : null,
      conId: segments != null && segments.length > 1 ? segments[1] : null,
    );
    // If last con not set or could not parse
    if (lastConData == null) {
      if (context.mounted) {
        context.go('/home');
      } else {
        Logger.error(message: 'Splash context is not mounted');
      }
      return;
    }

    // Go to last con
    if (context.mounted) {
      context.go(lastConData.conPath);
    } else {
      Logger.error(message: 'Splash context is not mounted');
    }
  }

  @override
  Widget build(BuildContext context) {
    return const SizedBox.expand();
  }
}
