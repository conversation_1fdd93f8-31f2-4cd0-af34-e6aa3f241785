import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/global_view_models/role_view_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/router.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/accent_label.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/profile_photo_view.dart';

class RoleParticipantsScreen extends StatelessWidget {
  final String? roleId;

  const RoleParticipantsScreen({super.key, required this.roleId});

  @override
  Widget build(BuildContext context) {
    if (roleId == null) {
      return const Scaffold(appBar: GlassAppBar(), body: ContentArea(fillWidth: false, child: Text('Role not found')));
    }

    return Scaffold(
      appBar: GlassAppBar(title: Text(context.watch<RoleViewModel>().getRoleById(roleId)?.name ?? 'Role')),
      body: BlocBuilder<ParticipantViewModel, List<ParticipantModel>?>(
        builder: (context, state) {
          if (state == null) {
            return const Center(child: ContentArea(fillWidth: false, child: Text('No Participants Found')));
          }
          final List<ParticipantModel> spotlightParticipants = [];
          final List<ParticipantModel> regularParticipants = [];
          for (final participant in state) {
            if (participant.spotlightRoles?.contains(roleId) == true) {
              spotlightParticipants.add(participant);
            } else if (participant.allRoles?.contains(roleId) == true) {
              regularParticipants.add(participant);
            }
          }
          return LayoutBuilder(
            builder:
                (context, constraints) => CustomScrollView(
                  slivers: [
                    SliverPadding(
                      padding: const EdgeInsets.fromLTRB(
                        AppTheme.screenPadding,
                        AppTheme.screenPadding,
                        AppTheme.screenPadding,
                        AppTheme.widgetPaddingSmall,
                      ),
                      sliver: SliverGrid(
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: constraints.maxWidth ~/ 200,
                          childAspectRatio: 3 / 5,
                          crossAxisSpacing: AppTheme.widgetPadding,
                          mainAxisSpacing: AppTheme.widgetPadding,
                        ),
                        delegate: SliverChildBuilderDelegate((context, index) {
                          final participant = spotlightParticipants[index];
                          return ContentArea(
                            onTap: () => AppRouter.pushNamedRoute(context, 'profile/${participant.id}'),
                            child: Column(
                              children: [
                                ProfilePhotoView(profile: participant, size: 100),
                                const SizedBox(height: AppTheme.widgetPaddingSmall),
                                const AccentLabel(text: 'Spotlight', icon: Icons.star, isOnContainer: true),
                                const SizedBox(height: AppTheme.widgetPaddingSmall),
                                Text(
                                  participant.displayName ?? '',
                                  style: Theme.of(context).textTheme.titleLarge,
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: AppTheme.widgetPaddingSmall),
                                Expanded(
                                  child: Text(
                                    participant.conHeadline ?? participant.headline ?? '',
                                    style: Theme.of(context).textTheme.bodySmall,
                                    textAlign: TextAlign.center,
                                    overflow: TextOverflow.fade,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }, childCount: spotlightParticipants.length),
                      ),
                    ),
                    SliverPadding(
                      padding: const EdgeInsets.fromLTRB(
                        AppTheme.screenPadding,
                        AppTheme.widgetPaddingSmall,
                        AppTheme.screenPadding,
                        AppTheme.screenPadding,
                      ),
                      sliver: SliverGrid(
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: constraints.maxWidth ~/ 125,
                          childAspectRatio: 0.8,
                          crossAxisSpacing: AppTheme.widgetPadding,
                          mainAxisSpacing: AppTheme.widgetPadding,
                        ),
                        delegate: SliverChildBuilderDelegate((context, index) {
                          final participant = regularParticipants[index];
                          return ContentArea(
                            onTap: () => AppRouter.pushNamedRoute(context, 'profile/${participant.id}'),
                            child: Column(
                              children: [
                                ProfilePhotoView(profile: participant, size: 40),
                                const SizedBox(height: AppTheme.widgetPaddingSmall),
                                Expanded(
                                  child: Text(
                                    participant.displayName ?? '',
                                    style: Theme.of(context).textTheme.titleSmall,
                                    textAlign: TextAlign.center,
                                    overflow: TextOverflow.fade,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }, childCount: regularParticipants.length),
                      ),
                    ),
                  ],
                ),
          );
        },
      ),
    );
  }
}
