import 'dart:math' as math;

import 'package:flutter/material.dart';

class MapEventIndicator extends StatefulWidget {
  final Color? color;
  final double size;
  final double initialDuration;

  const MapEventIndicator({
    super.key,
    this.color,
    this.size = 48.0,
    this.initialDuration = 1.0,
  });

  @override
  State<MapEventIndicator> createState() => _MapEventIndicatorState();
}

class _MapEventIndicatorState extends State<MapEventIndicator> {
  double _percentRemaining = 1.0;

  @override
  void initState() {
    super.initState();
    _percentRemaining = widget.initialDuration;
  }

  void updateDuration(double percentRemaining) {
    setState(() {
      _percentRemaining = percentRemaining;
    });
  }

  @override
  Widget build(BuildContext context) {
    final themeColor = widget.color ?? Theme.of(context).colorScheme.primary;
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: CustomPaint(
        painter: _MapEventIndicatorPainter(
          color: themeColor,
          percentRemaining: _percentRemaining,
        ),
      ),
    );
  }
}

class _MapEventIndicatorPainter extends CustomPainter {
  final Color color;
  final double percentRemaining;

  const _MapEventIndicatorPainter({
    required this.color,
    required this.percentRemaining,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2;

    final backgroundPaint = Paint()
      ..color = color.withAlpha(255)
      ..style = PaintingStyle.stroke
      ..strokeWidth = radius * 0.15;

    final foregroundPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius, backgroundPaint);

    const startAngle = -math.pi / 2;
    final sweepAngle = math.pi * 2 * percentRemaining;
    final progressPath = Path()
      ..moveTo(center.dx, center.dy)
      ..arcTo(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        false,
      )
      ..close();
    if (percentRemaining == 1) {
      canvas.drawCircle(center, radius, foregroundPaint);
    } else {
      canvas.drawPath(progressPath, foregroundPaint);
    }

    canvas.drawCircle(center, radius, backgroundPaint..strokeWidth = 0);
  }

  @override
  bool shouldRepaint(_MapEventIndicatorPainter oldDelegate) => percentRemaining != oldDelegate.percentRemaining;
}
