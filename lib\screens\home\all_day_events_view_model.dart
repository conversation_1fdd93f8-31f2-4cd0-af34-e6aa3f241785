import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/event_type.dart';
import 'package:venvi/constants/time_constants.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/global_view_models/event_view_model.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/models/event_model.dart';
import 'package:venvi/models/user_model.dart';
import 'package:venvi/screens/home/<USER>';
import 'package:venvi/utils/time_utils.dart';

class AllDayEventsViewModel extends Cubit<AllDayEventsState> {
  static const int _minUnexpandedEvents = 3;
  static const int _maxUnexpandedEvents = 8;

  final ConViewModel _conViewModel;

  late final StreamSubscription? _eventsSubscription;
  late final StreamSubscription? _userSubscription;

  List<EventModel>? _allEvents;
  List<String> _favorites = [];
  bool? _showAdultContent;
  bool _expanded = false;

  AllDayEventsViewModel(ConData conData, EventViewModel eventViewModel, UserViewModel userViewModel, this._conViewModel)
    : super(const AllDayEventsState(null, null, false)) {
    _eventsSubscription = eventViewModel.stream.listen((events) {
      _allEvents = events;
      refresh();
    });
    _userSubscription = userViewModel.stream.listen((user) {
      final newFavorites = user.getFavoriteEvents(conData);
      // Checks if anything relevant has changed
      if (_showAdultContent != user.showAdultContent ||
          _favorites.length != newFavorites.length ||
          _favorites.any((element) => !newFavorites.contains(element))) {
        _favorites = newFavorites;
        _showAdultContent = user.showAdultContent;
        refresh();
      }
    });
    _allEvents = eventViewModel.state;
    _favorites = userViewModel.state.getFavoriteEvents(conData);
    _showAdultContent = userViewModel.state.showAdultContent;
    refresh();
  }

  @override
  Future<void> close() {
    _eventsSubscription?.cancel();
    _userSubscription?.cancel();
    return super.close();
  }

  void refresh() {
    final events = _allEvents;
    if (events == null) {
      emit(AllDayEventsState(null, null, false));
      return;
    }

    final List<EventModel> allDayEvents = [];
    for (final event in events) {
      if (_isAllDayEvent(event)) {
        allDayEvents.add(event);
      }
    }

    if (allDayEvents.isEmpty) {
      emit(AllDayEventsState(null, null, false));
      return;
    }

    final potentialPriorityEvents = allDayEvents.toList();

    final List<EventModel> priorityAllDayEvents = [];
    final now = Timestamp.now().millisecondsSinceEpoch;

    // Events ending soon
    final priorityEndingOperationHour = now + TimeConstants.priorityEndingOperationHour.inMilliseconds;
    for (final event in potentialPriorityEvents) {
      if (event.adultOnly != true || _showAdultContent != false) {
        final endTime = event.endTime?.millisecondsSinceEpoch;
        if (endTime != null && now <= endTime && endTime <= priorityEndingOperationHour) {
          priorityAllDayEvents.add(event);
          if (priorityAllDayEvents.length >= _maxUnexpandedEvents) {
            break;
          }
        }
      }
    }

    // Events starting soon
    if (priorityAllDayEvents.length < _maxUnexpandedEvents) {
      potentialPriorityEvents.removeWhere((element) => priorityAllDayEvents.contains(element));

      final priorityUpcomingOperationHour = now + TimeConstants.priorityUpcomingOperationHour.inMilliseconds;
      for (final event in potentialPriorityEvents) {
        if (event.adultOnly != true || _showAdultContent != false) {
          final startTime = event.startTime?.millisecondsSinceEpoch;
          if (startTime != null && now <= startTime && startTime <= priorityUpcomingOperationHour) {
            priorityAllDayEvents.add(event);
            if (priorityAllDayEvents.length >= _maxUnexpandedEvents) {
              break;
            }
          }
        }
      }
    }

    // Spotlight events
    if (priorityAllDayEvents.length < _minUnexpandedEvents) {
      potentialPriorityEvents.removeWhere((element) => priorityAllDayEvents.contains(element));

      for (final event in potentialPriorityEvents) {
        if (event.adultOnly != true || _showAdultContent != false) {
          final startTime = event.startTime?.millisecondsSinceEpoch;
          final endTime = event.endTime?.millisecondsSinceEpoch;
          if (startTime != null && endTime != null && startTime <= now && now < endTime) {
            if (_isSpotlight(event)) {
              priorityAllDayEvents.add(event);
              if (priorityAllDayEvents.length >= _minUnexpandedEvents) {
                break;
              }
            }
          }
        }
      }
    }

    // Adds most recently started events
    if (priorityAllDayEvents.length < _minUnexpandedEvents) {
      potentialPriorityEvents.removeWhere((element) => priorityAllDayEvents.contains(element));

      for (final event in potentialPriorityEvents.reversed) {
        if (event.adultOnly != true || _showAdultContent != false) {
          final startTime = event.startTime?.millisecondsSinceEpoch;
          final endTime = event.endTime?.millisecondsSinceEpoch;
          if (startTime != null && endTime != null && startTime <= now && now < endTime) {
            priorityAllDayEvents.add(event);
            if (priorityAllDayEvents.length >= _minUnexpandedEvents) {
              break;
            }
          }
        }
      }
    }

    emit(state.copyWith(unexpandedEvents: priorityAllDayEvents, expandedEvents: allDayEvents));
  }

  bool _isSpotlight(EventModel event) {
    return event.spotlight == true || event.type == EventType.type1 || _favorites.contains(event.id);
  }

  bool _isAllDayEvent(EventModel event) {
    final startTime = event.startTime?.millisecondsSinceEpoch;
    final endTime = event.endTime?.millisecondsSinceEpoch;

    if (startTime == null || endTime == null) {
      return false;
    }

    final duration = endTime - startTime;
    if (duration < TimeConstants.longRunningEventDuration.inMilliseconds) {
      return false;
    }

    final now = Timestamp.now();

    final currentConTime = TimeUtils.convertToConTimeZone(_conViewModel.state?.location, now);
    if (currentConTime == null) {
      return false;
    }

    final locationModel = _conViewModel.state?.location;
    final todayNoon = TimeUtils.getConDay(locationModel, now, hour: 12);
    if (todayNoon == null) {
      return false;
    }

    final todayDate = TimeUtils.getConDay(locationModel, Timestamp.fromDate(todayNoon));
    final tomorrowDate = TimeUtils.getConDay(locationModel, Timestamp.fromDate(todayNoon.add(const Duration(days: 1))));
    final todayMidnight = todayDate?.millisecondsSinceEpoch;
    final tomorrowMidnight = tomorrowDate?.millisecondsSinceEpoch;
    if (todayMidnight == null || tomorrowMidnight == null) {
      return false;
    }

    if (todayMidnight <= startTime && startTime < tomorrowMidnight) {
      return true;
    }

    // Only shows overlap while overlap is happening
    final showPreviousDayOverlap = currentConTime.hour <= TimeConstants.latestTimeOfDayHourOverlap;
    if (showPreviousDayOverlap && startTime < tomorrowMidnight && todayMidnight < endTime) {
      return true;
    }

    return false;
  }

  void toggleExpanded() {
    _expanded = !_expanded;
    emit(state.copyWith(expanded: _expanded));
  }
}
