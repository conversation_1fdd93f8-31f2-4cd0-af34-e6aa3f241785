import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/tier.dart';
import 'package:venvi/json_converters/timestamp_converter.dart';
import 'package:venvi/models/create_con_location_model.dart';

part 'create_con_model.freezed.dart';

@freezed
sealed class CreateConModel with _$CreateConModel {
  const factory CreateConModel({
    String? orgId,
    String? orgOwnerId,
    required Tier tier,
    required String name,
    required CreateConLocationModel location,
    @TimestampConverter() required Timestamp startDate,
    @TimestampConverter() required Timestamp endDate,
    required String color,
  }) = _CreateConModel;
}
