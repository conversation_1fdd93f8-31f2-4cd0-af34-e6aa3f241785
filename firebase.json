{"hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "headers": [{"source": "/.well-known/assetlinks.json", "headers": [{"key": "Content-Type", "value": "application/json"}]}]}, "flutter": {"platforms": {"android": {"default": {"projectId": "venvi-a83ac", "appId": "1:42378229751:android:2792858b394fbdd48620f2", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "venvi-a83ac", "appId": "1:42378229751:ios:7a4cde92cf797b858620f2", "uploadDebugSymbols": true, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "venvi-a83ac", "configurations": {"android": "1:42378229751:android:2792858b394fbdd48620f2", "ios": "1:42378229751:ios:7a4cde92cf797b858620f2", "web": "1:42378229751:web:647178e554cb516c8620f2"}}}}}}