import 'package:venvi/utils/logger.dart';

enum MetadataField {
  org,
  con,
  namedDocs,
  participants,
  roles,
  permissions,
  events,
  announcements,
  venues,
  ads,
  maps,
  generalContent,
  homeScreenGallery;

  static MetadataField? fromName(String name) {
    try {
      return values.firstWhere((element) => element.name == name);
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace, message: 'Could not find MetadataField with name: $name');
      return null;
    }
  }
}
