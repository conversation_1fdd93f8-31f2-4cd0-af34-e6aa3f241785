import 'dart:async';

import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/image_library_type.dart';
import 'package:venvi/constants/metadata_field.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/metadata_listener_view_model.dart';
import 'package:venvi/global_view_models/metadata_view_model.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/repositories/image_library_repository.dart';

class HomeScreenGalleryViewModel extends MetadataListenerViewModel<ImageModel, List<ImageModel>?> {
  final ConData _conData;
  final ImageLibraryRepository _repository;

  HomeScreenGalleryViewModel(this._conData, this._repository, MetadataViewModel metadataViewModel)
      : super(null, _conData, metadataViewModel, MetadataField.homeScreenGallery);

  @override
  Future<ImageModel?> getData(String id, DataLocation dataLocation) async {
    return await _repository.getImage(_conData, ImageLibraryType.homeScreenGallery, id, dataLocation: dataLocation);
  }

  @override
  void onData(Map<String, ImageModel> data) {
    if (isClosed) {
      return;
    }

    emit(data.values.toList()..shuffle());
  }
}
