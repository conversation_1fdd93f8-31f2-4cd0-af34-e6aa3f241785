import 'package:flutter/material.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/gradient_background.dart';

class ErrorScreen extends StatelessWidget {
  final String message;
  final String? buttonText;
  final void Function()? onTap;

  const ErrorScreen({
    super.key,
    required this.message,
    this.buttonText,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: AppTheme.createThemeData(
        null,
        Theme.of(context).brightness,
      ),
      child: GradientBackground(
        child: Scaffold(
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.screenPadding),
              child: ContentArea(
                fillWidth: false,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      message,
                      textAlign: TextAlign.center,
                    ),
                    if (buttonText != null && onTap != null)
                      Padding(
                        padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                        child: ElevatedButton(
                          onPressed: onTap,
                          child: Text(buttonText!),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
