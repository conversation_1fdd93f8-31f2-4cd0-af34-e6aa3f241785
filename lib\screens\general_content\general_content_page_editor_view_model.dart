import 'package:image_picker/image_picker.dart';
import 'package:uuid/uuid.dart';
import 'package:venvi/constants/image_type.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/general_content_model.dart';
import 'package:venvi/models/general_content_tile_model.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/repositories/general_content_repository.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/screens/general_content/general_content_page_editor_state.dart';
import 'package:venvi/screens/general_content/general_content_tile_state.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class GeneralContentPageEditorViewModel extends EditorScaffoldViewModel<GeneralContentPageEditorState> {
  final String? _generalContentId;
  final ConData _conData;
  final GeneralContentRepository _generalContentRepository;
  final ImageRepository _imageRepository;

  GeneralContentPageEditorViewModel(
      super.initialState, this._generalContentId, this._conData, this._generalContentRepository, this._imageRepository);

  @override
  bool checkChanges(GeneralContentPageEditorState initialState, GeneralContentPageEditorState currentState) {
    return initialState != currentState;
  }

  @override
  Future<GeneralContentPageEditorState> applyChanges(
      GeneralContentPageEditorState initialState, GeneralContentPageEditorState state) async {
    final Map<int, GeneralContentTileModel> tiles = {};
    final List<ImageModel> imageUploadModels = [];
    final List<XFile> imageUploadFiles = [];

    for (int i = 0; i < state.tiles.length; i++) {
      final GeneralContentTileState tileState = state.tiles[i];
      GeneralContentTileModel tile = tileState.model;
      final imageFile = tileState.imageFile;
      // Uploads image if new one exists
      if (imageFile != null) {
        final imageModel = _imageRepository.createImageModel(_conData, ImageType.generalContentTiles);
        tile = tile.copyWith(image: imageModel);
        imageUploadModels.add(imageModel);
        imageUploadFiles.add(imageFile);
      }
      tiles[i] = tile;
    }

    final tileImageIds = tiles.values.map((tile) => tile.image?.id).whereType<String>().toList();

    final primaryImageModel = state.primaryImageOverrideFile != null
        ? _imageRepository.createImageModel(_conData, ImageType.generalContentPrimary)
        : state.primaryImage;

    final generalContentModel = GeneralContentModel(
      id: _generalContentId,
      orderIndex: state.orderIndex,
      title: state.title,
      primaryImage: primaryImageModel,
      tileImageIds: tileImageIds,
      tiles: tiles,
    );

    final List<String> error = [];
    if (_generalContentId == null) {
      final id = await _generalContentRepository.createGeneralContent(_conData, generalContentModel);
      if (id == null) {
        error.add('Failed to create content page');
      }
    } else {
      final success =
          await _generalContentRepository.updateGeneralContent(_conData, _generalContentId, generalContentModel);
      if (!success) {
        error.add('Failed to update content page');
      }
    }

    if (error.isNotEmpty) {
      throw EditorScaffoldException(error);
    }

    final List<Future<bool>> imageUploadFutures = [];
    if (primaryImageModel != null && state.primaryImageOverrideFile != null) {
      imageUploadFutures.add(_imageRepository.uploadImage(primaryImageModel, state.primaryImageOverrideFile!));
    }
    for (int i = 0; i < imageUploadModels.length; i++) {
      imageUploadFutures.add(_imageRepository.uploadImage(imageUploadModels[i], imageUploadFiles[i]));
    }

    final imageUploadResults = await Future.wait(imageUploadFutures);
    if (imageUploadResults.contains(false)) {
      throw const EditorScaffoldException(['Failed to upload images']);
    }

    return state;
  }

  void moveTile(int oldIndex, int newIndex) {
    if (newIndex < 0 || newIndex >= state.tiles.length) {
      return;
    }
    final tile = state.tiles[oldIndex];
    final newTiles = List<GeneralContentTileState>.from(state.tiles);
    newTiles.removeAt(oldIndex);
    newTiles.insert(newIndex, tile);
    emit(state.copyWith(tiles: newTiles));
  }

  void updateTitle(String? title) {
    title = title?.trim();
    emit(state.copyWith(title: title));
  }

  void updatePrimaryImage(XFile? imageFile) {
    emit(state.copyWith(primaryImageOverrideFile: imageFile));
  }

  void updateTile(int index, GeneralContentTileState tileState) {
    final newTiles = List<GeneralContentTileState>.from(state.tiles);
    newTiles[index] = tileState;
    emit(state.copyWith(tiles: newTiles));
  }

  void addTile() {
    final newTile = GeneralContentTileState(key: const Uuid().v4(), model: const GeneralContentTileModel());
    emit(state.copyWith(tiles: [...state.tiles, newTile]));
  }

  void removeTile(int index) {
    final newTiles = List<GeneralContentTileState>.from(state.tiles);
    newTiles.removeAt(index);
    emit(state.copyWith(tiles: newTiles));
  }

  Future<bool> deletePage(ConData conData, GeneralContentRepository generalContentRepository) async {
    final generalContentId = _generalContentId;
    if (generalContentId == null) {
      return true;
    } else {
      return await generalContentRepository.deleteGeneralContent(conData, generalContentId);
    }
  }
}
