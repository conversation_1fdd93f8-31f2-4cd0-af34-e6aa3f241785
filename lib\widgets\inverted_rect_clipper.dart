import 'package:flutter/material.dart';
import 'package:venvi/theme.dart';

class InvertedRectClipper extends CustomClipper<Path> {
  final Rect rect;
  final double borderRadius;

  InvertedRectClipper({required this.rect, this.borderRadius = AppTheme.borderRadius});

  @override
  Path getClip(Size size) {
    return Path.combine(
      PathOperation.difference,
      Path()..addRRect(RRect.fromRectAndRadius(rect, Radius.circular(borderRadius))),
      Path()..addRect(rect),
    );
  }

  @override
  bool shouldReclip(InvertedRectClipper oldClipper) => oldClipper.rect != rect;
}
