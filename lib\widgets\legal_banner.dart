import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:venvi/utils/url_handler.dart';
import 'package:venvi/widgets/content_area.dart';

class LegalBanner extends StatelessWidget {
  const LegalBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return ContentArea(
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          style: TextStyle(color: Theme.of(context).colorScheme.onPrimaryContainer),
          children: [
            const TextSpan(text: 'By using this ${kIsWeb ? 'site' : 'app'}, you agree to our '),
            TextSpan(
              text: 'Privacy Policy',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                decoration: TextDecoration.underline,
              ),
              recognizer: TapGestureRecognizer()..onTap = () => UrlHandler.open('https://privacy.venvi.app'),
            ),
            const TextSpan(text: ' and '),
            TextSpan(
              text: 'Terms of Service',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                decoration: TextDecoration.underline,
              ),
              recognizer: TapGestureRecognizer()..onTap = () => UrlHandler.open('https://terms.venvi.app'),
            ),
            const TextSpan(text: '.'),
          ],
        ),
      ),
    );
  }
}
