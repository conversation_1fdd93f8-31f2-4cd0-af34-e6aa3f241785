import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/role_view_model.dart';
import 'package:venvi/models/role_model.dart';
import 'package:venvi/repositories/role_repository.dart';
import 'package:venvi/screens/roles_and_participants/roles_reorder_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/editor_scaffold.dart';

class RolesReorderScreen extends StatelessWidget {
  const RolesReorderScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return EditorScaffold<RolesReorderViewModel, List<RoleModel>, RoleViewModel, List<RoleModel>?>(
      externalCubit: context.read<RoleViewModel>(),
      buildState: (externalCubitState) => externalCubitState ?? [],
      initViewModel: (state) => RolesReorderViewModel(
        List<RoleModel>.from(state),
        context.read<ConData>(),
        context.read<RoleRepository>(),
      ),
      title: const Text('Reorder Roles'),
      successMessage: 'Roles Order Updated',
      buildContent: (context, viewModel, state) => ListView.separated(
        padding: const EdgeInsets.all(AppTheme.screenPadding),
        itemCount: state.length,
        separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
        itemBuilder: (context, index) {
          final model = state[index];
          return ContentArea(
            key: ValueKey('${context.read<ConData>().conWidgetKey}-${model.id}'),
            child: Row(
              children: [
                Text(
                  model.name ?? '',
                  style: Theme.of(context).listTileTheme.titleTextStyle,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.arrow_upward),
                  onPressed: index > 0 ? () => viewModel.moveTile(index, index - 1) : null,
                ),
                IconButton(
                  icon: const Icon(Icons.arrow_downward),
                  onPressed: index < state.length - 1 ? () => viewModel.moveTile(index, index + 1) : null,
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
