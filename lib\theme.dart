import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:venvi/json_converters/color_converter.dart';

class AppTheme {
  static const double screenPadding = 16;
  static const double widgetPadding = 16;
  static const double widgetPaddingVeryLarge = widgetPadding * 4;
  static const double widgetPaddingLarge = widgetPadding * 2;
  static const double widgetPaddingSmall = widgetPadding / 2;
  static const double widgetPaddingVerySmall = widgetPadding / 4;
  static const double desktopWidth = 1000;
  static const double maxContentWidth = 800;
  static const double largeImageWidth = maxContentWidth / 3;
  static const double borderRadius = 12;
  static const double gridCornerRadius = 4;

  static const double dialogBlurSigma = 4;

  static const double subtleSurfaceOpacity = 0.15;
  static const double subtleWidgetOpacity = 0.5;
  static const double subtitleTextOpacity = 0.65;
  static const double unselectedOpacity = 0.35;
  static const double coloredContainerOpacity = 0.85;
  static const double subtleColoredContainerOpacity = 0.5;
  static const double hintOpacity = 0.35;
  static const double _unselectedBottomNavOpacity = 0.35;
  static const double _overlayColor = 0.1;

  static const String defaultColorHex = '#2A3439';
  static final Color lightForegroundColor = Colors.white.withValues(alpha: 0.9);
  static final Color darkForegroundColor = Colors.black.withValues(alpha: 0.8);

  static const String _darkBackgroundColorHex = '#121212';

  static const double _foregroundTolerance = 0.4;

  static ThemeData createThemeData(String? colorHex, Brightness deviceBrightness) {
    final brandColor = ColorConverter.stringToColor(colorHex ?? defaultColorHex);
    final darkBackgroundColor = ColorConverter.stringToColor(_darkBackgroundColorHex);

    // Background mainly used for calculations
    final background = brandColor;
    final averageGradientBackgroundColor = colorHex != null
        ? _averageGradient(
            deviceBrightness == Brightness.light ? getLightModeGradient(background) : getDarkModeGradient(background),
          )
        : background;
    final averageBackgroundLuminance = averageGradientBackgroundColor.computeLuminance();
    final onBackgroundTopBrightness = colorHex != null
        ? _calculateForegroundBrightness(
            deviceBrightness == Brightness.light ? _calculateLightThemeStartColor(brandColor) : darkBackgroundColor,
          )
        : Brightness.dark;
    final onBackgroundTop = _calculateForegroundColor(onBackgroundTopBrightness);
    final onBackgroundCenter = calculateForegroundColor(averageGradientBackgroundColor);
    final onBackgroundBottomBrightness = colorHex != null
        ? _calculateForegroundBrightness(brandColor)
        : Brightness.dark;
    final onBackgroundBottom = _calculateForegroundColor(onBackgroundBottomBrightness);
    final bottomSystemNavColor = colorHex != null
        ? brandColor
        : (deviceBrightness == Brightness.light
              ? getLightModeGradient(null).colors.last
              : getDarkModeGradient(null).colors.last);

    // Surface depends on the device's brightness
    final surface = deviceBrightness == Brightness.light ? Colors.white : Colors.black;
    final onSurface = calculateForegroundColor(surface);

    // Primary depends on both the device brightness and background color
    late final Color primaryContainer, primary, onPrimaryContainer, onPrimary;
    if (deviceBrightness == Brightness.light) {
      // Light mode
      if (averageBackgroundLuminance < 0.6) {
        // Regular background
        primaryContainer = Colors.white.withValues(alpha: 0.6);
        primary = brandColor.withValues(alpha: 0.75);
        onPrimaryContainer = darkForegroundColor;
        onPrimary = calculateForegroundColor(primary);
      } else {
        // Extremely bright background
        primaryContainer = Colors.grey.shade800.withValues(alpha: 0.6);
        primary = brandColor.withValues(alpha: 0.75);
        onPrimaryContainer = lightForegroundColor;
        onPrimary = calculateForegroundColor(primary);
      }
    } else {
      // Dark mode
      if (averageBackgroundLuminance > 0.015) {
        // Regular background
        primaryContainer = Colors.black.withValues(alpha: 0.6);
        primary = brandColor.withValues(alpha: 0.75);
        onPrimaryContainer = lightForegroundColor;
        onPrimary = calculateForegroundColor(primary);
      } else {
        // Extremely dark background
        primaryContainer = Colors.grey.shade800.withValues(alpha: 0.6);
        primary = brandColor.withValues(alpha: 0.75);
        onPrimaryContainer = lightForegroundColor;
        onPrimary = calculateForegroundColor(primary);
      }
    }

    // Secondary is the accent/brand color
    final onSecondary = brandColor;
    final secondary = _calculateForegroundBrightness(brandColor) == Brightness.light
        ? Colors.grey.shade900
        : Colors.white;

    // Tertiary depends on device brightness
    final tertiaryContainer = deviceBrightness == Brightness.light
        ? Colors.white.withValues(alpha: 0.85)
        : Colors.grey.shade800.withValues(alpha: 0.85);
    final tertiary = deviceBrightness == Brightness.light ? Colors.white : darkBackgroundColor;
    final onTertiary = calculateForegroundColor(tertiary);
    final onTertiaryContainer = onTertiary;

    final colorScheme = ColorScheme(
      brightness: deviceBrightness,
      primary: primary,
      onPrimary: onPrimary,
      primaryContainer: primaryContainer,
      onPrimaryContainer: onPrimaryContainer,
      secondary: secondary,
      onSecondary: onSecondary,
      tertiary: tertiary,
      onTertiary: onTertiary,
      tertiaryContainer: tertiaryContainer,
      onTertiaryContainer: onTertiaryContainer,
      surface: surface,
      onSurface: onSurface,
      error: onTertiary,
      onError: calculateForegroundColor(onTertiary),
    );

    final dividerColor = colorScheme.onPrimaryContainer.withValues(alpha: .2);
    final textStyle = TextStyle(
      color: colorScheme.onPrimaryContainer,
      overflow: TextOverflow.fade,
      decorationThickness: 0,
    );
    final titleTextStyle = TextStyle(color: colorScheme.onPrimaryContainer, overflow: TextOverflow.fade, height: 1.2);
    final inputBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius),
      borderSide: BorderSide(color: Colors.white.withValues(alpha: 0.2), width: 0),
    );
    final buttonStyle = ButtonStyle(
      elevation: WidgetStateProperty.all(0),
      padding: WidgetStateProperty.all(const EdgeInsets.symmetric(horizontal: widgetPaddingLarge)),
      shape: WidgetStateProperty.all(RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadius))),
      foregroundColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.disabled)) {
          return colorScheme.onPrimaryContainer.withValues(alpha: unselectedOpacity);
        }
        return colorScheme.onPrimaryContainer;
      }),
      overlayColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.pressed) ||
            states.contains(WidgetState.hovered) ||
            states.contains(WidgetState.focused)) {
          return colorScheme.onPrimaryContainer.withValues(alpha: _overlayColor);
        }
        return null;
      }),
      textStyle: WidgetStateProperty.all(const TextStyle(fontWeight: FontWeight.w500)),
    );

    return ThemeData(
      canvasColor: Colors.transparent,
      colorScheme: colorScheme,
      scaffoldBackgroundColor: Colors.transparent,
      hoverColor: colorScheme.onPrimaryContainer.withValues(alpha: _overlayColor),
      focusColor: colorScheme.onPrimaryContainer.withValues(alpha: _overlayColor),
      hintColor: colorScheme.onPrimaryContainer.withValues(alpha: hintOpacity),
      unselectedWidgetColor: colorScheme.onPrimaryContainer.withValues(alpha: unselectedOpacity),
      disabledColor: colorScheme.onPrimaryContainer.withValues(alpha: unselectedOpacity),
      dividerColor: dividerColor,
      iconTheme: IconThemeData(color: colorScheme.onPrimaryContainer),
      materialTapTargetSize: MaterialTapTargetSize.padded,
      iconButtonTheme: IconButtonThemeData(
        style: ButtonStyle(
          iconColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.disabled)) {
              return colorScheme.onPrimaryContainer.withValues(alpha: unselectedOpacity);
            }
            return null;
          }),
        ),
      ),
      textTheme: TextTheme(
        displayLarge: textStyle,
        displayMedium: textStyle,
        displaySmall: textStyle,
        headlineLarge: textStyle,
        headlineMedium: textStyle,
        headlineSmall: textStyle,
        titleLarge: titleTextStyle,
        titleMedium: titleTextStyle,
        titleSmall: titleTextStyle,
        bodyLarge: textStyle,
        bodyMedium: textStyle,
        bodySmall: textStyle,
        labelLarge: textStyle,
        labelMedium: textStyle,
        labelSmall: textStyle,
      ),
      appBarTheme: AppBarTheme(
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
        foregroundColor: onBackgroundTop,
        titleTextStyle: TextStyle(fontSize: 20, fontWeight: FontWeight.w500, color: onBackgroundTop),
        actionsIconTheme: IconThemeData(color: onBackgroundTop),
        iconTheme: IconThemeData(color: onBackgroundTop),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarBrightness: onBackgroundTopBrightness,
          statusBarIconBrightness: onBackgroundTopBrightness == Brightness.light ? Brightness.dark : Brightness.light,
          statusBarColor: Colors.transparent,
          systemStatusBarContrastEnforced: false,
          systemNavigationBarIconBrightness: onBackgroundBottomBrightness,
          systemNavigationBarColor: bottomSystemNavColor,
          systemNavigationBarDividerColor: Colors.transparent,
          systemNavigationBarContrastEnforced: false,
        ),
      ),
      actionIconTheme: ActionIconThemeData(backButtonIconBuilder: (context) => const Icon(Icons.arrow_back_ios_new)),
      tabBarTheme: TabBarThemeData(
        indicator: UnderlineTabIndicator(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: const BorderSide(width: 0, color: Colors.transparent),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        overlayColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.pressed)) {
            return colorScheme.onPrimaryContainer.withValues(alpha: unselectedOpacity);
          } else if (states.contains(WidgetState.hovered)) {
            return colorScheme.onPrimaryContainer.withValues(alpha: unselectedOpacity);
          }
          return null;
        }),
        indicatorColor: onBackgroundTop,
        labelColor: onBackgroundTop,
        dividerColor: Colors.transparent,
        unselectedLabelColor: onBackgroundTop.withValues(alpha: unselectedOpacity),
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: Colors.transparent,
        elevation: 0,
        selectedItemColor: onBackgroundBottom,
        type: BottomNavigationBarType.fixed,
        unselectedItemColor: onBackgroundBottom.withValues(alpha: _unselectedBottomNavOpacity),
        showSelectedLabels: true,
        showUnselectedLabels: false,
      ),
      dialogTheme: DialogThemeData(
        backgroundColor: colorScheme.tertiaryContainer,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadius)),
        titleTextStyle: TextStyle(fontSize: 20, fontWeight: FontWeight.w500, color: colorScheme.onTertiaryContainer),
        contentTextStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.w400, color: colorScheme.onTertiaryContainer),
        iconColor: colorScheme.onTertiaryContainer,
      ),
      bottomSheetTheme: BottomSheetThemeData(
        modalBackgroundColor: colorScheme.tertiaryContainer,
        dragHandleColor: colorScheme.onTertiaryContainer,
        showDragHandle: true,
        elevation: 0,
        modalElevation: 0,
        dragHandleSize: const Size(80, 4),
        clipBehavior: Clip.hardEdge,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(AppTheme.widgetPadding)),
        ),
      ),
      progressIndicatorTheme: ProgressIndicatorThemeData(color: onBackgroundCenter),
      dividerTheme: DividerThemeData(thickness: 1, space: 1, color: dividerColor),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.tertiary,
        iconColor: colorScheme.onTertiary,
        prefixIconColor: colorScheme.onTertiary,
        suffixIconColor: colorScheme.onTertiary,
        hintStyle: TextStyle(color: colorScheme.onTertiary.withValues(alpha: hintOpacity)),
        border: inputBorder,
        enabledBorder: inputBorder,
        disabledBorder: inputBorder,
        focusedBorder: inputBorder,
        errorBorder: inputBorder,
        hoverColor: Colors.transparent,
        focusedErrorBorder: inputBorder,
        contentPadding: const EdgeInsets.symmetric(horizontal: widgetPadding),
      ),
      textSelectionTheme: TextSelectionThemeData(
        selectionColor: Colors.grey.withValues(alpha: 0.5),
        cursorColor: Colors.grey,
        selectionHandleColor: Colors.grey,
      ),
      listTileTheme: ListTileThemeData(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadius)),
        iconColor: colorScheme.onPrimaryContainer,
        titleTextStyle: TextStyle(color: colorScheme.onPrimaryContainer, fontWeight: FontWeight.w400, fontSize: 16),
        subtitleTextStyle: TextStyle(color: colorScheme.onPrimaryContainer.withValues(alpha: subtitleTextOpacity)),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppTheme.widgetPaddingLarge,
          vertical: AppTheme.widgetPaddingVerySmall,
        ),
      ),
      chipTheme: ChipThemeData(
        elevation: 0,
        pressElevation: 0,
        checkmarkColor: colorScheme.onPrimary,
        color: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.disabled)) {
            return colorScheme.primary.withValues(alpha: unselectedOpacity);
          }
          return colorScheme.primary;
        }),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(50),
          side: const BorderSide(color: Colors.transparent),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: buttonStyle.copyWith(
          backgroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.disabled)) {
              return colorScheme.secondary.withValues(alpha: unselectedOpacity);
            }
            return colorScheme.secondary;
          }),
          foregroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.disabled)) {
              return colorScheme.onSecondary.withValues(alpha: unselectedOpacity);
            }
            return colorScheme.onSecondary;
          }),
          iconColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.disabled)) {
              return colorScheme.onSecondary.withValues(alpha: unselectedOpacity);
            }
            return colorScheme.onSecondary;
          }),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: buttonStyle.copyWith(
          side: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.disabled)) {
              return BorderSide(color: colorScheme.primary.withValues(alpha: unselectedOpacity));
            }
            return BorderSide(color: colorScheme.primary);
          }),
        ),
      ),
      textButtonTheme: TextButtonThemeData(style: buttonStyle),
      menuButtonTheme: MenuButtonThemeData(style: buttonStyle),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.disabled)) {
            return colorScheme.onPrimaryContainer.withValues(alpha: unselectedOpacity);
          }
          return colorScheme.onPrimaryContainer;
        }),
        trackOutlineColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.disabled)) {
            return colorScheme.onPrimaryContainer.withValues(alpha: unselectedOpacity);
          }
          return colorScheme.onPrimaryContainer;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return Colors.grey.withValues(alpha: 0.5);
          }
          return Colors.transparent;
        }),
      ),
    );
  }

  static Color calculateForegroundColor(Color solidBackgroundColor, {Color? semiTransparentBackground}) {
    final backgroundBrightness = _calculateForegroundBrightness(
      solidBackgroundColor,
      semiTransparentBackground: semiTransparentBackground,
    );
    return _calculateForegroundColor(backgroundBrightness);
  }

  static Brightness _calculateForegroundBrightness(Color solidBackgroundColor, {Color? semiTransparentBackground}) {
    Color backgroundColor = solidBackgroundColor;

    if (semiTransparentBackground != null) {
      backgroundColor =
          Color.lerp(backgroundColor, semiTransparentBackground, semiTransparentBackground.a) ?? backgroundColor;
    }

    return backgroundColor.computeLuminance() < _foregroundTolerance ? Brightness.dark : Brightness.light;
  }

  static Color _calculateForegroundColor(Brightness backgroundBrightness) {
    return backgroundBrightness == Brightness.light ? darkForegroundColor : lightForegroundColor;
  }

  static Gradient getLightModeGradient(Color? color) {
    if (color == null) {
      return getVenviGradient(0.2);
    }
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [_calculateLightThemeStartColor(color), color],
    );
  }

  static Gradient getDarkModeGradient(Color? color) {
    if (color == null) {
      return getVenviGradient(0.5);
    }
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [ColorConverter.stringToColor(_darkBackgroundColorHex), color],
    );
  }

  static Gradient getVenviGradient([double lerpAmount = 0]) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      stops: const [0.15, 0.3, 0.5, 0.7, 0.9],
      colors: [
        Color.lerp(ColorConverter.stringToColor('#651fff'), Colors.black, lerpAmount)!,
        Color.lerp(ColorConverter.stringToColor('#2979ff'), Colors.black, lerpAmount)!,
        Color.lerp(ColorConverter.stringToColor('#00b8d4'), Colors.black, lerpAmount)!,
        Color.lerp(ColorConverter.stringToColor('#00e676'), Colors.black, lerpAmount)!,
        Color.lerp(ColorConverter.stringToColor('#76ff03'), Colors.black, lerpAmount)!,
      ],
    );
  }

  static Color _calculateLightThemeStartColor(Color color) {
    final hsv = HSVColor.fromColor(color);

    final saturation = (hsv.saturation - 0.6).clamp(0.0, 1.0);

    final value = (hsv.value + 0.4).clamp(0.0, 1.0);

    return hsv.withSaturation(saturation).withValue(value).toColor();
  }

  static Color _averageGradient(Gradient gradient) {
    int red = 0;
    int green = 0;
    int blue = 0;

    for (Color color in gradient.colors) {
      red += (255 * color.r).round();
      green += (255 * color.g).round();
      blue += (255 * color.b).round();
    }

    red = (red / gradient.colors.length).round();
    green = (green / gradient.colors.length).round();
    blue = (blue / gradient.colors.length).round();

    return Color.fromRGBO(red, green, blue, 1);
  }
}
