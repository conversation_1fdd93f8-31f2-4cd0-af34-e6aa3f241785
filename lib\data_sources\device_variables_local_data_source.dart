import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:venvi/utils/logger.dart';

class DeviceVariablesLocalDataSource {
  static const _deviceIdKey = 'deviceId';
  static const _analyticsConsentKey = 'analyticsConsent';

  final SharedPreferencesAsync _sharedPreferences;

  DeviceVariablesLocalDataSource(this._sharedPreferences);

  Future<String?> getDeviceId() async {
    try {
      if (await _sharedPreferences.containsKey(_deviceIdKey)) {
        return _sharedPreferences.getString(_deviceIdKey);
      } else {
        final deviceId = const Uuid().v4();
        await _sharedPreferences.setString(_deviceIdKey, deviceId);
        return deviceId;
      }
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace);
      return null;
    }
  }

  Future<bool> getAnalyticsConsent() async {
    try {
      return await _sharedPreferences.getBool(_analyticsConsentKey) ?? false;
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace);
      return false;
    }
  }

  Future<bool> analyticsConsentPrompted() async {
    try {
      return await _sharedPreferences.containsKey(_analyticsConsentKey);
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace);
      return false;
    }
  }

  Future<bool> setAnalyticsConsent(bool consent) async {
    try {
      await _sharedPreferences.setBool(_analyticsConsentKey, consent);
      return true;
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace);
      return false;
    }
  }
}
