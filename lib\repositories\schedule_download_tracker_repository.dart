import 'package:cloud_functions/cloud_functions.dart';
import 'package:venvi/constants/device_platform.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/utils/logger.dart';

class ScheduleDownloadTrackerRepository {
  final FirebaseFunctions _functions;

  ScheduleDownloadTrackerRepository(this._functions);

  Future<bool> registerScheduleDownload(ConData conData) async {
    try {
      final result = await _functions.httpsCallable('registerScheduleDownload').call({
        'orgId': conData.orgId,
        'conId': conData.conId,
        'platform': DevicePlatform.currentPlatform?.name,
      });

      final bool? data = result.data;
      if (data != true) {
        Logger.error(message: 'Register schedule download returned $data: ${conData.toString()}');
        return false;
      }

      return true;
    } catch (e, stackTrace) {
      Logger.error(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to register schedule download: ${conData.toString()}',
      );
      return false;
    }
  }
}
