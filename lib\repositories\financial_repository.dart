import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/models/financial_model.dart';
import 'package:venvi/utils/logger.dart';

class FinancialRepository {
  final FirebaseFirestore _firestore;

  FinancialRepository(this._firestore);

  Stream<FinancialModel?> getFinancialStream(String orgId) {
    final ref = _firestore.collection('orgs').doc(orgId).collection('restricted').doc('financial');
    try {
      return ref.snapshots().map((snapshot) {
        if (snapshot.exists) {
          final data = snapshot.data();
          if (data != null) {
            try {
              return FinancialModel.fromJson(data);
            } catch (e, stackTrace) {
              Logger.error(exception: e, stackTrace: stackTrace);
            }
          }
        }
        return null;
      });
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc stream',
        documentReference: ref,
      );
      return Stream.value(null);
    }
  }

  Future<FinancialModel?> getFinancial(String orgId, {DataLocation? dataLocation}) async {
    final ref = _firestore.collection('orgs').doc(orgId).collection('restricted').doc('financial');
    try {
      final snapshot = await ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));

      if (snapshot.exists) {
        final data = snapshot.data();
        if (data != null) {
          return FinancialModel.fromJson(data);
        }
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        documentReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<bool> sendInvoiceToBillingManager(String orgId, String conId) async {
    // TODO: Implement
    return true;
  }
}
