// ignore_for_file: invalid_annotation_target

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/event_type.dart';
import 'package:venvi/json_converters/timestamp_converter.dart';
import 'package:venvi/models/venue_snippet_model.dart';

part 'row_event_model.freezed.dart';

@freezed
sealed class RowEventModel with _$RowEventModel {
  const factory RowEventModel({
    String? id,
    String? title,
    String? desc,
    @TimestampConverter() Timestamp? startTime,
    @TimestampConverter() Timestamp? endTime,
    String? newVenueKey,
    VenueSnippetModel? existingVenue,
    bool? spotlight,
    bool? adultOnly,
    @JsonKey(unknownEnumValue: JsonKey.nullForUndefinedEnumValue) EventType? type,
    List<String>? tagKeys,
    String? adminId,
  }) = _RowEventModel;
}
