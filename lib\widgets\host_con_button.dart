import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/content_area.dart';

class HostConButton extends StatelessWidget {
  const HostConButton({super.key});

  @override
  Widget build(BuildContext context) {
    return ContentArea(
      onTap: () => context.go('/home/<USER>'),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: Image.asset('assets/icon/icon.png', width: 28, height: 28),
          ),
          const SizedBox(width: AppTheme.widgetPadding),
          Text('Host a con with Venvi', textAlign: TextAlign.center, style: Theme.of(context).textTheme.titleMedium),
        ],
      ),
    );
  }
}
