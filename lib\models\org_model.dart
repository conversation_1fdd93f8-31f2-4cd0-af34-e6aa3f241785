import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/org_active_con_model.dart';

part 'org_model.freezed.dart';
part 'org_model.g.dart';

@freezed
sealed class OrgModel with _$OrgModel {
  const factory OrgModel({
    String? id,
    bool? isSearchable,
    String? activeConId,
    OrgActiveConModel? activeCon,
    bool? multipleConsPublished,
  }) = _OrgModel;

  factory OrgModel.fromJson(Map<String, dynamic> json) => _$OrgModelFromJson(json);
}
