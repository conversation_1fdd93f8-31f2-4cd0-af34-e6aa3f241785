import 'package:flutter/material.dart';

abstract class ColorConverter {
  static Color? tryStringToColor(String hexString) {
    hexString = hexString.replaceAll('#', '');
    if (hexString.length < 6) {
      hexString = hexString.padLeft(6, '0');
    }
    final colorValue = int.tryParse('FF$hexString', radix: 16);
    return colorValue != null ? Color(colorValue) : null;
  }

  static Color stringToColor(String hexString) {
    return tryStringToColor(hexString) ?? Colors.black;
  }

  static String colorToString(Color color) {
    final r = (255 * color.r).toInt().toRadixString(16).padLeft(2, '0');
    final g = (255 * color.g).toInt().toRadixString(16).padLeft(2, '0');
    final b = (255 * color.b).toInt().toRadixString(16).padLeft(2, '0');
    return '#$r$g$b';
  }
}
