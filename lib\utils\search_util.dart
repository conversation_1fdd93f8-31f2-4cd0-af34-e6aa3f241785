abstract class SearchUtil {
  static bool searchList(List<String>? rawTextList, String searchTerm) {
    if (rawTextList == null) {
      return false;
    }
    for (final rawText in rawTextList) {
      if (searchParts(rawText, searchTerm)) {
        return true;
      }
    }
    return false;
  }

  static bool searchParts(String? rawText, String searchTerm) {
    if (rawText == null || rawText.isEmpty) {
      return false;
    }

    List<String> searchWords = searchTerm.toLowerCase().split(' ');
    String lowerRawText = rawText.toLowerCase();

    return searchWords.every((searchWord) => lowerRawText.contains(searchWord));
  }

  static String sanitizeForFirestoreSearch(String text) {
    return text.trim().replaceAll(RegExp(r'[^a-zA-Z0-9]'), '').toLowerCase();
  }

  static String? sanitizeForImportSearch(String? text) {
    if (text == null) {
      return null;
    }

    // Loose rule to see if it can be minimally sanitized
    final looseSanitizedText = text.trim().toLowerCase();
    if (looseSanitizedText.isEmpty) {
      return null;
    }

    // Strict rule to make result more lenient towards user mistakes when checking for matches
    final strictSanitizedText = looseSanitizedText.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '');
    return strictSanitizedText.isNotEmpty ? strictSanitizedText : looseSanitizedText;
  }
}
