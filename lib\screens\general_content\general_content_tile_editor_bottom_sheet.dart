import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/screens/general_content/general_content_tile_editor_view_model.dart';
import 'package:venvi/screens/general_content/general_content_tile_state.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/image_selector.dart';
import 'package:venvi/utils/snack_bars.dart';
import 'package:venvi/widgets/input_field_label.dart';
import 'package:venvi/widgets/surface_input_field.dart';
import 'package:venvi/widgets/url_text_field.dart';

class GeneralContentTileEditorBottomSheet extends HookWidget {
  final GeneralContentTileState tileState;
  final void Function(GeneralContentTileState tileState) onTileUpdated;
  final void Function() onDelete;

  const GeneralContentTileEditorBottomSheet({
    super.key,
    required this.tileState,
    required this.onTileUpdated,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final headlineTextController = useTextEditingController();
    final detailsTextController = useTextEditingController();
    final externalUrlController = useTextEditingController();

    return BlocProvider(
      create: (context) => GeneralContentTileEditorViewModel(tileState),
      child: Builder(
        builder: (context) => BlocConsumer<GeneralContentTileEditorViewModel, GeneralContentTileState>(
          listener: (context, state) => onTileUpdated(state),
          builder: (context, state) {
            if (state.model.headlineText != null && state.model.headlineText != headlineTextController.text) {
              headlineTextController.text = state.model.headlineText ?? '';
            }
            if (state.model.detailsText != null && state.model.detailsText != detailsTextController.text) {
              detailsTextController.text = state.model.detailsText ?? '';
            }
            if (state.model.externalLink != null && state.model.externalLink != externalUrlController.text) {
              externalUrlController.text = state.model.externalLink ?? '';
            }

            return ListView(
              shrinkWrap: true,
              padding: const EdgeInsets.fromLTRB(
                AppTheme.screenPadding,
                AppTheme.widgetPadding,
                AppTheme.screenPadding,
                AppTheme.screenPadding,
              ),
              children: [
                ElevatedButton.icon(
                  icon: const Icon(Icons.add_photo_alternate),
                  label: const Text('Upload Image'),
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.onTertiary,
                    iconColor: Theme.of(context).colorScheme.onTertiary,
                    backgroundColor: Theme.of(context).colorScheme.tertiary,
                  ),
                  onPressed: () async {
                    final file = await ImageSelector().selectImage(context, cropAspectRatio: 1);
                    if (file != null && context.mounted) {
                      context.read<GeneralContentTileEditorViewModel>().setImage(file);
                    }
                    if (context.mounted) {
                      SnackBars.showInfoSnackBar(context, 'Image added');
                    }
                  },
                ),
                const SizedBox(height: AppTheme.widgetPadding),
                const InputFieldLabel(
                  label: 'Headline',
                  isTertiary: true,
                ),
                SurfaceInputField(
                  isTertiary: true,
                  child: TextField(
                    controller: headlineTextController,
                    onChanged: (value) => context.read<GeneralContentTileEditorViewModel>().updateHeadlineText(value),
                    textInputAction: TextInputAction.newline,
                    maxLines: 5,
                    textCapitalization: TextCapitalization.sentences,
                    maxLength: InputConstants.maxGeneralContentHeadlineLength,
                    decoration: const InputDecoration(
                      contentPadding: EdgeInsets.all(AppTheme.widgetPadding),
                      hintText: 'Headline',
                      counterText: '',
                    ),
                  ),
                ),
                const SizedBox(height: AppTheme.widgetPadding),
                const InputFieldLabel(
                  label: 'Details',
                  isTertiary: true,
                ),
                SurfaceInputField(
                  isTertiary: true,
                  child: TextField(
                    controller: detailsTextController,
                    onChanged: (value) => context.read<GeneralContentTileEditorViewModel>().updateDetailsText(value),
                    textInputAction: TextInputAction.newline,
                    maxLines: 15,
                    textCapitalization: TextCapitalization.sentences,
                    maxLength: InputConstants.maxGeneralContentDetailsLength,
                    decoration: const InputDecoration(
                      contentPadding: EdgeInsets.all(AppTheme.widgetPadding),
                      hintText: 'Details',
                      counterText: '',
                    ),
                  ),
                ),
                const SizedBox(height: AppTheme.widgetPadding),
                UrlTextField(
                  controller: externalUrlController,
                  onChanged: (value) => context.read<GeneralContentTileEditorViewModel>().updateExternalLink(value),
                  label: 'External Link',
                  prefix: 'https://',
                  icon: Icons.link,
                  value: state.model.externalLink,
                  textInputAction: TextInputAction.done,
                  isTertiary: true,
                ),
                const SizedBox(height: AppTheme.widgetPadding),
                Center(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.check),
                    label: const Text('Done'),
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Theme.of(context).colorScheme.onTertiary,
                      iconColor: Theme.of(context).colorScheme.onTertiary,
                      backgroundColor: Theme.of(context).colorScheme.tertiary,
                    ),
                    onPressed: () => context.pop(),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
