import 'package:flutter/material.dart';
import 'package:venvi/theme.dart';

class SurfaceInputField extends StatelessWidget {
  final bool isTertiary;
  final Widget child;

  const SurfaceInputField({super.key, this.isTertiary = false, required this.child});

  @override
  Widget build(BuildContext context) {
    final mainTheme = Theme.of(context);
    final colorScheme = mainTheme.colorScheme;

    final surfaceColor = colorScheme.tertiary;
    final onSurfaceColor = colorScheme.onTertiary;

    return Theme(
      data: mainTheme.copyWith(
        colorScheme: colorScheme.copyWith(surface: surfaceColor, onSurface: onSurfaceColor),
        inputDecorationTheme: mainTheme.inputDecorationTheme.copyWith(
          fillColor: surfaceColor,
          iconColor: onSurfaceColor,
          prefixIconColor: onSurfaceColor,
          suffixIconColor: onSurfaceColor,
          hintStyle: TextStyle(color: onSurfaceColor.withValues(alpha: AppTheme.hintOpacity)),
        ),
        textTheme: mainTheme.textTheme.copyWith(
          bodyLarge: mainTheme.textTheme.bodyLarge?.copyWith(color: onSurfaceColor),
          bodyMedium: mainTheme.textTheme.bodyMedium?.copyWith(color: onSurfaceColor),
        ),
      ),
      child: child,
    );
  }
}
