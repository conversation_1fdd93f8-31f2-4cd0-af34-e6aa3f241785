import 'dart:async';

import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/metadata_field.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/metadata_listener_view_model.dart';
import 'package:venvi/global_view_models/metadata_view_model.dart';
import 'package:venvi/models/org_model.dart';
import 'package:venvi/repositories/org_repository.dart';

class OrgViewModel extends MetadataListenerViewModel<OrgModel, OrgModel?> {
  final ConData _conData;
  final OrgRepository _repository;

  OrgViewModel(this._conData, this._repository, MetadataViewModel metadataViewModel)
      : super(null, _conData, metadataViewModel, MetadataField.org);

  @override
  Future<OrgModel?> getData(String id, DataLocation dataLocation) async {
    return await _repository.getOrg(_conData.orgId, dataLocation: dataLocation);
  }

  @override
  void onData(Map<String, OrgModel> data) {
    if (isClosed) {
      return;
    }

    emit(data.values.firstOrNull);
  }
}
