import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/profile_model.dart';

part 'profile_search_state.freezed.dart';

@freezed
sealed class ProfileSearchState with _$ProfileSearchState {
  const factory ProfileSearchState({
    required List<ParticipantModel> participants,
    required List<ProfileModel> profiles,
  }) = _ProfileSearchState;
}
