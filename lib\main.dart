import 'dart:io';

import 'package:app_links/app_links.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/data/latest.dart';
import 'package:universal_html/html.dart' as html;
import 'package:venvi/analytics_consent.dart';
import 'package:venvi/app_link_listener.dart';
import 'package:venvi/data_sources/device_variables_local_data_source.dart';
import 'package:venvi/data_sources/metadata_local_data_source.dart';
import 'package:venvi/data_sources/metadata_remote_data_source.dart';
import 'package:venvi/data_sources/push_token_data_source.dart';
import 'package:venvi/firebase_options.dart';
import 'package:venvi/global_view_models/analytics_consent_view_model.dart';
import 'package:venvi/global_view_models/app_status_view_model.dart';
import 'package:venvi/global_view_models/auth_view_model.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/message_tap_view_model.dart';
import 'package:venvi/models/app_status_model.dart';
import 'package:venvi/repositories/ad_repository.dart';
import 'package:venvi/repositories/announcement_repository.dart';
import 'package:venvi/repositories/app_status_repository.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/repositories/cloud_messaging_repository.dart';
import 'package:venvi/repositories/con_repository.dart';
import 'package:venvi/repositories/create_con_repository.dart';
import 'package:venvi/repositories/device_repository.dart';
import 'package:venvi/repositories/event_repository.dart';
import 'package:venvi/repositories/financial_repository.dart';
import 'package:venvi/repositories/general_content_repository.dart';
import 'package:venvi/repositories/image_library_repository.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/repositories/location_repository.dart';
import 'package:venvi/repositories/map_repository.dart';
import 'package:venvi/repositories/metadata_repository.dart';
import 'package:venvi/repositories/named_doc_repository.dart';
import 'package:venvi/repositories/org_repository.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/repositories/permission_repository.dart';
import 'package:venvi/repositories/profile_repository.dart';
import 'package:venvi/repositories/role_repository.dart';
import 'package:venvi/repositories/schedule_download_tracker_repository.dart';
import 'package:venvi/repositories/user_repository.dart';
import 'package:venvi/repositories/venue_repository.dart';
import 'package:venvi/router.dart';
import 'package:venvi/screens/error/error_screen.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/url_handler.dart';

const String _sentryDsn =
    'https://<EMAIL>/4507975640219648';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  final analyticsConsent = AnalyticsConsent(DeviceVariablesLocalDataSource(SharedPreferencesAsync()));
  await analyticsConsent.initAnalyticsEnabled();

  if (kReleaseMode) {
    await SentryFlutter.init(
      (options) {
        options.dsn = _sentryDsn;
        options.beforeSend = (event, hint) => analyticsConsent.analyticsEnabled ? event : null;
        options.beforeSendTransaction = (transaction) => analyticsConsent.analyticsEnabled ? transaction : null;
        options.beforeBreadcrumb = (breadcrumb, hint) => analyticsConsent.analyticsEnabled ? breadcrumb : null;
      },
      appRunner: () async {
        await analyticsConsent.initFirebaseAnalytics();
        return await _handleMessage(message);
      },
    );
  } else {
    await analyticsConsent.initFirebaseAnalytics();
    await _handleMessage(message);
  }
}

Future<void> _handleMessage(RemoteMessage message) async {
  final firestore = FirebaseFirestore.instance;
  final sharedPreferences = SharedPreferencesAsync();

  return await CloudMessagingRepository(
    AuthRepository(FirebaseAuth.instance),
    MetadataRepository(MetadataRemoteDataSource(firestore), MetadataLocalDataSource(sharedPreferences)),
    NamedDocRepository(firestore),
    UserRepository(firestore),
    DeviceRepository(
      DeviceVariablesLocalDataSource(sharedPreferences),
      PushTokenDataSource(FirebaseMessaging.instance),
      firestore,
    ),
    ScheduleDownloadTrackerRepository(FirebaseFunctions.instance),
  ).handleMessageData(message.data);
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  final analyticsConsent = AnalyticsConsent(DeviceVariablesLocalDataSource(SharedPreferencesAsync()));
  await analyticsConsent.initAnalyticsEnabled();

  if (kReleaseMode) {
    await SentryFlutter.init(
      (options) {
        options.dsn = _sentryDsn;
        options.tracesSampleRate = analyticsConsent.analyticsEnabled ? 0.1 : 0.0;
        options.profilesSampleRate = analyticsConsent.analyticsEnabled ? 0.01 : 0.0;
        options.beforeSend = (event, hint) => analyticsConsent.analyticsEnabled ? event : null;
        options.beforeSendTransaction = (transaction) => analyticsConsent.analyticsEnabled ? transaction : null;
        options.beforeBreadcrumb = (breadcrumb, hint) => analyticsConsent.analyticsEnabled ? breadcrumb : null;
      },
      appRunner: () async {
        await analyticsConsent.initFirebaseAnalytics();
        return await _appRunner(analyticsConsent);
      },
    );
  } else {
    await analyticsConsent.initFirebaseAnalytics();
    await _appRunner(analyticsConsent);
  }
}

Future<void> _appRunner(AnalyticsConsent analyticsConsent) async {
  // Futures
  final packageInfoFuture = PackageInfo.fromPlatform();

  initializeTimeZones();

  final appLinks = kIsWeb ? null : AppLinks();

  await FirebaseAppCheck.instance
      .activate(
        webProvider:
            kDebugMode
                ? ReCaptchaV3Provider('6LdMih0qAAAAAPWytorjd-14wZZTRQA7RYFVS_jR')
                : ReCaptchaEnterpriseProvider('6Lc2khwqAAAAAETDVIncQDI8FGZ_b86W442giGMh'),
        androidProvider: kDebugMode ? AndroidProvider.debug : AndroidProvider.playIntegrity,
        appleProvider: kDebugMode ? AppleProvider.debug : AppleProvider.appAttest,
      )
      .timeout(const Duration(seconds: 10), onTimeout: () => Future.error('Failed to activate app check'));

  if (!kIsWeb) {
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    FirebaseMessaging.onMessage.listen(_firebaseMessagingBackgroundHandler);
  }

  FirebaseFirestore.instance.settings = const Settings(persistenceEnabled: true);

  final packageInfo = await packageInfoFuture.timeout(
    const Duration(seconds: 5),
    onTimeout: () => Future.error('Failed to get package info'),
  );

  if (FirebaseAuth.instance.currentUser == null) {
    await FirebaseAuth.instance.signInAnonymously().timeout(
      const Duration(seconds: 10),
      onTimeout: () => Future.error('Failed to sign in anonymously'),
    );
  }

  runApp(MyApp(analyticsConsent: analyticsConsent, packageInfo: packageInfo, appLinks: appLinks));
}

class MyApp extends StatelessWidget {
  final AnalyticsConsent analyticsConsent;
  final PackageInfo packageInfo;
  final AppLinks? appLinks;

  const MyApp({super.key, required this.analyticsConsent, required this.packageInfo, required this.appLinks});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        //
        // ----- Resources -----
        //
        Provider(create: (context) => FirebaseAuth.instance),
        Provider(create: (context) => FirebaseFirestore.instance),
        Provider(create: (context) => FirebaseFunctions.instance),
        Provider(create: (context) => FirebaseStorage.instance),
        Provider(create: (context) => FirebaseMessaging.instance),
        Provider(create: (context) => SharedPreferencesAsync()),
        //
        // ----- Data sources -----
        //
        Provider(create: (context) => MetadataRemoteDataSource(context.read<FirebaseFirestore>())),
        Provider(create: (context) => MetadataLocalDataSource(context.read<SharedPreferencesAsync>())),
        Provider(create: (context) => DeviceVariablesLocalDataSource(context.read<SharedPreferencesAsync>())),
        Provider(create: (context) => PushTokenDataSource(context.read<FirebaseMessaging>())),
        //
        // ----- Repositories -----
        //
        Provider(
          create:
              (context) => AnalyticsConsentViewModel(analyticsConsent, context.read<DeviceVariablesLocalDataSource>()),
        ),
        Provider(create: (context) => UserRepository(context.read<FirebaseFirestore>())),
        Provider(
          create:
              (context) =>
                  MetadataRepository(context.read<MetadataRemoteDataSource>(), context.read<MetadataLocalDataSource>()),
        ),
        Provider(create: (context) => ImageRepository(context.read<FirebaseStorage>())),
        Provider(
          create:
              (context) => ImageLibraryRepository(context.read<FirebaseFirestore>(), context.read<ImageRepository>()),
        ),
        Provider(create: (context) => AppStatusRepository(context.read<FirebaseFirestore>())),
        Provider(create: (context) => AuthRepository(context.read<FirebaseAuth>())),
        Provider(create: (context) => ProfileRepository(context.read<FirebaseFirestore>())),
        Provider(create: (context) => OrgRepository(context.read<FirebaseFirestore>())),
        Provider(create: (context) => ConRepository(context.read<FirebaseFirestore>())),
        Provider(create: (context) => NamedDocRepository(context.read<FirebaseFirestore>())),
        Provider(create: (context) => PermissionRepository(context.read<FirebaseFirestore>())),
        Provider(create: (context) => FinancialRepository(context.read<FirebaseFirestore>())),
        Provider(create: (context) => ParticipantRepository(context.read<FirebaseFirestore>())),
        Provider(create: (context) => RoleRepository(context.read<FirebaseFirestore>())),
        Provider(create: (context) => EventRepository(context.read<FirebaseFirestore>())),
        Provider(create: (context) => AnnouncementRepository(context.read<FirebaseFirestore>())),
        Provider(create: (context) => VenueRepository(context.read<FirebaseFirestore>())),
        Provider(create: (context) => AdRepository(context.read<FirebaseFirestore>())),
        Provider(create: (context) => MapRepository(context.read<FirebaseFirestore>())),
        Provider(create: (context) => GeneralContentRepository(context.read<FirebaseFirestore>())),
        Provider(create: (context) => ScheduleDownloadTrackerRepository(context.read<FirebaseFunctions>())),
        Provider(create: (context) => LocationRepository(context.read<FirebaseFunctions>())),
        Provider(
          create:
              (context) => DeviceRepository(
                context.read<DeviceVariablesLocalDataSource>(),
                context.read<PushTokenDataSource>(),
                context.read<FirebaseFirestore>(),
              ),
        ),
        Provider(
          create:
              (context) => CloudMessagingRepository(
                context.read<AuthRepository>(),
                context.read<MetadataRepository>(),
                context.read<NamedDocRepository>(),
                context.read<UserRepository>(),
                context.read<DeviceRepository>(),
                context.read<ScheduleDownloadTrackerRepository>(),
              ),
        ),
        Provider(create: (context) => CreateConRepository(context.read<FirebaseFunctions>())),
      ],
      builder:
          (context, child) => MultiBlocProvider(
            providers: [
              BlocProvider(create: (context) => AppStatusViewModel(context.read<AppStatusRepository>())),
              BlocProvider(
                create:
                    (context) => AuthViewModel(
                      context.read<AuthRepository>(),
                      context.read<UserRepository>(),
                      context.read<ProfileRepository>(),
                    ),
              ),
              BlocProvider(
                create:
                    (context) => UserViewModel(
                      context.read<UserRepository>(),
                      context.read<AuthRepository>(),
                      context.read<AuthViewModel>(),
                      context.read<CloudMessagingRepository>(),
                      context.read<DeviceRepository>(),
                    ),
              ),
            ],
            child: MaterialApp.router(
              debugShowCheckedModeBanner: false,
              title: 'Venvi',
              theme: AppTheme.createThemeData(null, Brightness.light),
              darkTheme: AppTheme.createThemeData(null, Brightness.dark),
              routerConfig: AppRouter.router,
              builder:
                  (context, child) => BlocBuilder<AppStatusViewModel, AppStatusModel?>(
                    builder: (context, state) {
                      final installedBuildNumber = int.tryParse(packageInfo.buildNumber) ?? 0;
                      if (!kIsWeb &&
                          state?.minSupportedVersion != null &&
                          state!.minSupportedVersion! > installedBuildNumber) {
                        AppRouter.router.go('/home');
                        return ErrorScreen(
                          message: 'Your app is out of date\nPlease update to the latest version',
                          buttonText: 'Open App Store',
                          onTap: () {
                            if (Platform.isAndroid) {
                              UrlHandler.open('https://play.google.com/store/apps/details?id=app.venvi.venvi');
                            } else if (Platform.isIOS) {
                              UrlHandler.open('https://apps.apple.com/us/app/venvi/id6574389209');
                            }
                          },
                        );
                      } else if (kIsWeb &&
                          state?.latestVersion != null &&
                          state!.latestVersion! > installedBuildNumber) {
                        html.window.location.reload();
                      }
                      if (state?.isActive == false) {
                        AppRouter.router.go('/home');
                        return ErrorScreen(
                          message:
                              state?.statusMessage?.isNotEmpty == true
                                  ? state!.statusMessage!
                                  : 'The service is currently down for maintenance\nPlease try again later',
                        );
                      }

                      AppLinkListener(appLinks);

                      return BlocProvider(
                        create:
                            (context) => MessageTapViewModel(
                              context.read<FirebaseMessaging>(),
                              context.read<CloudMessagingRepository>(),
                            ),
                        child: BlocConsumer<MessageTapViewModel, String?>(
                          listener: (context, state) {
                            if (state != null) {
                              AppRouter.router.go(state);
                              context.read<MessageTapViewModel>().markEventHandled();
                            }
                          },
                          builder: (context, state) => child ?? const SizedBox.shrink(),
                        ),
                      );
                    },
                  ),
            ),
          ),
    );
  }
}
