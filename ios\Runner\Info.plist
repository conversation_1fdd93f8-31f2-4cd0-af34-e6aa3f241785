<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Venvi</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>venvi</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string></string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.42378229751-65459hdk9cgb46q3rjpmgk5f5umig3h7</string>
				</array>
			</dict>
		</array>
		<key>FlutterDeepLinkingEnabled</key>
		<false/>
		<key>GIDClientID</key>
		<string>42378229751-65459hdk9cgb46q3rjpmgk5f5umig3h7.apps.googleusercontent.com</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>FIREBASE_ANALYTICS_COLLECTION_ENABLED</key>
		<false/>
		<key>FirebaseAppDelegateProxyEnabled</key>
		<false/>
		<key>FirebaseAutomaticScreenReportingEnabled</key>
		<false/>
		<key>GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS</key>
		<false/>
		<key>GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED</key>
		<false/>
		<key>GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED</key>
		<false/>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Hosts can upload videos to their cons</string>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>LSMinimumSystemVersion</key>
		<string>12.0</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
	</dict>
</plist>
