import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:visibility_detector/visibility_detector.dart';

class MinuteTrigger extends StatefulWidget {
  final Function()? onTrigger;
  final bool rebuildOnTrigger;
  final Widget child;

  const MinuteTrigger({super.key, this.onTrigger, this.rebuildOnTrigger = false, required this.child});

  @override
  State<MinuteTrigger> createState() => _MinuteTriggerState();
}

class _MinuteTriggerState extends State<MinuteTrigger> {
  late final Key _key;

  late Key _childKey;

  bool _isTimerReady = true;
  bool _isVisible = false;

  @override
  void initState() {
    super.initState();
    _key = ValueKey(const Uuid().v4());
    _childKey = ValueKey(const Uuid().v4());
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: _key,
      onVisibilityChanged: (info) {
        if (!_isVisible && info.visibleFraction > 0) {
          // Became visible
          _isVisible = true;
          _tryTrigger();
        } else if (_isVisible && info.visibleFraction <= 0) {
          // Became invisible
          _isVisible = false;
        }
      },
      child: KeyedSubtree(key: _childKey, child: widget.child),
    );
  }

  Future<void> _tryTrigger() async {
    if (_isTimerReady && _isVisible && mounted) {
      _isTimerReady = false;
      widget.onTrigger?.call();

      if (widget.rebuildOnTrigger) {
        setState(() {
          _childKey = ValueKey(const Uuid().v4());
        });
      }

      final now = DateTime.now();
      final nextMinute = DateTime(now.year, now.month, now.day, now.hour, now.minute).add(const Duration(minutes: 1));
      final duration = nextMinute.difference(now);

      Future.delayed(duration, () {
        _isTimerReady = true;
        _tryTrigger();
      });
    }
  }
}
