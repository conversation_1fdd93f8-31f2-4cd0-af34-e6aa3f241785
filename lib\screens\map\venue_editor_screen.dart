import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/venue_view_model.dart';
import 'package:venvi/models/venue_model.dart';
import 'package:venvi/repositories/venue_repository.dart';
import 'package:venvi/screens/map/venue_editor_tile.dart';
import 'package:venvi/screens/map/venue_editor_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/editor_scaffold.dart';
import 'package:venvi/widgets/glass_tile_button.dart';

class VenueEditorScreen extends HookWidget {
  const VenueEditorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final scrollController = useScrollController();

    return EditorScaffold<VenueEditorViewModel, List<VenueModel>, VenueViewModel, List<VenueModel>?>(
      externalCubit: context.read<VenueViewModel>(),
      buildState: (externalCubitState) => List<VenueModel>.from(externalCubitState ?? []),
      initViewModel: (state) => VenueEditorViewModel(state, context.read<ConData>(), context.read<VenueRepository>()),
      title: const Text('Venue Editor'),
      successMessage: 'Venues Updated Successfully',
      buildContent:
          (context, viewModel, state) => ListView(
            padding: const EdgeInsets.all(AppTheme.screenPadding),
            controller: scrollController,
            children: [
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: state.length,
                separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                itemBuilder:
                    (context, index) => VenueEditorTile(
                      conData: context.read<ConData>(),
                      index: index,
                      model: state[index],
                      viewModel: viewModel,
                    ),
              ),
              const SizedBox(height: AppTheme.widgetPadding),
              ContentArea(
                padding: EdgeInsets.zero,
                child: GlassTileButton(
                  text: 'Add Venue',
                  icon: Icons.add,
                  onTap: () {
                    viewModel.addVenue();
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      scrollController.animateTo(
                        scrollController.position.maxScrollExtent,
                        duration: const Duration(milliseconds: 500),
                        curve: Curves.easeInOut,
                      );
                    });
                  },
                ),
              ),
            ],
          ),
    );
  }
}
