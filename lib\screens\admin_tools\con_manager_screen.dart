import 'package:flex_color_picker/flex_color_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/constants/social_media.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/json_converters/color_converter.dart';
import 'package:venvi/models/con_model.dart';
import 'package:venvi/models/location_model.dart';
import 'package:venvi/repositories/con_repository.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/router.dart';
import 'package:venvi/screen_dialogs/location_search/location_search_screen.dart';
import 'package:venvi/screens/admin_tools/con_manager_state.dart';
import 'package:venvi/screens/admin_tools/con_manager_view_model.dart';
import 'package:venvi/screens/admin_tools/demo_app.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/bottom_sheets.dart';
import 'package:venvi/utils/image_selector.dart';
import 'package:venvi/widgets/content_group.dart';
import 'package:venvi/widgets/editor_scaffold.dart';
import 'package:venvi/widgets/image_updater_view.dart';
import 'package:venvi/widgets/surface_input_field.dart';
import 'package:venvi/widgets/url_text_field.dart';

class ConManagerScreen extends HookWidget {
  const ConManagerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final scrollController = useScrollController();
    final nameController = useTextEditingController();
    final colorHexController = useTextEditingController();
    final homepageUrlController = useTextEditingController();
    final registrationUrlController = useTextEditingController();
    final Map<SocialMedia, TextEditingController> socialMediaControllers = Map.fromEntries(
      SocialMedia.values.map((socialMedia) => MapEntry(socialMedia, useTextEditingController())),
    );

    return EditorScaffold<ConManagerViewModel, ConManagerState, ConViewModel, ConModel?>(
      externalCubit: context.read<ConViewModel>(),
      buildState: (externalCubitState) => ConManagerState(conModel: externalCubitState ?? const ConModel()),
      initViewModel: (state) {
        nameController.text = state.conModel.name ?? '';
        colorHexController.text = state.conModel.color?.substring(1) ?? '';
        homepageUrlController.text = state.conModel.homepageUrl ?? '';
        registrationUrlController.text = state.conModel.registrationUrl ?? '';
        socialMediaControllers.forEach((key, value) {
          value.text = state.conModel.socialMediaLinks?[key] ?? '';
        });

        return ConManagerViewModel(
          state,
          context.read<ConData>(),
          context.read<ConRepository>(),
          context.read<ImageRepository>(),
        );
      },
      title: const Text('Con Manager'),
      successMessage: 'Con Updated',
      buildContent: (context, viewModel, state) => ListView(
        controller: scrollController,
        padding: const EdgeInsets.all(AppTheme.screenPadding),
        children: [
          ContentGroup(
            title: 'Con Name',
            child: SurfaceInputField(
              child: TextField(
                controller: nameController,
                onChanged: (value) => viewModel.setName(value),
                textInputAction: TextInputAction.next,
                textCapitalization: TextCapitalization.words,
                textAlign: TextAlign.center,
                maxLength: InputConstants.maxConNameLength,
                decoration: const InputDecoration(hintText: 'Con Name', counterText: ''),
              ),
            ),
          ),
          const SizedBox(height: AppTheme.widgetPadding),
          ContentGroup(
            title: 'Con Logo',
            child: ImageUpdaterView(
              aspectRatio: null,
              imageUrl: state.conModel.logo?.downloadUrl,
              imageFile: state.logoFile,
              onTap: () async {
                final file = await ImageSelector().selectImage(context, cropAspectRatio: null);
                if (file != null) {
                  viewModel.setLogo(file);
                }
              },
            ),
          ),
          const SizedBox(height: AppTheme.widgetPadding),
          ContentGroup(
            title: 'Banner',
            child: ImageUpdaterView(
              aspectRatio: 2 / 1,
              imageUrl: state.conModel.banner?.downloadUrl,
              imageFile: state.bannerFile,
              onTap: () async {
                final file = await ImageSelector().selectImage(context, cropAspectRatio: 2);
                if (file != null && context.mounted) {
                  viewModel.setBanner(file);
                }
              },
            ),
          ),
          const SizedBox(height: AppTheme.widgetPadding),
          _themeSection(context, viewModel, state.conModel, state.conModel.color, colorHexController),
          const SizedBox(height: AppTheme.widgetPadding),
          ContentGroup(
            title: 'Location',
            padHorizontally: false,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppTheme.widgetPadding),
                child: Text(
                  state.conModel.location?.name ?? 'Location not found',
                  textAlign: TextAlign.center,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.onPrimaryContainer),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppTheme.widgetPadding),
                child: Text(
                  state.conModel.location?.address ?? '',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onPrimaryContainer.withValues(alpha: AppTheme.subtleWidgetOpacity),
                  ),
                ),
              ),
              const SizedBox(height: AppTheme.widgetPaddingSmall),
              Center(
                child: OutlinedButton.icon(
                  label: const Text('Select Location'),
                  icon: const Icon(Icons.place),
                  onPressed: () async {
                    final locationModel = await AppRouter.pushFullScreenDialog<LocationModel>(
                      context,
                      const LocationSearchScreen(),
                    );
                    if (locationModel != null) {
                      viewModel.setLocation(locationModel);
                    }
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.widgetPadding),
          ContentGroup(
            title: 'Website Links',
            children: [
              UrlTextField(
                controller: homepageUrlController,
                onChanged: (value) => viewModel.setHomepageUrl(value),
                label: 'Homepage',
                prefix: 'https://',
                icon: Icons.public,
                value: state.conModel.homepageUrl,
              ),
              const SizedBox(height: AppTheme.widgetPaddingSmall),
              UrlTextField(
                controller: registrationUrlController,
                onChanged: (value) => viewModel.setRegistrationUrl(value),
                label: 'Registration',
                prefix: 'https://',
                icon: Icons.confirmation_num,
                value: state.conModel.registrationUrl,
              ),
            ],
          ),
          const SizedBox(height: AppTheme.widgetPadding),
          ContentGroup(
            title: 'Social Media Links',
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Wrap(
                  alignment: WrapAlignment.center,
                  spacing: AppTheme.widgetPaddingSmall,
                  children: List.generate(SocialMedia.values.length, (index) {
                    final socialMedia = SocialMedia.values[index];
                    return ChoiceChip(
                      label: Icon(socialMedia.icon, color: Theme.of(context).colorScheme.onPrimary),
                      selected: state.conModel.socialMediaLinks?.containsKey(socialMedia) ?? false,
                      onSelected: (value) {
                        if (value) {
                          viewModel.setSocialMediaLink(socialMedia, '');
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            scrollController.animateTo(
                              scrollController.position.maxScrollExtent,
                              duration: const Duration(milliseconds: 500),
                              curve: Curves.easeInOut,
                            );
                          });
                        } else {
                          viewModel.removeSocialMediaLink(socialMedia);
                        }
                      },
                    );
                  }),
                ),
                if (state.conModel.socialMediaLinks?.isNotEmpty == true)
                  Padding(
                    padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                    child: ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.conModel.socialMediaLinks?.length ?? 0,
                      separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                      itemBuilder: (context, index) {
                        final socialMedia = state.conModel.socialMediaLinks!.keys.elementAt(index);
                        final value = state.conModel.socialMediaLinks![socialMedia];
                        return UrlTextField(
                          controller: socialMediaControllers[socialMedia],
                          onChanged: (value) => viewModel.setSocialMediaLink(socialMedia, value),
                          label: socialMedia.text,
                          prefix: socialMedia.url,
                          icon: socialMedia.icon,
                          value: value,
                        );
                      },
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _themeSection(
    BuildContext context,
    ConManagerViewModel viewModel,
    ConModel conModel,
    String? initialColorHex,
    TextEditingController colorHexController,
  ) {
    final demoColorHex = conModel.color ?? AppTheme.defaultColorHex;
    return ContentGroup(
      title: 'Theme',
      additionalTitlePadding: true,
      padHorizontally: false,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(AppTheme.widgetPadding, 0, AppTheme.widgetPadding, AppTheme.widgetPadding),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              DemoApp(colorHex: demoColorHex, brightness: Brightness.light),
              DemoApp(colorHex: demoColorHex, brightness: Brightness.dark),
            ],
          ),
        ),
        Wrap(
          spacing: AppTheme.widgetPadding,
          runSpacing: AppTheme.widgetPadding,
          alignment: WrapAlignment.center,
          runAlignment: WrapAlignment.center,
          crossAxisAlignment: WrapCrossAlignment.center,
          children: [
            SizedBox(
              width: 150,
              child: SurfaceInputField(
                child: TextField(
                  controller: colorHexController,
                  textAlign: TextAlign.center,
                  maxLength: 6,
                  decoration: const InputDecoration(prefixText: '#', hintText: 'FFFFFF', counterText: ''),
                  onChanged: (value) => viewModel.changeColor(value),
                ),
              ),
            ),
            OutlinedButton.icon(
              icon: const Icon(Icons.color_lens),
              label: const Text('Color Picker'),
              onPressed: () => _showColorPicker(
                context,
                hexTextController: colorHexController,
                conManagerViewModel: viewModel,
                colorHex: demoColorHex,
                onChanged: (hexColor) {
                  viewModel.changeColor(hexColor);
                  colorHexController.text = hexColor.substring(1);
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showColorPicker(
    BuildContext context, {
    required TextEditingController hexTextController,
    required ConManagerViewModel conManagerViewModel,
    required String colorHex,
    required void Function(String hexColor) onChanged,
  }) {
    BottomSheets.showBottomSheet(
      context: context,
      darkenBackground: false,
      child: BlocProvider.value(
        value: conManagerViewModel,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 220,
              child: AspectRatio(
                aspectRatio: 1,
                child: BlocBuilder<ConManagerViewModel, ConManagerState>(
                  builder: (context, state) => ColorWheelPicker(
                    color: ColorConverter.stringToColor(colorHex),
                    onChanged: (value) => onChanged(ColorConverter.colorToString(value)),
                    onWheel: (value) {},
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
