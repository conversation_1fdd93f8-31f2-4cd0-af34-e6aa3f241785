// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/ad_enums.dart';

part 'ad_model.freezed.dart';
part 'ad_model.g.dart';

@freezed
sealed class AdModel with _$AdModel {
  const factory AdModel({
    String? id,
    int? orderIndex,
    @JsonKey(unknownEnumValue: JsonKey.nullForUndefinedEnumValue) AdContentType? contentType,
    Map<String, dynamic>? content,
    @JsonKey(unknownEnumValue: JsonKey.nullForUndefinedEnumValue) AdLocationType? locationType,
    String? location,
    @J<PERSON><PERSON>ey(unknownEnumValue: JsonKey.nullForUndefinedEnumValue) AdActionType? actionType,
    String? action,
  }) = _AdModel;

  factory AdModel.fromJson(Map<String, dynamic> json) => _$AdModelFromJson(json);
}
