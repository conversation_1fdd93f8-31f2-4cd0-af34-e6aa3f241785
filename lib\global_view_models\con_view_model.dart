import 'dart:async';

import 'package:timezone/timezone.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/metadata_field.dart';
import 'package:venvi/constants/tier.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/custom_classes/date_fields.dart';
import 'package:venvi/global_view_models/metadata_listener_view_model.dart';
import 'package:venvi/global_view_models/metadata_view_model.dart';
import 'package:venvi/models/con_model.dart';
import 'package:venvi/repositories/con_repository.dart';
import 'package:venvi/utils/logger.dart';
import 'package:venvi/utils/time_utils.dart';

class ConViewModel extends MetadataListenerViewModel<ConModel, ConModel?> {
  final ConData _conData;
  final ConRepository _repository;

  ConViewModel(this._conData, this._repository, MetadataViewModel metadataViewModel)
    : super(null, _conData, metadataViewModel, MetadataField.con);

  @override
  Future<ConModel?> getData(String id, DataLocation dataLocation) async {
    final con = await _repository.getCon(_conData, dataLocation: dataLocation);
    if (con == null) {
      return null;
    } else if (_conData.orgId != con.orgId) {
      Logger.error(message: 'OrgId mismatch for: ${_conData.orgId}/${_conData.conId}');
      return null;
    } else if (_conData.conId != con.id) {
      Logger.error(message: 'ConId mismatch for: ${_conData.orgId}/${_conData.conId}');
      return null;
    } else {
      return con;
    }
  }

  @override
  void onData(Map<String, ConModel> data) {
    if (isClosed) {
      return;
    }

    emit(data.values.firstOrNull);
  }

  bool isPremium() {
    return state?.tier == Tier.premium;
  }

  List<DateFields> getConDays() {
    final locationModel = state?.location;
    final conStartDay = TimeUtils.getConDay(locationModel, state?.startDate, hour: 15);
    final conEndDay = TimeUtils.getConDay(locationModel, state?.endDate, hour: 9);
    if (conStartDay == null || conEndDay == null) {
      return [];
    }

    TZDateTime currentDateTime = conStartDay;
    final List<DateFields> conDays = [];
    // Accounts for daylight savings time
    while (currentDateTime.millisecondsSinceEpoch < conEndDay.millisecondsSinceEpoch) {
      conDays.add(DateFields(year: currentDateTime.year, month: currentDateTime.month, day: currentDateTime.day));
      currentDateTime = currentDateTime.add(const Duration(days: 1));
    }
    return conDays;
  }
}
