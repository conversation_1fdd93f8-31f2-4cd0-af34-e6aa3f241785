import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/user_model.dart';
import 'package:venvi/utils/logger.dart';

class UserRepository {
  final FirebaseFirestore _firestore;

  UserRepository(this._firestore);

  Stream<UserModel?> getUserStream(String userId) {
    final ref = _firestore.collection('users').doc(userId);
    try {
      return ref.snapshots().map((snapshot) {
        if (snapshot.exists) {
          final data = snapshot.data();
          if (data != null) {
            try {
              return UserModel.fromJson(data);
            } catch (e, stackTrace) {
              Logger.error(exception: e, stackTrace: stackTrace);
            }
          }
        }
        return null;
      });
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc stream',
        documentReference: ref,
      );
      return Stream.value(null);
    }
  }

  Future<UserModel?> attemptCachedUser(String? userId) async {
    if (userId == null) {
      return null;
    }
    return await _getUser(userId, dataLocation: DataLocation.cache) ?? await getLiveUser(userId);
  }

  Future<UserModel?> getLiveUser(String? userId) async {
    if (userId == null) {
      return null;
    }
    return await _getUser(userId, dataLocation: DataLocation.server);
  }

  Future<UserModel?> _getUser(String userId, {DataLocation? dataLocation}) async {
    final ref = _firestore.collection('users').doc(userId);
    try {
      final snapshot = await ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));

      if (snapshot.exists) {
        final data = snapshot.data();
        if (data != null) {
          return UserModel.fromJson(data);
        }
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        documentReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<bool> updateUserFromAuth(User? user) async {
    try {
      final userId = user?.uid;
      if (user == null || userId == null || user.isAnonymous) {
        return false;
      }

      final remoteUserModel = await _getUser(userId);
      final Map<String, dynamic> newUserFields = {};

      // Updates from user auth
      if (user.email != null && user.email!.isNotEmpty && remoteUserModel?.email != user.email) {
        newUserFields['email'] = user.email!;
      }

      if (newUserFields.isNotEmpty) {
        return await _updateUserFields(userId, newUserFields);
      }
      return true;
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace);
      return false;
    }
  }

  Future<bool> setLastCon(String? userId, ConData conData) async {
    final userModel = await attemptCachedUser(userId);
    if (userId == null) {
      return false;
    }

    final newLastCon = '${conData.orgId}_${conData.conId}';
    bool needsUpdate = false;

    if (userModel?.lastCon != newLastCon) {
      needsUpdate = true;
    }

    if (userModel?.favoriteOrgs?.toList().contains(conData.orgId) != true) {
      needsUpdate = true;
    }

    if (userModel?.favoriteCons?.toList().contains('${conData.orgId}_${conData.conId}') != true) {
      needsUpdate = true;
    }

    if (needsUpdate) {
      return await _updateUserFields(userId, {
        'lastCon': newLastCon,
        'favoriteOrgs': FieldValue.arrayUnion([conData.orgId]),
        'favoriteCons': FieldValue.arrayUnion(['${conData.orgId}_${conData.conId}']),
      });
    }
    return true;
  }

  Future<bool> updateUser(String userId, UserModel userModel) async {
    final userJson = userModel.toJson();
    userJson.remove('id');
    userJson.remove('email');
    userJson.remove('pushTokens');
    userJson.remove('devices');

    final ref = _firestore.collection('users').doc(userId);
    try {
      await ref.update(userJson);
      return true;
    } catch (e, stackTrace) {
      if (e is FirebaseException && e.code == 'not-found') {
        // New user
        await _firestore.collection('users').doc(userId).set(userJson, SetOptions(merge: true));
        return true;
      } else {
        Logger.firestoreError(
          exception: e,
          stackTrace: stackTrace,
          message: 'Failed to update doc',
          documentReference: ref,
        );
      }
    }
    return false;
  }

  Future<bool> _updateUserFields(String userId, Map<String, dynamic> fields) async {
    final ref = _firestore.collection('users').doc(userId);
    try {
      await ref.update(fields);
      return true;
    } catch (e, stackTrace) {
      if (e is FirebaseException && e.code == 'not-found') {
        // New user
        await _firestore.collection('users').doc(userId).set(fields, SetOptions(merge: true));
        return true;
      } else {
        Logger.firestoreError(
          exception: e,
          stackTrace: stackTrace,
          message: 'Failed to update doc',
          documentReference: ref,
        );
      }
    }
    return false;
  }
}
