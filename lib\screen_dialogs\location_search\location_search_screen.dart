import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/repositories/location_repository.dart';
import 'package:venvi/screen_dialogs/location_search/location_search_state.dart';
import 'package:venvi/screen_dialogs/location_search/location_search_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/search_view.dart';

class LocationSearchScreen extends StatelessWidget {
  const LocationSearchScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LocationSearchViewModel(context.read<LocationRepository>()),
      child: Scaffold(
        appBar: const GlassAppBar(title: Text('Location Search'), isFullScreenDialog: true),
        body: Builder(
          builder: (context) => SearchView(
            autofocus: true,
            onSearchChange: (String searchText) => context.read<LocationSearchViewModel>().updateQuery(searchText),
            child: BlocBuilder<LocationSearchViewModel, LocationSearchState>(
              builder: (context, state) => state.isLoading
                  ? Center(child: const CircularProgressIndicator())
                  : state.locations.isEmpty
                  ? const Center(
                      child: ContentArea(
                        fillWidth: false,
                        child: Text('Start typing to search for a location', textAlign: TextAlign.center),
                      ),
                    )
                  : ListView.separated(
                      padding: const EdgeInsets.all(AppTheme.screenPadding),
                      itemCount: state.locations.length,
                      separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPaddingSmall),
                      itemBuilder: (context, index) => ContentArea(
                        padding: EdgeInsets.zero,
                        child: ListTile(
                          dense: true,
                          visualDensity: VisualDensity.compact,
                          title: Text(state.locations[index].name ?? ''),
                          subtitle: Text(state.locations[index].address ?? ''),
                          onTap: () async {
                            Dialogs.showLoadingDialog(context);
                            final locationModel = await context.read<LocationSearchViewModel>().completeLocationModel(
                              state.locations[index],
                            );
                            if (context.mounted) {
                              Navigator.of(context, rootNavigator: true).pop();
                              if (locationModel != null) {
                                Navigator.of(context).pop(locationModel);
                              } else {
                                Dialogs.showErrorDialog(context, message: 'Failed to load location');
                              }
                            }
                          },
                        ),
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }
}
