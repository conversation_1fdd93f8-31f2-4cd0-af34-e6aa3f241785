import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/venue_model.dart';
import 'package:venvi/utils/logger.dart';

class VenueRepository {
  final FirebaseFirestore _firestore;

  VenueRepository(this._firestore);

  Future<VenueModel?> getVenue(ConData conData, String id, {DataLocation? dataLocation}) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('venues')
        .doc(id);
    try {
      final snapshot = await ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));

      if (snapshot.exists) {
        final data = snapshot.data();
        if (data != null) {
          return VenueModel.fromJson(data);
        }
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        documentReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<bool> updateAllVenues(ConData conData, List<VenueModel> newModels) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('venues');
    try {
      final currentSnapshots = await ref.get();
      final currentIds = currentSnapshots.docs.map((e) => e.id).toList();
      final deletedIds = currentIds.where((id) => !newModels.any((model) => model.id == id)).toList();

      final batch = _firestore.batch();
      for (final id in deletedIds) {
        batch.delete(ref.doc(id));
      }
      for (int i = 0; i < newModels.length; i++) {
        final model = newModels[i];
        batch.set(ref.doc(model.id), model.toJson());
      }
      await batch.commit();
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to update multiple venues',
        collectionReference: ref,
      );
      return false;
    }
  }

  Future<List<VenueModel>?> createNewVenuesByName(ConData conData, List<String> venueNames) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('venues');

    try {
      final batch = _firestore.batch();
      final createdModels = <VenueModel>[];

      for (final venueName in venueNames) {
        final docRef = ref.doc();
        final model = VenueModel(id: docRef.id, name: venueName);
        batch.set(docRef, model.toJson());
        createdModels.add(model);
      }

      await batch.commit();
      return createdModels;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to create new venues',
        collectionReference: ref,
      );
      return null;
    }
  }
}
