import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/permissions.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/profile_model.dart';
import 'package:venvi/models/profile_snippet_model.dart';
import 'package:venvi/repositories/permission_repository.dart';
import 'package:venvi/repositories/profile_repository.dart';

class PermissionsManagerViewModel extends Cubit<Map<Permissions, List<ProfileSnippetModel>>> {
  final String _orgId;
  final PermissionRepository _roleRepository;

  late final StreamSubscription? _permissionsSubscription;

  PermissionsManagerViewModel(
    this._orgId,
    this._roleRepository,
    ParticipantViewModel participantViewModel,
    ProfileRepository profileRepository,
  ) : super(const {}) {
    _permissionsSubscription = _roleRepository.getOrgRolesStream(_orgId).listen((roleModels) async {
      if (roleModels == null) {
        emit(const {});
        return;
      }

      final roleProfiles = <Permissions, List<ProfileSnippetModel>>{};
      for (final roleModel in roleModels) {
        final userId = roleModel.id;
        final permissions = roleModel.permissions;
        if (userId == null || permissions == null || permissions.isEmpty) {
          continue;
        }
        late final ProfileSnippetModel? profileSnippet;
        final participantModel = participantViewModel.getParticipant(userId);
        if (participantModel != null) {
          profileSnippet = _convertFromParticipantModel(participantModel);
        } else {
          final profileModel = await profileRepository.getProfile(userId);
          if (profileModel != null) {
            profileSnippet = _convertFromProfileModel(profileModel);
          } else {
            continue;
          }
        }

        if (profileSnippet == null) {
          continue;
        }

        // For each role user has, add to map
        for (final role in permissions) {
          if (roleProfiles.containsKey(role)) {
            roleProfiles[role]!.add(profileSnippet);
          } else {
            roleProfiles[role] = [profileSnippet];
          }
        }
      }

      emit(roleProfiles);
    });
  }

  @override
  Future<void> close() async {
    _permissionsSubscription?.cancel();
    return super.close();
  }

  Future<bool> addPermission(String userId, Permissions role) async {
    return _roleRepository.addPermission(_orgId, userId, role);
  }

  Future<bool> removePermission(String userId, Permissions role) async {
    return _roleRepository.removePermission(_orgId, userId, role);
  }

  Future<bool> changeOrgOwner(String oldOwnerId, String newOwnerId) async {
    return _roleRepository.changeOrgOwner(_orgId, oldOwnerId, newOwnerId);
  }

  ProfileSnippetModel? _convertFromParticipantModel(ParticipantModel participantModel) {
    if (participantModel.manualEntry == true) {
      return null;
    }
    return ProfileSnippetModel(
      id: participantModel.id,
      username: participantModel.username,
      displayName: participantModel.displayName,
      photoUrl: participantModel.photoUrl,
      headline: participantModel.conHeadline ?? participantModel.headline,
    );
  }

  ProfileSnippetModel? _convertFromProfileModel(ProfileModel profileModel) {
    return ProfileSnippetModel(
      id: profileModel.id,
      username: profileModel.username,
      displayName: profileModel.displayName,
      photoUrl: profileModel.photoUrl,
      headline: profileModel.headline,
    );
  }
}
