import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/image_model.dart';

part 'con_photo_gallery_model.freezed.dart';
part 'con_photo_gallery_model.g.dart';

@freezed
sealed class ConPhotoGalleryModel with _$ConPhotoGalleryModel {
  const factory ConPhotoGalleryModel({Map<String, ImageModel>? photos}) = _ConPhotoGalleryModel;

  factory ConPhotoGalleryModel.fromJson(Map<String, dynamic> json) => _$ConPhotoGalleryModelFromJson(json);
}
