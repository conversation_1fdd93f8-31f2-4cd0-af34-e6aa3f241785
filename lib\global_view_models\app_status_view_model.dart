import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/models/app_status_model.dart';
import 'package:venvi/repositories/app_status_repository.dart';

class AppStatusViewModel extends Cubit<AppStatusModel?> {
  final AppStatusRepository _repository;

  late final StreamSubscription _appStatusSubscription;

  AppStatusViewModel(this._repository) : super(null) {
    _appStatusSubscription = _repository.getAppStatusStream().listen((data) {
      if (data != null) {
        emit(data);
      }
    });
  }

  @override
  Future<void> close() {
    _appStatusSubscription.cancel();
    return super.close();
  }
}
