import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/screens/general_content/general_content_tile_state.dart';

part 'general_content_page_editor_state.freezed.dart';

@freezed
sealed class GeneralContentPageEditorState with _$GeneralContentPageEditorState {
  const factory GeneralContentPageEditorState({
    int? orderIndex,
    String? title,
    ImageModel? primaryImage,
    XFile? primaryImageOverrideFile,
    required List<GeneralContentTileState> tiles,
  }) = _GeneralContentPageEditorState;
}
