import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/announcement_model.dart';

part 'announcement_view_state.freezed.dart';

@freezed
sealed class AnnouncementViewState with _$AnnouncementViewState {
  const factory AnnouncementViewState({
    List<AnnouncementModel>? unreadAnnouncements,
    List<AnnouncementModel>? readAnnouncements,
    required bool showRead,
  }) = _AnnouncementViewState;
}
