import 'dart:async';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/cloud_message_type.dart';
import 'package:venvi/models/cloud_message_announcement_notification_model.dart';
import 'package:venvi/models/cloud_message_event_notification_model.dart';
import 'package:venvi/repositories/cloud_messaging_repository.dart';
import 'package:venvi/utils/logger.dart';

class MessageTapViewModel extends Cubit<String?> {
  final FirebaseMessaging _firebaseMessaging;
  final CloudMessagingRepository _cloudMessagingRepository;

  late final StreamSubscription<RemoteMessage>? _subscription;

  MessageTapViewModel(this._firebaseMessaging, this._cloudMessagingRepository) : super(null) {
    if (!kIsWeb) {
      _firebaseMessaging.getInitialMessage().then(_onRemoteMessage);
      _subscription = FirebaseMessaging.onMessageOpenedApp.listen(_onRemoteMessage);

      _cloudMessagingRepository.initNotifications().then(
        (flutterLocalNotificationsPlugin) {
          if (flutterLocalNotificationsPlugin != null) {
            flutterLocalNotificationsPlugin.getNotificationAppLaunchDetails().then(
              (details) {
                if (details == null || details.didNotificationLaunchApp == false) {
                  return;
                }

                try {
                  final parts = details.notificationResponse?.payload?.split('_');
                  if (parts == null || parts.length != 3) {
                    return;
                  }
                  final orgId = parts[0];
                  final conId = parts[1];
                  final eventId = parts[2];
                  emit('/$orgId/$conId/schedule/event/$eventId');
                } catch (e, stackTrace) {
                  Logger.error(
                    exception: e,
                    stackTrace: stackTrace,
                    message: 'Failed to handle launch local notification response',
                  );
                }
              },
            );
          }
        },
      );
    } else {
      _subscription = null;
    }
  }

  void markEventHandled() {
    emit(null);
  }

  void _onRemoteMessage(RemoteMessage? message) {
    final messageType = message?.data['messageType'];

    if (messageType == CloudMessageType.announcementNotification.name) {
      final dataModel = CloudMessageAnnouncementNotificationModel.fromJson(message!.data);
      final orgId = dataModel.orgId;
      final conId = dataModel.conId;
      if (orgId != null && orgId.isNotEmpty && conId != null && conId.isNotEmpty) {
        emit('/$orgId/$conId');
      }
    } else if (messageType == CloudMessageType.eventNotification.name) {
      final dataModel = CloudMessageEventNotificationModel.fromJson(message!.data);
      final orgId = dataModel.orgId;
      final conId = dataModel.conId;
      final eventId = dataModel.eventId;
      if (orgId != null &&
          orgId.isNotEmpty &&
          conId != null &&
          conId.isNotEmpty &&
          eventId != null &&
          eventId.isNotEmpty) {
        emit('/$orgId/$conId/schedule/event/$eventId');
      }
    }
  }

  @override
  Future<void> close() {
    _subscription?.cancel();
    return super.close();
  }
}
