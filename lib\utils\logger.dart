import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:venvi/constants/data_location.dart';

abstract class Logger {
  static void error({dynamic exception, StackTrace? stackTrace, dynamic message}) {
    if (kReleaseMode) {
      Sentry.captureException(exception, stackTrace: stackTrace, hint: Hint.withMap({'message': message}));
    } else {
      debugPrint('Error: ${message?.toString()}');
      debugPrint('Exception: ${exception?.toString()}');
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  static void firestoreError({
    dynamic exception,
    StackTrace? stackTrace,
    dynamic message,
    DocumentReference? documentReference,
    CollectionReference? collectionReference,
    Query? query,
    DataLocation? dataLocation,
  }) {
    if (exception is FirebaseException && dataLocation == DataLocation.cache && exception.code == 'unavailable') {
      return;
    }

    if (kReleaseMode) {
      Sentry.captureException(
        exception,
        stackTrace: stackTrace,
        withScope: (scope) {
          if (documentReference != null) {
            scope.setContexts('documentReference', documentReference.path);
          }
          if (collectionReference != null) {
            scope.setContexts('collectionReference', collectionReference.path);
          }
          if (query != null) {
            scope.setContexts('query', query.toString());
          }
          if (dataLocation != null) {
            scope.setContexts('dataLocation', dataLocation.name);
          }
        },
        hint: Hint.withMap({'message': message}),
      );
    } else {
      if (message != null) {
        debugPrint('Error: ${message.toString()}');
      }
      if (documentReference != null) {
        debugPrint('DocumentReference: ${documentReference.path}');
      }
      if (collectionReference != null) {
        debugPrint('CollectionReference: ${collectionReference.path}');
      }
      if (query != null) {
        debugPrint('Query: ${query.toString()}');
      }
      if (dataLocation != null) {
        debugPrint('DataLocation: ${dataLocation.name}');
      }
      if (exception != null) {
        debugPrint('Exception: ${exception.toString()}');
      }
      if (stackTrace != null) {
        debugPrintStack(stackTrace: stackTrace);
      }
    }
  }
}
