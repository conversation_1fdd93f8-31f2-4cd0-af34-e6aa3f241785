import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/data/latest.dart';
import 'package:timezone/timezone.dart';
import 'package:venvi/constants/cloud_message_type.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/constants/metadata_field.dart';
import 'package:venvi/constants/named_doc.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/cloud_message_con_start_data_model.dart';
import 'package:venvi/models/cloud_message_event_data_model.dart';
import 'package:venvi/models/cloud_message_user_data_model.dart';
import 'package:venvi/models/metadata_model.dart';
import 'package:venvi/models/reminder_notification_model.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/repositories/device_repository.dart';
import 'package:venvi/repositories/metadata_repository.dart';
import 'package:venvi/repositories/named_doc_repository.dart';
import 'package:venvi/repositories/schedule_download_tracker_repository.dart';
import 'package:venvi/repositories/user_repository.dart';
import 'package:venvi/router.dart';
import 'package:venvi/utils/logger.dart';

class CloudMessagingRepository {
  static const String _favoriteTitle = 'Favorite event starts in 15 minutes';
  static const String _spotlightTitle = 'Spotlight event starts in 20 minutes';
  static const int _favoriteReminderMilliseconds = 15 * 60 * 1000;
  static const int _spotlightReminderMilliseconds = 20 * 60 * 1000;
  static const int _millisecondsInDay = 24 * 60 * 60 * 1000;
  static const int _millisecondsBeforeCon = _millisecondsInDay;
  static const int _maxConMilliseconds = (InputConstants.maxConDays + 1) * _millisecondsInDay;

  final AuthRepository _authRepository;
  final MetadataRepository _metadataRepository;
  final NamedDocRepository _namedDocRepository;
  final UserRepository _userRepository;
  final DeviceRepository _deviceRepository;
  final ScheduleDownloadTrackerRepository _scheduleDownloadTrackerRepository;

  CloudMessagingRepository(
    this._authRepository,
    this._metadataRepository,
    this._namedDocRepository,
    this._userRepository,
    this._deviceRepository,
    this._scheduleDownloadTrackerRepository,
  );

  Future<void> handleMessageData(Map<String, dynamic> data) async {
    final messageType = data['messageType'];
    if (messageType == CloudMessageType.conStartData.name) {
      return await _handleConStartData(CloudMessageConStartDataModel.fromJson(data));
    } else if (messageType == CloudMessageType.eventData.name) {
      return await _handleEventData(CloudMessageEventDataModel.fromJson(data));
    } else if (messageType == CloudMessageType.userData.name) {
      return await _handleUserData(CloudMessageUserDataModel.fromJson(data));
    }
  }

  Future<void> _handleConStartData(CloudMessageConStartDataModel cloudMessageConStartModel) async {
    try {
      final conData = ConData.tryCreate(orgId: cloudMessageConStartModel.orgId, conId: cloudMessageConStartModel.conId);
      if (conData == null) {
        return;
      }

      // Live refreshes summary from server
      final summaryModel = await _namedDocRepository.getSummary(conData, dataLocation: DataLocation.server);
      if (summaryModel == null) {
        return;
      }

      // Updates local metadata timestamp for summary
      final summaryTimestamp = summaryModel.metadataTimestamp;
      if (summaryTimestamp != null) {
        MetadataModel? localMetadata = await _metadataRepository.getLocalMetadata(conData);
        if (localMetadata == null) {
          _scheduleDownloadTrackerRepository.registerScheduleDownload(conData);
          localMetadata = {};
        }
        localMetadata[MetadataField.namedDocs]?[NamedDoc.summary.name] = summaryTimestamp;
        await _metadataRepository.saveLocalMetadata(conData, localMetadata);
      }

      await scheduleNotifications();
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace, message: 'Cloud messaging con start data failed');
    }
  }

  Future<void> _handleEventData(CloudMessageEventDataModel cloudMessageEventDataModel) async {
    try {
      final conData = ConData.tryCreate(
        orgId: cloudMessageEventDataModel.orgId,
        conId: cloudMessageEventDataModel.conId,
      );
      if (conData == null) {
        return;
      }

      // Live refreshes summary from server
      final summaryModel = await _namedDocRepository.getSummary(conData, dataLocation: DataLocation.server);
      if (summaryModel == null) {
        return;
      }

      // Updates local metadata timestamp for summary
      final summaryTimestamp = summaryModel.metadataTimestamp;
      if (summaryTimestamp != null) {
        MetadataModel? localMetadata = await _metadataRepository.getLocalMetadata(conData);
        if (localMetadata == null) {
          _scheduleDownloadTrackerRepository.registerScheduleDownload(conData);
          localMetadata = {};
        }
        localMetadata[MetadataField.namedDocs]?[NamedDoc.summary.name] = summaryTimestamp;
        await _metadataRepository.saveLocalMetadata(conData, localMetadata);
      }

      await scheduleNotifications();
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace, message: 'Cloud messaging event data failed');
    }
  }

  Future<void> _handleUserData(CloudMessageUserDataModel cloudMessageUserModel) async {
    try {
      final userId = _authRepository.currentUser?.uid;

      // Live refreshes user from server
      await _userRepository.getLiveUser(userId);

      await scheduleNotifications();
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace, message: 'Cloud messaging user data failed');
    }
  }

  Future<FlutterLocalNotificationsPlugin?> initNotifications() async {
    if (kIsWeb) {
      return null;
    }
    final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    await flutterLocalNotificationsPlugin.initialize(
      InitializationSettings(
        android: AndroidInitializationSettings('@drawable/ic_notification'),
        iOS: DarwinInitializationSettings(),
      ),
      onDidReceiveNotificationResponse: (details) {
        try {
          final parts = details.payload?.split('_');
          if (parts == null || parts.length != 3) {
            return;
          }
          final orgId = parts[0];
          final conId = parts[1];
          final eventId = parts[2];
          AppRouter.router.go('/$orgId/$conId/schedule/event/$eventId');
        } catch (e, stackTrace) {
          Logger.error(exception: e, stackTrace: stackTrace, message: 'Failed to handle local notification response');
        }
      },
    );
    return flutterLocalNotificationsPlugin;
  }

  Future<void> clearAllNotifications() async {
    final flutterLocalNotificationsPlugin = await initNotifications();
    if (flutterLocalNotificationsPlugin != null) {
      await flutterLocalNotificationsPlugin.cancelAll();
    }
  }

  Future<void> scheduleNotifications() async {
    try {
      if (kIsWeb) {
        return;
      }
      // Initializes
      initializeTimeZones();
      final flutterLocalNotificationsPlugin = await initNotifications();
      if (flutterLocalNotificationsPlugin == null) {
        return;
      }
      flutterLocalNotificationsPlugin.cancelAll();

      // Cached user
      final userId = _authRepository.currentUserId;
      final cachedUser = await _userRepository.attemptCachedUser(userId);

      // Skips if no user or all reminders are disabled
      if (cachedUser == null ||
          (cachedUser.spotlightEventNotifications == false && cachedUser.favoriteEventNotifications == false)) {
        return;
      } else {
        final deviceNotificationsEnabled = await _deviceRepository.deviceNotificationsEnabled(userId, cachedUser);
        // Skips if notifications disabled for this device
        if (!deviceNotificationsEnabled) {
          return;
        }
      }

      final now = Timestamp.now().millisecondsSinceEpoch;
      final favoriteConIds = cachedUser.favoriteCons ?? [];
      final favoriteEventIds = cachedUser.favoriteEventNotifications != false ? cachedUser.favoriteEvents ?? [] : [];
      final List<ReminderNotificationModel> notificationModels = [];

      for (final favoriteConId in favoriteConIds) {
        // Creates conData
        final split = favoriteConId.split('_');
        if (split.length != 2) {
          continue;
        }
        final conData = ConData(orgId: split[0], conId: split[1]);

        final summaryModel = await _namedDocRepository.getSummary(conData, dataLocation: DataLocation.cache);

        // Skips if not cached, startDate is null, is further than 24 hours form now or has ended
        if (summaryModel == null ||
            summaryModel.startDate == null ||
            now < summaryModel.startDate!.millisecondsSinceEpoch - _millisecondsBeforeCon ||
            (summaryModel.endDate != null
                    ? summaryModel.endDate!.millisecondsSinceEpoch
                    : summaryModel.startDate!.millisecondsSinceEpoch + _maxConMilliseconds) <=
                now) {
          continue;
        }

        final timeZoneId = summaryModel.location?.timeZoneId;
        for (final event in summaryModel.events ?? []) {
          final eventId = '${conData.orgId}_${conData.conId}_${event.id}';
          final startTime = event.startTime?.millisecondsSinceEpoch ?? 0;
          // Favorite reminder
          if (now < startTime - _favoriteReminderMilliseconds && favoriteEventIds.contains(eventId)) {
            notificationModels.add(
              ReminderNotificationModel(
                eventId: eventId,
                reminderTime: Timestamp.fromMillisecondsSinceEpoch(startTime - _favoriteReminderMilliseconds),
                timeZoneId: timeZoneId,
                title: _favoriteTitle,
                body: event.title,
              ),
            );
          }
          // Spotlight reminder
          if (cachedUser.spotlightEventNotifications != false &&
              event.spotlight == true &&
              (event.adultOnly != true || cachedUser.showAdultContent != false) &&
              now < startTime - _spotlightReminderMilliseconds) {
            notificationModels.add(
              ReminderNotificationModel(
                eventId: eventId,
                reminderTime: Timestamp.fromMillisecondsSinceEpoch(startTime - _spotlightReminderMilliseconds),
                timeZoneId: timeZoneId,
                title: _spotlightTitle,
                body: event.title,
              ),
            );
          }
        }
      }

      notificationModels.sort((a, b) => a.reminderTime.compareTo(b.reminderTime));

      // Schedules notifications
      List<Future<void>> notificationScheduleFutures = [];
      for (int i = 0; i < 64 && i < notificationModels.length; i++) {
        final notificationModel = notificationModels[i];
        final timeZone = notificationModel.timeZoneId != null ? getLocation(notificationModel.timeZoneId!) : UTC;
        notificationScheduleFutures.add(
          flutterLocalNotificationsPlugin.zonedSchedule(
            i,
            notificationModel.title,
            notificationModel.body,
            TZDateTime.fromMillisecondsSinceEpoch(timeZone, notificationModel.reminderTime.millisecondsSinceEpoch),
            const NotificationDetails(
              android: AndroidNotificationDetails(
                'reminder_channel',
                'Reminders',
                importance: Importance.high,
                priority: Priority.high,
              ),
            ),
            androidScheduleMode: AndroidScheduleMode.inexactAllowWhileIdle,
            payload: notificationModel.eventId,
          ),
        );
      }
      try {
        await Future.wait(notificationScheduleFutures);
      } catch (e, stackTrace) {
        Logger.error(exception: e, stackTrace: stackTrace, message: 'Failed when scheduling notifications');
      }
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace, message: 'Failed to schedule notifications');
    }
  }
}
