import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/adaptive_view.dart';
import 'package:venvi/widgets/app_bar_logo.dart';

class GlassAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget? title;
  final List<Widget>? actions;
  final bool isRootScreen;
  final Widget? leading;
  final bool isFullScreenDialog;

  const GlassAppBar({
    super.key,
    this.title,
    this.actions,
    this.isRootScreen = false,
    this.leading,
    this.isFullScreenDialog = false,
  }) : assert(!isFullScreenDialog || leading == null);

  @override
  Widget build(BuildContext context) {
    final mainTheme = Theme.of(context);
    return Theme(
      data: mainTheme.copyWith(
        iconButtonTheme: IconButtonThemeData(
          style: ButtonStyle(
            iconColor: WidgetStateProperty.resolveWith((states) {
              if (states.contains(WidgetState.disabled)) {
                return mainTheme.appBarTheme.iconTheme?.color?.withValues(alpha: AppTheme.unselectedOpacity);
              }
              return mainTheme.appBarTheme.iconTheme?.color;
            }),
          ),
        ),
      ),
      child: AdaptiveView(
        desktopView: AppBar(
          automaticallyImplyLeading: false,
          title: !isRootScreen ? title : null,
          leading: isFullScreenDialog
              ? IconButton(icon: const Icon(Icons.close), onPressed: () => context.pop(), tooltip: 'Close')
              : null,
          actions: [
            ...actions ?? [],
            const SizedBox(width: AppTheme.screenPadding),
          ],
        ),
        mobileView: AppBar(
          automaticallyImplyLeading: isFullScreenDialog ? false : !isRootScreen,
          title: !isRootScreen ? title : const AppBarLogo(),
          leading: isFullScreenDialog
              ? IconButton(icon: const Icon(Icons.close), onPressed: () => context.pop(), tooltip: 'Close')
              : leading,
          actions: actions,
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
