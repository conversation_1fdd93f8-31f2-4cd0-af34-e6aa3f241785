import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/repositories/profile_repository.dart';
import 'package:venvi/screen_dialogs/profile_search/profile_search_result.dart';
import 'package:venvi/screen_dialogs/profile_search/profile_search_state.dart';
import 'package:venvi/screen_dialogs/profile_search/profile_search_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/accent_label.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/profile_item.dart';
import 'package:venvi/widgets/search_view.dart';

class ProfileSearchScreen extends StatelessWidget {
  final String title;
  final List<String>? excludedIds;
  final Widget? bottomWidget;
  final bool showManualEntries;

  const ProfileSearchScreen({
    super.key,
    this.title = 'Profile Search',
    this.excludedIds,
    this.bottomWidget,
    this.showManualEntries = false,
  });

  @override
  Widget build(BuildContext context) {
    late final ParticipantViewModel? participantViewModel;
    try {
      participantViewModel = context.read<ParticipantViewModel>();
    } catch (e) {
      participantViewModel = null;
    }

    return BlocProvider(
      create: (context) => ProfileSearchViewModel(
        participantViewModel,
        context.read<ProfileRepository>(),
        excludedIds,
        participantViewModel != null && showManualEntries,
      ),
      child: Scaffold(
        appBar: GlassAppBar(title: Text(title), isFullScreenDialog: true),
        body: Builder(
          builder: (context) => SearchView(
            autofocus: true,
            onSearchChange: (searchText) => context.read<ProfileSearchViewModel>().searchTextUpdated(searchText),
            child: BlocBuilder<ProfileSearchViewModel, ProfileSearchState>(
              builder: (context, state) => Column(
                children: [
                  Expanded(
                    child: ListView(
                      padding: const EdgeInsets.all(AppTheme.screenPadding),
                      children: [
                        if (state.participants.isNotEmpty)
                          const Padding(
                            padding: EdgeInsets.only(bottom: AppTheme.widgetPadding),
                            child: AccentLabel(text: 'Participants', isOnContainer: false),
                          ),
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const BouncingScrollPhysics(),
                          itemCount: state.participants.length,
                          separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                          itemBuilder: (context, index) {
                            final participantModel = state.participants[index];
                            return ContentArea(
                              padding: EdgeInsets.zero,
                              child: ProfileItem(
                                profile: participantModel,
                                isSearch: true,
                                onTapOverride: () async =>
                                    context.pop(ProfileSearchResult(participantModel: participantModel)),
                              ),
                            );
                          },
                        ),
                        if (state.participants.isNotEmpty && state.profiles.isNotEmpty)
                          const SizedBox(height: AppTheme.widgetPadding),
                        if (state.profiles.isNotEmpty)
                          const Padding(
                            padding: EdgeInsets.only(bottom: AppTheme.widgetPadding),
                            child: AccentLabel(text: 'Venvi Users', isOnContainer: false),
                          ),
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const BouncingScrollPhysics(),
                          itemCount: state.profiles.length,
                          separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                          itemBuilder: (context, index) {
                            final profileModel = state.profiles[index];
                            return ContentArea(
                              padding: EdgeInsets.zero,
                              child: ProfileItem(
                                profile: profileModel,
                                isSearch: true,
                                onTapOverride: () => context.pop(ProfileSearchResult(profileModel: profileModel)),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                  if (bottomWidget != null)
                    Padding(
                      padding: const EdgeInsets.fromLTRB(
                        AppTheme.screenPadding,
                        0,
                        AppTheme.screenPadding,
                        AppTheme.screenPadding,
                      ),
                      child: bottomWidget,
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
