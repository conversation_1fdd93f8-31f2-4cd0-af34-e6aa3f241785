import 'dart:async';

import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/event_type.dart';
import 'package:venvi/constants/metadata_field.dart';
import 'package:venvi/constants/named_doc.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/metadata_listener_view_model.dart';
import 'package:venvi/global_view_models/metadata_view_model.dart';
import 'package:venvi/global_view_models/named_doc_state.dart';
import 'package:venvi/models/event_properties_model.dart';
import 'package:venvi/repositories/cloud_messaging_repository.dart';
import 'package:venvi/repositories/named_doc_repository.dart';

class NamedDocViewModel extends MetadataListenerViewModel<dynamic, NamedDocState> {
  final ConData _conData;
  final NamedDocRepository _repository;
  final CloudMessagingRepository _cloudMessagingRepository;

  NamedDocViewModel(
    this._conData,
    this._repository,
    MetadataViewModel metadataViewModel,
    this._cloudMessagingRepository,
  ) : super(const NamedDocState(), _conData, metadataViewModel, MetadataField.namedDocs);

  @override
  Future<dynamic> getData(String id, DataLocation dataLocation) async {
    return await _repository.getNamedDoc(_conData, id, dataLocation: dataLocation);
  }

  @override
  void onData(Map<String, dynamic> data) {
    if (data.containsKey(NamedDoc.summary.name)) {
      _cloudMessagingRepository.scheduleNotifications();
    }

    if (isClosed) {
      return;
    }
    if (data.containsKey(NamedDoc.eventProperties.name)) {
      emit(state.copyWith(
        eventProperties: EventPropertiesModel.fromJson(data[NamedDoc.eventProperties.name]),
      ));
    }
  }

  List<EventType> getEnabledEventTypes() {
    final List<EventType> enabledEventTypes = [];
    final customTypes = state.eventProperties?.eventTypes;
    for (final type in EventType.values) {
      final customType = customTypes?[type];
      if (!type.optional || customType?.enabled == true) {
        enabledEventTypes.add(type);
      }
    }
    return enabledEventTypes;
  }
}
