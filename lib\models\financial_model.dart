import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/billing_types.dart';
import 'package:venvi/json_converters/timestamp_converter.dart';

part 'financial_model.freezed.dart';
part 'financial_model.g.dart';

@freezed
sealed class FinancialModel with _$FinancialModel {
  const factory FinancialModel({
    BillingType? billingType,
    String? stripeCustomerId,
    String? lastConId,
    int? lastConDownloads,
    List<String>? paidConIds,
    String? subscriptionId,
    @TimestampConverter() Timestamp? subscriptionEndDate,
    bool? starterTierLimitReached,
  }) = _FinancialModel;

  factory FinancialModel.fromJson(Map<String, dynamic> json) => _$FinancialModelFromJson(json);
}
