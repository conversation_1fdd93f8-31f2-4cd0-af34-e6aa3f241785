import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:sign_in_button/sign_in_button.dart';
import 'package:venvi/custom_classes/user_friendly_exception.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/screens/delete_account/delete_account_state.dart';
import 'package:venvi/screens/delete_account/delete_account_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/snack_bars.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/content_group.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/glass_tile_button.dart';

class DeleteAccountScreen extends StatelessWidget {
  const DeleteAccountScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => DeleteAccountViewModel(context.read<AuthRepository>()),
      child: Scaffold(
        appBar: const GlassAppBar(title: Text('Delete Account')),
        body: BlocBuilder<DeleteAccountViewModel, DeleteAccountState>(
          builder:
              (context, state) => Column(
                children: [
                  Expanded(
                    child: ListView(
                      padding: const EdgeInsets.all(AppTheme.screenPadding),
                      children: [
                        const ContentArea(
                          child: Text(
                            'Deleting your account will remove all your private and public data. You\'ll be removed from all public areas where your profile shows up. This cannot be undone.',
                            textAlign: TextAlign.center,
                          ),
                        ),
                        const SizedBox(height: AppTheme.widgetPadding),
                        ContentGroup(
                          title: 'You must re-sign in',
                          children: [
                            SignInButton(
                              Buttons.google,
                              elevation: 0,
                              onPressed: () async {
                                try {
                                  final user = await context.read<AuthRepository>().reAuthenticateWithGoogle();
                                  if (context.mounted) {
                                    context.read<DeleteAccountViewModel>().reAuth(user);
                                  }
                                } catch (e) {
                                  if (!context.mounted) {
                                    return;
                                  }
                                  if (e is UserFriendlyException) {
                                    if (e.message != null) {
                                      Dialogs.showErrorDialog(context, message: e.message);
                                    }
                                  } else {
                                    Dialogs.showErrorDialog(context, message: 'Failed to authenticate with Google');
                                  }
                                }
                              },
                            ),
                            if (!kIsWeb && Platform.isIOS)
                              Padding(
                                padding: const EdgeInsets.only(top: AppTheme.widgetPaddingSmall),
                                child: SignInButton(
                                  Buttons.apple,
                                  elevation: 0,
                                  onPressed: () async {
                                    try {
                                      final user = await context.read<AuthRepository>().reAuthenticateWithApple();
                                      if (context.mounted) {
                                        context.read<DeleteAccountViewModel>().reAuth(user);
                                      }
                                    } catch (e) {
                                      if (!context.mounted) {
                                        return;
                                      }
                                      if (e is UserFriendlyException) {
                                        if (e.message != null) {
                                          Dialogs.showErrorDialog(context, message: e.message);
                                        }
                                      } else {
                                        Dialogs.showErrorDialog(context, message: 'Failed to authenticate with Apple');
                                      }
                                    }
                                  },
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(AppTheme.screenPadding),
                    child: ContentArea(
                      padding: EdgeInsets.zero,
                      child: GlassTileButton(
                        text: 'Delete Account',
                        icon: Icons.delete,
                        isAccent: true,
                        onTap:
                            state.canDelete
                                ? () async {
                                  final confirmDelete = await Dialogs.showConfirmationDialog(
                                    context,
                                    title: 'Delete Account?',
                                    message: 'I agree to permanently delete my account',
                                  );
                                  if (confirmDelete == true && context.mounted) {
                                    final result = await context.read<DeleteAccountViewModel>().deleteAccount();
                                    if (context.mounted) {
                                      if (result) {
                                        SnackBars.showInfoSnackBar(context, 'Account deleted');
                                        context.go('/home');
                                      } else {
                                        SnackBars.showInfoSnackBar(context, 'Failed to delete account');
                                      }
                                    }
                                  }
                                }
                                : null,
                      ),
                    ),
                  ),
                ],
              ),
        ),
      ),
    );
  }
}
