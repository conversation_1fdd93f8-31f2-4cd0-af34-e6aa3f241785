import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/event_type.dart';
import 'package:venvi/custom_classes/date_fields.dart';
import 'package:venvi/models/event_model.dart';

part 'schedule_state.freezed.dart';

@freezed
sealed class ScheduleState with _$ScheduleState {
  const factory ScheduleState({
    required List<DateFields> conDays,
    required int selectedDayIndex,
    required int? todayDayIndex,
    Map<DateFields, Map<TimeOfDay, List<EventModel>>>? events,
    required bool spotlightOnly,
    EventType? eventTypeFilter,
  }) = _ScheduleState;
}
