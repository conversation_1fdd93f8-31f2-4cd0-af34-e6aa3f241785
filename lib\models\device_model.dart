// ignore_for_file: invalid_annotation_target

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/device_platform.dart';
import 'package:venvi/json_converters/timestamp_converter.dart';

part 'device_model.freezed.dart';
part 'device_model.g.dart';

@freezed
sealed class DeviceModel with _$DeviceModel {
  const factory DeviceModel({
    String? deviceId,
    @TimestampConverter() Timestamp? deviceAdded,
    @JsonKey(unknownEnumValue: JsonKey.nullForUndefinedEnumValue) DevicePlatform? devicePlatform,
    String? pushToken,
  }) = _DeviceModel;

  factory DeviceModel.fromJson(Map<String, dynamic> json) => _$DeviceModelFromJson(json);
}
