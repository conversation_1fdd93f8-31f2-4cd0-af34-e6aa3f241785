// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/cloud_message_type.dart';

part 'cloud_message_con_start_data_model.freezed.dart';
part 'cloud_message_con_start_data_model.g.dart';

@freezed
sealed class CloudMessageConStartDataModel with _$CloudMessageConStartDataModel {
  const factory CloudMessageConStartDataModel({
    @JsonKey(unknownEnumValue: JsonKey.nullForUndefinedEnumValue) CloudMessageType? messageType,
    String? orgId,
    String? conId,
  }) = _CloudMessageConStartDataModel;

  factory CloudMessageConStartDataModel.fromJson(Map<String, dynamic> json) =>
      _$CloudMessageConStartDataModelFromJson(json);
}
