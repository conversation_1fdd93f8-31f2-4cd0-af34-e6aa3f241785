import 'package:flutter/material.dart';

enum SocialMedia {
  website(text: 'Website', url: 'https://', icon: Icons.language),
  facebook(
    text: 'Facebook',
    url: 'https://facebook.com/',
    icon: IconData(0xf09a, fontFamily: 'FontAwesomeBrands'),
  ),
  instagram(
    text: 'Instagram',
    url: 'https://instagram.com/',
    icon: IconData(0xf16d, fontFamily: 'FontAwesomeBrands'),
  ),
  x(
    text: 'X',
    url: 'https://x.com/',
    icon: IconData(0xe61b, fontFamily: 'FontAwesomeBrands'),
  ),
  discord(
    text: 'Discord',
    url: 'https://discord.gg/',
    icon: IconData(0xf392, fontFamily: 'FontAwesomeBrands'),
  ),
  linkedin(
    text: 'LinkedIn',
    url: 'https://linkedin.com/',
    icon: IconData(0xf08c, fontFamily: 'FontAwesomeBrands'),
  ),
  youtube(
    text: 'YouTube',
    url: 'https://youtube.com/',
    icon: IconData(0xf167, fontFamily: 'FontAwesomeBrands'),
  ),
  tiktok(
    text: 'TikTok',
    url: 'https://tiktok.com/',
    icon: IconData(0xe07b, fontFamily: 'FontAwesomeBrands'),
  ),
  twitch(
    text: 'Twitch',
    url: 'https://twitch.tv/',
    icon: IconData(0xf1e8, fontFamily: 'FontAwesomeBrands'),
  ),
  snapchat(
    text: 'Snapchat',
    url: 'https://snapchat.com/',
    icon: IconData(0xf2ab, fontFamily: 'FontAwesomeBrands'),
  ),
  whatsapp(
    text: 'WhatsApp',
    url: 'https://wa.me/',
    icon: IconData(0xf232, fontFamily: 'FontAwesomeBrands'),
  ),
  telegram(
    text: 'Telegram',
    url: 'https://t.me/',
    icon: IconData(0xf2c6, fontFamily: 'FontAwesomeBrands'),
  ),
  flickr(
    text: 'Flickr',
    url: 'https://flickr.com/',
    icon: IconData(0xf16e, fontFamily: 'FontAwesomeBrands'),
  ),
  medium(
    text: 'Medium',
    url: 'https://medium.com/',
    icon: IconData(0xf23a, fontFamily: 'FontAwesomeBrands'),
  ),
  pinterest(
    text: 'Pinterest',
    url: 'https://pinterest.com/',
    icon: IconData(0xf0d2, fontFamily: 'FontAwesomeBrands'),
  ),
  reddit(
    text: 'Reddit',
    url: 'https://reddit.com/',
    icon: IconData(0xf1a1, fontFamily: 'FontAwesomeBrands'),
  ),
  bluesky(
    text: 'Bluesky',
    url: 'https://bsky.app/',
    icon: IconData(0xe671, fontFamily: 'FontAwesomeBrands'),
  ),
  tumblr(
    text: 'Tumblr',
    url: 'https://tumblr.com/',
    icon: IconData(0xf173, fontFamily: 'FontAwesomeBrands'),
  );

  final String text;
  final String url;
  final IconData icon;

  const SocialMedia({required this.text, required this.url, required this.icon});
}
