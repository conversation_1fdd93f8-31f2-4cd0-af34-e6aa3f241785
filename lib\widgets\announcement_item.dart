import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/models/announcement_model.dart';
import 'package:venvi/models/base_profile.dart';
import 'package:venvi/repositories/profile_repository.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/admin_profile_info_getter.dart';
import 'package:venvi/utils/dialogs.dart';

class AnnouncementItem extends StatelessWidget {
  final AnnouncementModel announcement;
  final IconButton? leadingButton;
  final bool showCreator;
  final bool? read;
  final Function()? onTap;

  const AnnouncementItem({
    super.key,
    required this.announcement,
    this.leadingButton,
    this.showCreator = false,
    this.read,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (showCreator) {
      return FutureBuilder<BaseProfile?>(
        future: AdminProfileInfoGetter.getProfileInfo(
          context.read<ParticipantViewModel>(),
          context.read<ProfileRepository>(),
          announcement.createdBy,
        ),
        builder: (context, snapshot) {
          final createdBy = announcement.createdBy;
          if (snapshot.connectionState != ConnectionState.done || createdBy == null) {
            return _buildTile(context);
          }

          final profile = snapshot.data;
          if (profile == null) {
            return _buildTile(context);
          }
          return _buildTile(context, optionalText: '\nSender: ${profile.displayName}');
        },
      );
    } else {
      return _buildTile(context);
    }
  }

  Widget _buildTile(
    BuildContext context, {
    String? optionalText,
  }) {
    return ListTile(
      title: Text(announcement.headline ?? ''),
      subtitle: announcement.timestamp != null
          ? Text(
              '${DateFormat.MMMEd().format(announcement.timestamp!.toDate())} - ${DateFormat.jm().format(announcement.timestamp!.toDate())}${optionalText ?? ''}',
            )
          : null,
      titleTextStyle: read == true
          ? Theme.of(context).listTileTheme.titleTextStyle?.copyWith(
                color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: AppTheme.subtleWidgetOpacity),
              )
          : read == false
              ? Theme.of(context).listTileTheme.titleTextStyle?.copyWith(
                    fontWeight: FontWeight.bold,
                  )
              : null,
      subtitleTextStyle: read == true
          ? Theme.of(context).listTileTheme.subtitleTextStyle?.copyWith(
                color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: AppTheme.subtleWidgetOpacity),
              )
          : null,
      contentPadding: Theme.of(context).listTileTheme.contentPadding?.subtract(
            EdgeInsets.only(
              left: leadingButton != null ? AppTheme.widgetPadding : 0,
              right: AppTheme.widgetPadding,
            ),
          ),
      leading: leadingButton,
      trailing: read == true
          ? Icon(
              Icons.chevron_right,
              color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: AppTheme.subtleWidgetOpacity),
            )
          : const Icon(Icons.chevron_right),
      onTap: () async {
        await Dialogs.showInfoDialog(
          context,
          title: announcement.headline,
          message: announcement.details,
          isSelectable: true,
        );
        onTap?.call();
      },
    );
  }
}
