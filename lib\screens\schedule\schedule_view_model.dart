import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/event_type.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/custom_classes/date_fields.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/global_view_models/event_view_model.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/models/con_model.dart';
import 'package:venvi/models/event_model.dart';
import 'package:venvi/models/user_model.dart';
import 'package:venvi/screens/schedule/schedule_state.dart';

class ScheduleViewModel extends Cubit<ScheduleState> {
  final UserViewModel _userViewModel;
  final EventViewModel _eventViewModel;
  final ConViewModel _conViewModel;
  final bool _favoritesOnly;

  late final StreamSubscription<UserModel> _userSubscription;
  late final StreamSubscription<List<EventModel>?> _eventSubscription;
  late final StreamSubscription<ConModel?> _conSubscription;

  List<EventModel>? _allEvents;
  List<String>? _favoriteEventIds;

  bool _spotlightOnly = false;
  EventType? _typeFilter;
  bool? _showAdultContent;

  late final PageController _pageController;

  PageController get pageController => _pageController;

  ScheduleViewModel(ConData conData, this._userViewModel, this._eventViewModel, this._conViewModel, this._favoritesOnly)
      : super(const ScheduleState(conDays: [], selectedDayIndex: 0, todayDayIndex: null, spotlightOnly: false)) {
    _userSubscription = _userViewModel.stream.listen(
      (state) {
        _favoriteEventIds = state.getFavoriteEvents(conData);
        _showAdultContent = state.showAdultContent;
        _filter();
      },
    );
    _favoriteEventIds = _userViewModel.state.getFavoriteEvents(conData);

    _eventSubscription = _eventViewModel.stream.listen(
      (state) {
        _allEvents = state;
        _filter();
      },
    );
    _allEvents = _eventViewModel.state;
    _filter();

    _conSubscription = _conViewModel.stream.listen(
      (state) => _updateConDays(state),
    );
    final selectedIndex = _updateConDays(_conViewModel.state);
    _pageController = PageController(initialPage: selectedIndex);
  }

  @override
  Future<void> close() {
    _userSubscription.cancel();
    _eventSubscription.cancel();
    _conSubscription.cancel();

    _pageController.dispose();

    return super.close();
  }

  void setSelectedDayIndex(int dayIndex, bool goToPage) {
    if (goToPage) {
      _pageController.jumpToPage(dayIndex);
    }
    emit(state.copyWith(selectedDayIndex: dayIndex));
  }

  void toggleSpotlightOnly() {
    _spotlightOnly = !_spotlightOnly;
    _filter();
  }

  void setType(EventType? type) {
    _typeFilter = type;
    _filter();
  }

  void clearFilters() {
    _spotlightOnly = false;
    _typeFilter = null;

    _filter();
  }

  void updateTodayIndex() {
    final conDays = _conViewModel.getConDays();

    int? todayDayIndex;
    final now = DateTime.now();
    final today = DateFields(year: now.year, month: now.month, day: now.day);
    for (var i = 0; i < conDays.length; i++) {
      if (conDays[i] == today) {
        todayDayIndex = i;
        break;
      }
    }

    emit(state.copyWith(
      todayDayIndex: todayDayIndex,
    ));
  }

  int _updateConDays(ConModel? conModel) {
    final conDays = _conViewModel.getConDays();

    int selectedDayIndex = state.selectedDayIndex;
    int? todayDayIndex;
    final now = DateTime.now();
    final today = DateFields(year: now.year, month: now.month, day: now.day);
    for (var i = 0; i < conDays.length; i++) {
      if (conDays[i] == today) {
        selectedDayIndex = i;
        todayDayIndex = i;
      }
    }

    emit(state.copyWith(
      conDays: conDays,
      selectedDayIndex: selectedDayIndex,
      todayDayIndex: todayDayIndex,
    ));

    return selectedDayIndex;
  }

  void _filter() {
    if (_allEvents == null) {
      emit(state.copyWith(
        events: null,
        spotlightOnly: _spotlightOnly,
        eventTypeFilter: _typeFilter,
      ));
      return;
    }

    final filteredEvents = _allEvents!
        .where((event) =>
            event.startTime != null &&
            (!_favoritesOnly ||
                (event.id != null && _favoriteEventIds != null && _favoriteEventIds!.contains(event.id!))) &&
            (!_spotlightOnly || event.spotlight == true) &&
            (_typeFilter == null || event.type == _typeFilter) &&
            (event.adultOnly != true || _showAdultContent != false))
        .toList();

    filteredEvents.sort((a, b) => a.startTime!.compareTo(b.startTime!));

    emit(state.copyWith(
      events: _eventViewModel.groupEventsByDateAndTime(_conViewModel.state, filteredEvents),
      spotlightOnly: _spotlightOnly,
      eventTypeFilter: _typeFilter,
    ));
  }
}
