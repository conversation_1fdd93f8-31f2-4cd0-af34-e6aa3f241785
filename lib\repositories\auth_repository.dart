import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:venvi/custom_classes/user_friendly_exception.dart';
import 'package:venvi/utils/logger.dart';

class AuthRepository {
  final FirebaseAuth _auth;

  AuthRepository(this._auth);

  Stream<User?> get authStateChanges => _auth.userChanges();

  User? get currentUser => _auth.currentUser;

  String? get currentUserId => _auth.currentUser?.uid;

  Future<UserCredential?> signInWithEmailAndPassword(String email, String password) async {
    try {
      if (_auth.currentUser != null) {
        await _auth.signOut();
      }
      return await _auth.signInWithEmailAndPassword(email: email, password: password);
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace, message: 'Failed to sign in with email and password');
      return null;
    }
  }

  Future<UserCredential?> reAuthWithEmailAndPassword(String email, String password) async {
    try {
      final credential = EmailAuthProvider.credential(email: email, password: password);
      return await _auth.currentUser?.reauthenticateWithCredential(credential);
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace, message: 'Failed to re-authenticate with email and password');
      return null;
    }
  }

  Future<UserCredential?> signInAnonymously() async {
    try {
      return await _auth.signInAnonymously();
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace);
      return null;
    }
  }

  Future<User> signInWithGoogle() async {
    try {
      final credential = await _getGoogleCredential();
      final user = _auth.currentUser;
      if (user == null) {
        return await _signIn(() => _auth.signInWithCredential(credential), null);
      } else if (await _isLinked(GoogleAuthProvider.PROVIDER_ID)) {
        return await _signIn(
          () => _auth.signInWithCredential(credential),
          () => user.reauthenticateWithCredential(credential),
        );
      } else {
        return await _linkAccount(
          () => _auth.signInWithCredential(credential),
          () => user.linkWithCredential(credential),
          () => user.reauthenticateWithCredential(credential),
        );
      }
    } catch (e, stackTrace) {
      if (e is! UserFriendlyException) {
        Logger.error(exception: e, stackTrace: stackTrace);
      }
      rethrow;
    }
  }

  Future<User> signInWithApple() async {
    try {
      final provider = _getAppleProvider();
      final user = _auth.currentUser;
      if (user == null) {
        return await _signIn(() => _auth.signInWithProvider(provider), null);
      }
      if (await _isLinked(AppleAuthProvider.PROVIDER_ID)) {
        return await _signIn(() => _auth.signInWithProvider(provider), () => user.reauthenticateWithProvider(provider));
      } else {
        return await _linkAccount(
          () => _auth.signInWithProvider(provider),
          () => user.linkWithProvider(provider),
          () => user.reauthenticateWithProvider(provider),
        );
      }
    } catch (e, stackTrace) {
      if (e is! UserFriendlyException) {
        Logger.error(exception: e, stackTrace: stackTrace);
      }
      rethrow;
    }
  }

  Future<User> reAuthenticateWithGoogle() async {
    try {
      final credential = await _getGoogleCredential();
      final user = _auth.currentUser;
      if (user == null) {
        throw UserFriendlyException('Failed to re-authenticate with Google');
      }
      return await _reAuthenticate(() => user.reauthenticateWithCredential(credential));
    } catch (e, stackTrace) {
      if (e is! UserFriendlyException) {
        Logger.error(exception: e, stackTrace: stackTrace);
      }
      rethrow;
    }
  }

  Future<User> reAuthenticateWithApple() async {
    try {
      final provider = _getAppleProvider();
      final user = _auth.currentUser;
      if (user == null) {
        throw UserFriendlyException('Failed to re-authenticate with Apple');
      }
      return await _reAuthenticate(() => user.reauthenticateWithProvider(provider));
    } catch (e, stackTrace) {
      if (e is! UserFriendlyException) {
        Logger.error(exception: e, stackTrace: stackTrace);
      }
      rethrow;
    }
  }

  Future<OAuthCredential> _getGoogleCredential() async {
    final googleSignIn = GoogleSignIn(scopes: ['email', 'profile']);
    final googleUser = await googleSignIn.signIn();

    if (googleUser == null) {
      throw UserFriendlyException('Failed to sign in with Google');
    }

    final googleAuth = await googleUser.authentication;

    return GoogleAuthProvider.credential(accessToken: googleAuth.accessToken, idToken: googleAuth.idToken);
  }

  AppleAuthProvider _getAppleProvider() {
    return AppleAuthProvider().addScope('email');
  }

  Future<bool> _isLinked(String providerId) async {
    final user = _auth.currentUser;
    if (user == null) {
      return false;
    }
    return user.providerData.any((element) => element.providerId == providerId);
  }

  Future<User> _linkAccount(
    Future<UserCredential> Function() signIn,
    Future<UserCredential> Function() linkAccount,
    Future<UserCredential> Function() reAuthenticate,
  ) async {
    try {
      final userCredential = await linkAccount();
      return userCredential.user!;
    } on FirebaseAuthException catch (e) {
      if (e.code == 'credential-already-in-use') {
        return await _signIn(signIn, reAuthenticate);
      } else if (e.code == 'provider-already-linked') {
        return await _reAuthenticate(reAuthenticate);
      } else if (e.code == 'email-already-in-use') {
        throw UserFriendlyException('Email already in use by another account');
      } else if (e.code == 'invalid-credential') {
        throw UserFriendlyException('Invalid credentials');
      } else if (e.code == 'canceled') {
        throw UserFriendlyException(null);
      }
      rethrow;
    }
  }

  Future<User> _signIn(
    Future<UserCredential> Function() signIn,
    Future<UserCredential> Function()? reAuthenticate,
  ) async {
    try {
      final userCredential = await signIn();
      return userCredential.user!;
    } on FirebaseAuthException catch (e) {
      if (e.code == 'account-exists-with-different-credential') {
        if (reAuthenticate != null) {
          return await _reAuthenticate(reAuthenticate);
        } else {
          throw UserFriendlyException('Account already exists with a different provider');
        }
      } else if (e.code == 'invalid-credential') {
        throw UserFriendlyException('Invalid credentials');
      } else if (e.code == 'canceled') {
        throw UserFriendlyException(null);
      }
      rethrow;
    }
  }

  Future<User> _reAuthenticate(Future<UserCredential> Function() reAuthenticate) async {
    try {
      final userCredential = await reAuthenticate();
      return userCredential.user!;
    } on FirebaseAuthException catch (e) {
      if (e.code == 'invalid-credential' ||
          e.code == 'invalid-email' ||
          e.code == 'wrong-password' ||
          e.code == 'invalid-verification-code' ||
          e.code == 'invalid-verification-id') {
        throw UserFriendlyException('Invalid credentials');
      } else if (e.code == 'canceled') {
        throw UserFriendlyException(null);
      }
      rethrow;
    }
  }

  Future<void> signOut() async {
    try {
      await _auth.signOut();
      await signInAnonymously();
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace);
    }
  }

  Future<bool> deleteAccount() async {
    if (_auth.currentUser == null) {
      return false;
    }
    try {
      await _auth.currentUser!.delete();
      await _auth.signInAnonymously();
      return true;
    } catch (e, stackTrace) {
      Logger.error(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to delete account: ${_auth.currentUser?.uid}',
      );
      return false;
    }
  }
}
