import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

class ImageSelector {
  Future<XFile?> selectImage(BuildContext context, {required double? cropAspectRatio}) async {
    final pickedFile = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (pickedFile == null) {
      return null;
    }

    if (cropAspectRatio == null || !context.mounted) {
      return pickedFile;
    }

    final croppedFile = await ImageCropper().cropImage(
      sourcePath: pickedFile.path,
      aspectRatio: CropAspectRatio(ratioX: cropAspectRatio, ratioY: 1),
      uiSettings: [
        WebUiSettings(
          context: context,
          viewwMode: WebViewMode.mode_1,
        ),
      ],
    );
    if (croppedFile == null) {
      return null;
    }
    return XFile(croppedFile.path);
  }
}
