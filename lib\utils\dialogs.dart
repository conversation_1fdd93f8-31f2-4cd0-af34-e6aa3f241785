import 'dart:async';
import 'dart:ui';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:venvi/constants/permissions.dart';
import 'package:venvi/constants/rules.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/custom_classes/date_fields.dart';
import 'package:venvi/global_view_models/analytics_consent_view_model.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/global_view_models/permission_view_model.dart';
import 'package:venvi/global_view_models/role_view_model.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/models/location_model.dart';
import 'package:venvi/models/role_model.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/repositories/con_repository.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/snack_bars.dart';
import 'package:venvi/utils/time_utils.dart';
import 'package:venvi/utils/url_handler.dart';
import 'package:venvi/widgets/restricted_view.dart';
import 'package:venvi/widgets/surface_input_field.dart';

enum PremiumUpgradeDialogType { upgrade, feature, eventLimit }

class Dialogs {
  static Future<bool?> showConfirmationDialog(
    BuildContext context, {
    String? title,
    String? message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
  }) async {
    return await showDialog(
      context: context,
      builder:
          (context) => BackdropFilter(
            filter: ImageFilter.blur(sigmaX: AppTheme.dialogBlurSigma, sigmaY: AppTheme.dialogBlurSigma),
            child: AlertDialog(
              title: title != null ? Text(title, textAlign: TextAlign.center) : null,
              content: message != null ? Text(message, textAlign: TextAlign.center) : null,
              actionsPadding: EdgeInsets.zero,
              actions: [
                Row(
                  children: [
                    _buildButton(
                      context,
                      text: cancelText,
                      onTap: () => Navigator.of(context, rootNavigator: true).pop(false),
                    ),
                    _buildButton(
                      context,
                      text: confirmText,
                      onTap: () => Navigator.of(context, rootNavigator: true).pop(true),
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }

  static Future<bool?> showInfoDialog(
    BuildContext context, {
    String? title,
    String? message,
    bool isSelectable = false,
  }) async {
    return await showDialog(
      context: context,
      builder:
          (context) => BackdropFilter(
            filter: ImageFilter.blur(sigmaX: AppTheme.dialogBlurSigma, sigmaY: AppTheme.dialogBlurSigma),
            child: AlertDialog(
              title: title != null ? Text(title, textAlign: TextAlign.center) : null,
              content:
                  message != null
                      ? SingleChildScrollView(
                        child:
                            isSelectable
                                ? SelectableText(message, textAlign: TextAlign.start)
                                : Text(message, textAlign: TextAlign.start),
                      )
                      : null,
              actionsPadding: EdgeInsets.zero,
              actions: [
                Row(
                  children: [
                    _buildButton(
                      context,
                      text: 'Close',
                      onTap: () => Navigator.of(context, rootNavigator: true).pop(true),
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }

  static Future<bool?> showErrorDialog(BuildContext context, {String? message}) async {
    return await showInfoDialog(context, title: 'Error', message: message);
  }

  static Future<bool?> showLoadingDialog(BuildContext context) async {
    return await showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => BackdropFilter(
            filter: ImageFilter.blur(sigmaX: AppTheme.dialogBlurSigma, sigmaY: AppTheme.dialogBlurSigma),
            child: Center(child: CircularProgressIndicator(color: Colors.white.withValues(alpha: 0.8))),
          ),
    );
  }

  static Future<String?> showTextInputDialog(
    BuildContext context, {
    String? title,
    String? hintText,
    int? maxLength,
    String? initialValue,
    TextCapitalization textCapitalization = TextCapitalization.words,
  }) async {
    String text = '';
    return await showDialog(
      context: context,
      builder:
          (context) => BackdropFilter(
            filter: ImageFilter.blur(sigmaX: AppTheme.dialogBlurSigma, sigmaY: AppTheme.dialogBlurSigma),
            child: AlertDialog(
              title: title != null ? Text(title, textAlign: TextAlign.center) : null,
              content: SurfaceInputField(
                isTertiary: true,
                child: TextFormField(
                  initialValue: initialValue,
                  autofocus: true,
                  textCapitalization: textCapitalization,
                  maxLength: maxLength,
                  textInputAction: TextInputAction.done,
                  decoration: InputDecoration(hintText: hintText, counterText: ''),
                  onChanged: (value) => text = value.trim(),
                  onFieldSubmitted: (value) => Navigator.of(context, rootNavigator: true).pop(value),
                ),
              ),
              actionsPadding: EdgeInsets.zero,
              actions: [
                Row(
                  children: [
                    _buildButton(
                      context,
                      text: 'Cancel',
                      onTap: () => Navigator.of(context, rootNavigator: true).pop(null),
                    ),
                    _buildButton(
                      context,
                      text: 'Confirm',
                      onTap: () => Navigator.of(context, rootNavigator: true).pop(text),
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }

  static Future<bool?> showPermissionDeniedDialog(BuildContext context) async {
    return await showDialog(
      context: context,
      builder:
          (context) => BackdropFilter(
            filter: ImageFilter.blur(sigmaX: AppTheme.dialogBlurSigma, sigmaY: AppTheme.dialogBlurSigma),
            child: AlertDialog(
              title: const Text('You must allow permission in your device settings', textAlign: TextAlign.center),
              actionsPadding: EdgeInsets.zero,
              actions: [
                Row(
                  children: [
                    _buildButton(
                      context,
                      text: 'Close',
                      onTap: () => Navigator.of(context, rootNavigator: true).pop(true),
                    ),
                    _buildButton(
                      context,
                      text: 'Settings',
                      onTap: () async {
                        Navigator.of(context, rootNavigator: true).pop(true);
                        await openAppSettings();
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }

  static Future<bool?> showPermissionInfoDialog(BuildContext context, {required Permissions permission}) async {
    final accessList = permission.access.isNotEmpty ? '- ${permission.access.join('\n- ')}' : null;
    return await showInfoDialog(
      context,
      title: permission.text,
      message: '${permission.description}${accessList != null ? '\n\n$accessList' : ''}',
    );
  }

  static Future<String?> showRoleSelectorDialog(BuildContext context, String participantId, List<String> roles) async {
    final roleId = await showDialog(
      context: context,
      builder:
          (_) => BlocProvider.value(
            value: context.read<PermissionViewModel>(),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: AppTheme.dialogBlurSigma, sigmaY: AppTheme.dialogBlurSigma),
              child: Dialog(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(AppTheme.widgetPadding),
                      child: Text('Select Role', style: Theme.of(context).dialogTheme.titleTextStyle),
                    ),
                    BlocBuilder<RoleViewModel, List<RoleModel>?>(
                      bloc: context.read<RoleViewModel>(),
                      builder:
                          (context, state) =>
                              state == null || state.isEmpty
                                  ? const Text('No roles found')
                                  : ListView.builder(
                                    shrinkWrap: true,
                                    itemCount: state.length,
                                    itemBuilder: (context, index) {
                                      final role = state[index];
                                      final isNewRole = !roles.contains(role.id);
                                      return RestrictedView(
                                        rule: Rules.editParticipantsAndRoles,
                                        child: ListTile(
                                          visualDensity: VisualDensity.compact,
                                          title: Text(role.name ?? ''),
                                          subtitle: isNewRole ? const Text('New Role') : const Text('Existing Role'),
                                          titleTextStyle: Theme.of(context).textTheme.titleMedium?.copyWith(
                                            color:
                                                isNewRole
                                                    ? Theme.of(context).colorScheme.onTertiary.withValues(
                                                      alpha: AppTheme.subtleWidgetOpacity,
                                                    )
                                                    : Theme.of(context).colorScheme.onTertiary,
                                          ),
                                          subtitleTextStyle: Theme.of(context).textTheme.bodySmall?.copyWith(
                                            color:
                                                isNewRole
                                                    ? Theme.of(context).colorScheme.onTertiary.withValues(
                                                      alpha: AppTheme.subtleWidgetOpacity,
                                                    )
                                                    : Theme.of(context).colorScheme.onTertiary.withValues(
                                                      alpha: AppTheme.subtitleTextOpacity,
                                                    ),
                                          ),
                                          onTap: () => Navigator.of(context, rootNavigator: true).pop(role.id),
                                        ),
                                      );
                                    },
                                  ),
                    ),
                  ],
                ),
              ),
            ),
          ),
    );

    if (roleId == null) {
      return null;
    }

    final participantHasRole = roles.contains(roleId);

    if (participantHasRole) {
      return roleId;
    }

    if (context.mounted) {
      final confirmAdd = await showConfirmationDialog(context, title: 'Add user to new role?');
      if (confirmAdd == true && context.mounted) {
        showLoadingDialog(context);
        final successfullyAddedRole = await context.read<ParticipantRepository>().addToRole(
          context.read<ConData>(),
          participantId,
          roleId,
        );
        if (context.mounted) {
          Navigator.of(context, rootNavigator: true).pop();
          return successfullyAddedRole ? roleId : null;
        }
      }
    }

    return null;
  }

  static Future<TimeOfDay?> showConTimeTimePickerDialog(
    BuildContext context,
    LocationModel? locationModel,
    Timestamp? initialTime,
  ) async {
    final initialTimeOfDay = TimeUtils.convertToConTimeZone(locationModel, initialTime);
    final timePicked = await showTimePicker(
      context: context,
      initialTime:
          initialTimeOfDay != null ? TimeOfDay.fromDateTime(initialTimeOfDay) : const TimeOfDay(hour: 12, minute: 0),
      initialEntryMode: TimePickerEntryMode.dialOnly,
      helpText: '',
      builder: (context, child) {
        final foreground = Theme.of(context).dialogTheme.iconColor ?? Colors.grey;
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme:
                Theme.of(context).brightness == Brightness.light
                    ? ColorScheme.light(
                      surface: Theme.of(context).dialogTheme.backgroundColor ?? Colors.grey,
                      primary: foreground,
                      onSurface: foreground,
                      surfaceContainerHighest: Colors.transparent,
                      secondary: foreground,
                      onSecondary: AppTheme.lightForegroundColor,
                      outline: Colors.grey.withValues(alpha: 0.8),
                    )
                    : ColorScheme.dark(
                      surface: Theme.of(context).dialogTheme.backgroundColor ?? Colors.grey,
                      primary: foreground,
                      onSurface: foreground,
                      surfaceContainerHighest: Colors.transparent,
                      secondary: foreground,
                      onSecondary: AppTheme.darkForegroundColor,
                      outline: Colors.grey.withValues(alpha: 0.8),
                    ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(foregroundColor: foreground, iconColor: foreground),
            ),
          ),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: AppTheme.dialogBlurSigma, sigmaY: AppTheme.dialogBlurSigma),
            child: child,
          ),
        );
      },
    );
    if (timePicked == null) {
      return null;
    }

    return TimeOfDay(hour: timePicked.hour, minute: timePicked.minute);
  }

  static Future<DateFields?> showConTimeDatePickerDialog(
    BuildContext context,
    LocationModel? locationModel,
    Timestamp? initialDate,
    Timestamp startDate,
    Timestamp endDate,
  ) async {
    final firstDate = TimeUtils.convertToConTimeZone(locationModel, startDate);
    final lastDate = TimeUtils.convertToConTimeZone(locationModel, endDate);
    if (firstDate == null || lastDate == null) {
      await showErrorDialog(context, message: 'Could not load date');
      return null;
    }
    final conInitialDate = TimeUtils.convertToConTimeZone(locationModel, initialDate ?? startDate);
    return showDatePickerDialog(context, conInitialDate, firstDate, lastDate);
  }

  static Future<DateFields?> showDatePickerDialog(
    BuildContext context,
    DateTime? initialDate,
    DateTime firstDate,
    DateTime lastDate,
  ) async {
    final datePicked = await showDatePicker(
      context: context,
      initialDatePickerMode: DatePickerMode.day,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      initialEntryMode: DatePickerEntryMode.calendarOnly,
      helpText: '',
      builder: (context, child) {
        final foreground = Theme.of(context).dialogTheme.iconColor ?? Colors.grey;
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme:
                Theme.of(context).brightness == Brightness.light
                    ? ColorScheme.light(
                      surface: Theme.of(context).dialogTheme.backgroundColor ?? Colors.grey,
                      primary: foreground,
                      onSurface: foreground,
                    )
                    : ColorScheme.dark(
                      surface: Theme.of(context).dialogTheme.backgroundColor ?? Colors.grey,
                      primary: foreground,
                      onSurface: foreground,
                    ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(foregroundColor: foreground, iconColor: foreground),
            ),
            dividerTheme: const DividerThemeData(color: Colors.transparent),
          ),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: AppTheme.dialogBlurSigma, sigmaY: AppTheme.dialogBlurSigma),
            child: child,
          ),
        );
      },
    );
    if (datePicked == null) {
      return null;
    }
    return DateFields(year: datePicked.year, month: datePicked.month, day: datePicked.day);
  }

  static Future<void> showSignOutDialog(BuildContext context) async {
    final userViewModel = context.read<UserViewModel>();
    final authRepository = context.read<AuthRepository>();
    final confirmed = await Dialogs.showConfirmationDialog(context, title: 'Sign Out?');
    if (confirmed == true) {
      await userViewModel.signOut();
      await authRepository.signOut();
    }
  }

  static Future<UserCredential?> showEmailSignInDialog(BuildContext context, {bool reAuth = false}) async {
    final emailController = TextEditingController();
    final passwordController = TextEditingController();
    final AuthRepository authRepository = context.read<AuthRepository>();

    onSubmit() async {
      FocusManager.instance.primaryFocus?.unfocus();
      final userAccount =
          reAuth
              ? await authRepository.reAuthWithEmailAndPassword(emailController.text, passwordController.text)
              : await authRepository.signInWithEmailAndPassword(emailController.text, passwordController.text);
      if (context.mounted) {
        Navigator.of(context, rootNavigator: true).pop(userAccount);
        SnackBars.showInfoSnackBar(context, userAccount != null ? 'Signed in successfully' : 'Sign in failed');
      }
    }

    return await showDialog<UserCredential>(
      context: context,
      builder:
          (context) => BackdropFilter(
            filter: ImageFilter.blur(sigmaX: AppTheme.dialogBlurSigma, sigmaY: AppTheme.dialogBlurSigma),
            child: AlertDialog(
              title: const Text('Sign In', textAlign: TextAlign.center),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SurfaceInputField(
                    isTertiary: true,
                    child: TextField(
                      controller: emailController,
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.next,
                      decoration: const InputDecoration(hintText: 'Email', prefixIcon: Icon(Icons.email)),
                    ),
                  ),
                  const SizedBox(height: AppTheme.widgetPadding),
                  SurfaceInputField(
                    isTertiary: true,
                    child: TextField(
                      controller: passwordController,
                      onSubmitted: (value) => onSubmit(),
                      textInputAction: TextInputAction.done,
                      obscureText: true,
                      decoration: const InputDecoration(hintText: 'Password', prefixIcon: Icon(Icons.lock)),
                    ),
                  ),
                ],
              ),
              actionsPadding: EdgeInsets.zero,
              actions: [
                Row(
                  children: [
                    _buildButton(context, text: 'Close', onTap: () => Navigator.of(context, rootNavigator: true).pop()),
                    _buildButton(context, text: 'Sign In', onTap: () => onSubmit()),
                  ],
                ),
              ],
            ),
          ),
    );
  }

  static Future<void> showAnalyticsConsentDialog(BuildContext context) async {
    return await showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => BackdropFilter(
            filter: ImageFilter.blur(sigmaX: AppTheme.dialogBlurSigma, sigmaY: AppTheme.dialogBlurSigma),
            child: AlertDialog(
              title: const Text('Help Us Improve', textAlign: TextAlign.center),
              content: RichText(
                text: TextSpan(
                  style: TextStyle(color: Theme.of(context).colorScheme.onTertiaryContainer),
                  children: [
                    TextSpan(
                      text: 'Share analytics and crash reports with us?\n\n',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onTertiaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextSpan(
                      text: 'What we collect: ',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onTertiaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const TextSpan(text: 'Usage data, performance metrics, and crash reports\n\n'),
                    TextSpan(
                      text: 'Why we collect it: ',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onTertiaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const TextSpan(text: 'To fix bugs and improve your experience\n\n'),
                    TextSpan(
                      text: 'Privacy: ',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onTertiaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const TextSpan(text: 'We never sell your data and is only used internally\n\n'),
                    const TextSpan(text: 'You can change this at any time in the settings\n\n'),
                    TextSpan(
                      text: 'Privacy Policy',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onTertiaryContainer,
                        decoration: TextDecoration.underline,
                      ),
                      recognizer: TapGestureRecognizer()..onTap = () => UrlHandler.open('https://privacy.venvi.app'),
                    ),
                  ],
                ),
              ),
              actionsPadding: EdgeInsets.zero,
              actions: [
                Row(
                  children: [
                    _buildButton(
                      context,
                      text: 'Decline',
                      onTap: () {
                        context.read<AnalyticsConsentViewModel>().disableAnalytics();
                        Navigator.of(context, rootNavigator: true).pop();
                      },
                    ),
                    _buildButton(
                      context,
                      text: 'Accept',
                      onTap: () {
                        context.read<AnalyticsConsentViewModel>().enableAnalytics();
                        Navigator.of(context, rootNavigator: true).pop();
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }

  static Future<bool?> showPremiumFeatureDialog(BuildContext context, PremiumUpgradeDialogType type) async {
    final conData = context.read<ConData>();
    final canUpgrade = context.read<PermissionViewModel>().isPermitted(Rules.upgradeToPremium);
    final conRepository = context.read<ConRepository>();
    final conViewModel = context.read<ConViewModel>();

    final listStyle = Theme.of(context).textTheme.bodyMedium?.copyWith(
      color: Theme.of(context).colorScheme.onTertiaryContainer.withValues(alpha: AppTheme.subtleWidgetOpacity),
    );

    late final String title;
    switch (type) {
      case PremiumUpgradeDialogType.upgrade:
        title = 'Upgrade to Premium';
        break;
      case PremiumUpgradeDialogType.feature:
        title = 'Premium Feature';
        break;
      case PremiumUpgradeDialogType.eventLimit:
        title = 'Event Limit Reached';
        break;
    }

    final completer = Completer<bool>();
    final initialChoice = await showDialog(
      context: context,
      builder:
          (dialogContext) => BackdropFilter(
            filter: ImageFilter.blur(sigmaX: AppTheme.dialogBlurSigma, sigmaY: AppTheme.dialogBlurSigma),
            child: AlertDialog(
              title: Text(title, textAlign: TextAlign.center),
              content: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (!canUpgrade)
                      Padding(
                        padding: const EdgeInsets.only(bottom: AppTheme.widgetPadding),
                        child: Text('Contact your organization owner to upgrade'),
                      ),
                    Text('Premium features:'),
                    Text('• Send announcements to all attendees', style: listStyle),
                    Text('• Event scheduling conflict detector', style: listStyle),
                    Text('• Event-specific admins', style: listStyle),
                    Text('• Import/export schedules', style: listStyle),
                    Text('• Unlimited events', style: listStyle),
                    if (canUpgrade)
                      Padding(
                        padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                        child: Text(
                          'Payment is not required until publishing the con',
                          style: Theme.of(dialogContext).textTheme.bodySmall?.copyWith(
                            color: Theme.of(dialogContext).colorScheme.onTertiaryContainer,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              actionsPadding: EdgeInsets.zero,
              actions: [
                Row(
                  children: [
                    _buildButton(
                      dialogContext,
                      text: 'Stay with Starter',
                      onTap: () {
                        Navigator.of(dialogContext, rootNavigator: true).pop(false);
                        completer.complete(false);
                      },
                    ),
                    if (canUpgrade)
                      _buildButton(
                        dialogContext,
                        text: 'Upgrade to Premium',
                        onTap: () async {
                          Navigator.of(dialogContext, rootNavigator: true).pop(true);
                          showLoadingDialog(context);

                          final success = await conRepository.upgradeToPremium(conData);
                          if (success) {
                            bool upgraded = false;
                            for (int i = 0; i < 100 && context.mounted && !upgraded; i++) {
                              upgraded = conViewModel.isPremium();
                              if (!upgraded) {
                                await Future.delayed(const Duration(milliseconds: 100));
                              }
                            }
                            if (context.mounted && !upgraded) {
                              Navigator.of(context, rootNavigator: true).pop();
                              SnackBars.showInfoSnackBar(context, 'Upgrading timed out');
                              completer.complete(false);
                              return;
                            }
                          }

                          if (context.mounted) {
                            Navigator.of(context, rootNavigator: true).pop();
                            if (success) {
                              SnackBars.showInfoSnackBar(context, 'Upgraded to premium');
                            } else {
                              SnackBars.showInfoSnackBar(context, 'Failed to upgrade to premium');
                            }
                          }
                          completer.complete(success);
                        },
                      ),
                  ],
                ),
              ],
            ),
          ),
    );

    if (initialChoice == null) {
      return null;
    }
    return completer.future;
  }

  static Widget _buildButton(BuildContext context, {required String text, required void Function()? onTap}) {
    return Expanded(
      child: InkWell(
        borderRadius: BorderRadius.circular(AppTheme.borderRadius),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.widgetPadding),
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: Theme.of(
              context,
            ).textTheme.labelLarge?.copyWith(color: Theme.of(context).colorScheme.onTertiaryContainer),
          ),
        ),
      ),
    );
  }
}
