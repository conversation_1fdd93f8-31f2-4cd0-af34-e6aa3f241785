import 'package:flutter/material.dart';
import 'package:venvi/theme.dart';

class GradientBackground extends StatelessWidget {
  final Color? color;
  final Widget? child;

  const GradientBackground({
    super.key,
    this.color,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: Theme.of(context).brightness == Brightness.light
            ? AppTheme.getLightModeGradient(color)
            : AppTheme.getDarkModeGradient(color),
      ),
      child: GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: child,
      ),
    );
  }
}
