import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/models/base_profile.dart';
import 'package:venvi/repositories/profile_repository.dart';

class AdminProfileInfoGetter {
  static Future<BaseProfile?> getProfileInfo(
    ParticipantViewModel participantViewModel,
    ProfileRepository profileRepository,
    String? userId,
  ) async {
    if (userId == null) {
      return null;
    }

    final participant = participantViewModel.getParticipant(userId);
    if (participant != null) {
      return participant;
    }

    final profile = await profileRepository.getProfile(userId);
    if (profile != null) {
      return profile;
    }

    return null;
  }
}
