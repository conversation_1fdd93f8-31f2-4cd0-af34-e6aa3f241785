import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:image_picker/image_picker.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/image_library_type.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/utils/logger.dart';

class ImageLibraryRepository {
  final FirebaseFirestore _firestore;
  final ImageRepository _imageRepository;

  ImageLibraryRepository(this._firestore, this._imageRepository);

  Stream<List<ImageModel>> getImagesStream(ConData conData, ImageLibraryType imageLibraryType) {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection(imageLibraryType.firestoreCollection);
    try {
      return ref.snapshots().map(
        (snapshot) =>
            snapshot.docs
                .map((doc) {
                  try {
                    return ImageModel.fromJson(doc.data());
                  } catch (e, stackTrace) {
                    Logger.error(exception: e, stackTrace: stackTrace);
                    return null;
                  }
                })
                .whereType<ImageModel>()
                .toList(),
      );
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed getting doc stream',
        collectionReference: ref,
      );
      return Stream.value([]);
    }
  }

  Future<List<ImageModel>> getImages(
    ConData conData,
    ImageLibraryType imageLibraryType, {
    DataLocation? dataLocation,
  }) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection(imageLibraryType.firestoreCollection);
    try {
      final snapshot = await ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));

      return snapshot.docs
          .map((doc) {
            try {
              return ImageModel.fromJson(doc.data());
            } catch (e, stackTrace) {
              Logger.error(exception: e, stackTrace: stackTrace);
              return null;
            }
          })
          .whereType<ImageModel>()
          .toList();
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        collectionReference: ref,
        dataLocation: dataLocation,
      );
      return [];
    }
  }

  Future<ImageModel?> getImage(
    ConData conData,
    ImageLibraryType imageLibraryType,
    String id, {
    DataLocation? dataLocation,
  }) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection(imageLibraryType.firestoreCollection)
        .doc(id);
    try {
      final snapshot = await ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));

      if (snapshot.exists) {
        final data = snapshot.data();
        if (data != null) {
          return ImageModel.fromJson(data);
        }
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        documentReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<ImageModel?> createImage(
    ConData conData,
    ImageLibraryType imageLibraryType,
    ImageModel imageModel,
    XFile file,
  ) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection(imageLibraryType.firestoreCollection)
        .doc(imageModel.id);
    try {
      await ref.set(imageModel.toJson());
      final success = await _imageRepository.uploadImage(imageModel, file);
      if (success) {
        return imageModel;
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to create doc',
        documentReference: ref,
      );
      return null;
    }
  }

  Future<bool> deleteImage(ConData conData, ImageLibraryType imageLibraryType, String id) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection(imageLibraryType.firestoreCollection)
        .doc(id);
    try {
      await ref.delete();
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to delete doc',
        documentReference: ref,
      );
      return false;
    }
  }
}
