import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:venvi/models/general_content_tile_model.dart';

part 'general_content_tile_state.freezed.dart';

@freezed
sealed class GeneralContentTileState with _$GeneralContentTileState {
  const factory GeneralContentTileState({
    required String key,
    required GeneralContentTileModel model,
    XFile? imageFile,
  }) = _GeneralContentTileState;
}
