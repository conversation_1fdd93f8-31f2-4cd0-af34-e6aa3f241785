import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/permissions.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/models/profile_snippet_model.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/repositories/permission_repository.dart';
import 'package:venvi/repositories/profile_repository.dart';
import 'package:venvi/router.dart';
import 'package:venvi/screen_dialogs/profile_search/profile_search_result.dart';
import 'package:venvi/screen_dialogs/profile_search/profile_search_screen.dart';
import 'package:venvi/screens/admin_tools/permissions_manager_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/snack_bars.dart';
import 'package:venvi/widgets/accent_label.dart';
import 'package:venvi/widgets/content_group.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/profile_item.dart';

class PermissionsManagerScreen extends StatelessWidget {
  const PermissionsManagerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PermissionsManagerViewModel(
        context.read<ConData>().orgId,
        context.read<PermissionRepository>(),
        context.read<ParticipantViewModel>(),
        context.read<ProfileRepository>(),
      ),
      child: Scaffold(
        appBar: const GlassAppBar(title: Text('Manage Permissions')),
        body: ListView(
          padding: const EdgeInsets.all(AppTheme.screenPadding),
          children: [
            ContentGroup(
              title: Permissions.orgOwner.text,
              subtitle: Permissions.orgOwner.description,
              padHorizontally: false,
              children: [
                BlocBuilder<PermissionsManagerViewModel, Map<Permissions, List<ProfileSnippetModel>>>(
                  builder: (context, state) {
                    final ownerProfile = state[Permissions.orgOwner]?.firstOrNull;
                    if (ownerProfile == null) {
                      return const Center(
                        child: AccentLabel(text: 'Could not find organization owner', isOnContainer: true),
                      );
                    }

                    return ProfileItem(
                      profile: ownerProfile,
                      leadingButton: context.read<AuthRepository>().currentUserId == ownerProfile.id
                          ? IconButton(
                              tooltip: 'Change Organization Owner',
                              onPressed: () async {
                                final oldOwnerId = ownerProfile.id;
                                if (oldOwnerId == null) {
                                  return;
                                }

                                final profileSearchResult = await AppRouter.pushFullScreenDialog<ProfileSearchResult?>(
                                  context,
                                  ProfileSearchScreen(title: 'Change Organization Owner'),
                                );
                                if (profileSearchResult == null || !context.mounted) {
                                  return;
                                }

                                final newOwnerId =
                                    profileSearchResult.participantModel?.id ?? profileSearchResult.profileModel?.id;
                                if (newOwnerId == null) {
                                  return;
                                }
                                final displayName =
                                    profileSearchResult.participantModel?.displayName ??
                                    profileSearchResult.profileModel?.displayName;

                                final confirmed = await Dialogs.showConfirmationDialog(
                                  context,
                                  title: 'Change organization owner to ${displayName ?? 'this user'}?',
                                  message:
                                      'You will immediately lose access to owner permissions but will automatically get added as an admin',
                                );
                                if (confirmed != true || !context.mounted) {
                                  return;
                                }

                                final success = await context.read<PermissionsManagerViewModel>().changeOrgOwner(
                                  oldOwnerId,
                                  newOwnerId,
                                );
                                if (!context.mounted) {
                                  return;
                                }

                                if (success) {
                                  SnackBars.showInfoSnackBar(
                                    context,
                                    'Organization owner changed, it may take a minute to appear',
                                  );
                                } else {
                                  SnackBars.showInfoSnackBar(context, 'Failed to change organization owner');
                                }
                              },
                              icon: const Icon(Icons.swap_horiz),
                            )
                          : null,
                    );
                  },
                ),
                const SizedBox(height: AppTheme.widgetPadding),
                Center(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.help),
                    label: const Text('Access'),
                    onPressed: () => Dialogs.showPermissionInfoDialog(context, permission: Permissions.orgOwner),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.widgetPadding),
            ContentGroup(
              title: Permissions.billingManager.text,
              subtitle: Permissions.billingManager.description,
              padHorizontally: false,
              child: BlocBuilder<PermissionsManagerViewModel, Map<Permissions, List<ProfileSnippetModel>>>(
                builder: (context, state) {
                  final billingManagerProfile = state[Permissions.billingManager]?.firstOrNull;
                  return Column(
                    children: [
                      if (billingManagerProfile != null)
                        _profileWidget(context, billingManagerProfile, Permissions.billingManager),
                      const SizedBox(height: AppTheme.widgetPadding),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          OutlinedButton.icon(
                            icon: const Icon(Icons.help),
                            label: Text('Access'),
                            onPressed: () =>
                                Dialogs.showPermissionInfoDialog(context, permission: Permissions.billingManager),
                          ),
                          if (billingManagerProfile == null)
                            Padding(
                              padding: const EdgeInsets.only(left: AppTheme.widgetPadding),
                              child: OutlinedButton.icon(
                                icon: Icon(Icons.add),
                                label: Text('Set User'),
                                onPressed: () async {
                                  final profileSearchResult =
                                      await AppRouter.pushFullScreenDialog<ProfileSearchResult?>(
                                        context,
                                        ProfileSearchScreen(title: 'Set ${Permissions.billingManager.text}'),
                                      );
                                  if (profileSearchResult != null && context.mounted) {
                                    final userId =
                                        profileSearchResult.participantModel?.id ??
                                        profileSearchResult.profileModel?.id;
                                    if (userId == null) {
                                      return;
                                    }
                                    final success = await context.read<PermissionsManagerViewModel>().addPermission(
                                      userId,
                                      Permissions.billingManager,
                                    );
                                    if (context.mounted) {
                                      if (success) {
                                        SnackBars.showInfoSnackBar(
                                          context,
                                          'User added, it may take a minute to appear',
                                        );
                                      } else {
                                        SnackBars.showInfoSnackBar(context, 'Failed to add user');
                                      }
                                    }
                                  }
                                },
                              ),
                            ),
                        ],
                      ),
                    ],
                  );
                },
              ),
            ),
            const SizedBox(height: AppTheme.widgetPadding),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
              itemCount: Permissions.values.length - 2,
              itemBuilder: (context, index) => _createPermissionWidget(context, Permissions.values[index + 2]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _createPermissionWidget(BuildContext context, Permissions permission) {
    return ContentGroup(
      title: permission.text,
      subtitle: permission.description,
      padHorizontally: false,
      children: [
        BlocBuilder<PermissionsManagerViewModel, Map<Permissions, List<ProfileSnippetModel>>>(
          builder: (context, state) {
            final permissionProfiles = state[permission];
            if (permissionProfiles == null || permissionProfiles.isEmpty) {
              return const SizedBox.shrink();
            }

            return ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: permissionProfiles.length,
              itemBuilder: (context, index) => _profileWidget(context, permissionProfiles[index], permission),
            );
          },
        ),
        const SizedBox(height: AppTheme.widgetPadding),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            OutlinedButton.icon(
              icon: Icon(Icons.help),
              label: Text('Access'),
              onPressed: () => Dialogs.showPermissionInfoDialog(context, permission: permission),
            ),
            const SizedBox(width: AppTheme.widgetPadding),
            OutlinedButton.icon(
              icon: Icon(Icons.add),
              label: Text('Add User'),
              onPressed: () async {
                final profileSearchResult = await AppRouter.pushFullScreenDialog<ProfileSearchResult?>(
                  context,
                  ProfileSearchScreen(title: 'Add User to ${permission.text}'),
                );
                if (profileSearchResult != null && context.mounted) {
                  final userId = profileSearchResult.participantModel?.id ?? profileSearchResult.profileModel?.id;
                  if (userId == null) {
                    return;
                  }
                  final success = await context.read<PermissionsManagerViewModel>().addPermission(userId, permission);
                  if (context.mounted) {
                    if (success) {
                      SnackBars.showInfoSnackBar(context, 'User added, it may take a minute to appear');
                    } else {
                      SnackBars.showInfoSnackBar(context, 'Failed to add user');
                    }
                  }
                }
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _profileWidget(BuildContext context, ProfileSnippetModel profileSnippet, Permissions permission) {
    return ProfileItem(
      profile: profileSnippet,
      leadingButton: IconButton(
        tooltip: 'Remove User',
        onPressed: () =>
            Dialogs.showConfirmationDialog(
              context,
              title: 'Remove ${profileSnippet.displayName ?? 'this user'} from ${permission.text}?',
            ).then((value) {
              if (value == true && context.mounted) {
                context.read<PermissionsManagerViewModel>().removePermission(profileSnippet.id!, permission);
              }
            }),
        icon: const Icon(Icons.delete),
      ),
    );
  }
}
