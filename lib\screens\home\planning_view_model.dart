import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/con_view_model.dart';

class PlanningViewModel extends Cubit<bool> {
  final ConViewModel conViewModel;

  late final StreamSubscription? _conSubscription;

  int? _conStartMilliseconds;

  PlanningViewModel(ConData conData, this.conViewModel) : super(false) {
    _conSubscription = conViewModel.stream.listen((con) {
      _conStartMilliseconds = con?.startDate?.millisecondsSinceEpoch;
      refresh();
    });
    _conStartMilliseconds = conViewModel.state?.startDate?.millisecondsSinceEpoch;
    refresh();
  }

  @override
  Future<void> close() {
    _conSubscription?.cancel();
    return super.close();
  }

  void refresh() {
    final now = Timestamp.now().millisecondsSinceEpoch;
    emit(_conStartMilliseconds != null && now < _conStartMilliseconds!);
  }
}
