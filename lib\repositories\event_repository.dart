import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/event_import_model.dart';
import 'package:venvi/models/event_model.dart';
import 'package:venvi/utils/logger.dart';

class EventRepository {
  final FirebaseFirestore _firestore;

  EventRepository(this._firestore);

  Future<EventModel?> getEvent(ConData conData, String id, {DataLocation? dataLocation}) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('events')
        .doc(id);
    try {
      final snapshot = await ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));

      if (snapshot.exists) {
        final data = snapshot.data();
        if (data != null) {
          return EventModel.fromJson(data);
        }
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        documentReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<String?> createEvent(ConData conData, EventModel event) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('events');
    try {
      final docRef = await ref.add(event.toJson());
      return docRef.id;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to create doc',
        collectionReference: ref,
      );
      return null;
    }
  }

  Future<bool> batchWriteEvents(
    ConData conData,
    List<EventImportModel> newEvents,
    Map<String, EventImportModel> updatedEvents,
  ) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('events');
    try {
      final batch = _firestore.batch();
      for (final event in newEvents) {
        batch.set(ref.doc(event.id), event.toJson());
      }
      for (final entry in updatedEvents.entries) {
        batch.update(ref.doc(entry.key), entry.value.toJson());
      }
      await batch.commit();
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to batch write docs',
        collectionReference: ref,
      );
      return false;
    }
  }

  Future<bool> updateEvent(ConData conData, String eventId, EventModel event) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('events')
        .doc(eventId);
    try {
      await ref.update(event.toJson());
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to update doc',
        documentReference: ref,
      );
      return false;
    }
  }

  Future<bool> deleteEvent(ConData conData, String eventId) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('events')
        .doc(eventId);
    try {
      await ref.delete();
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to delete doc',
        documentReference: ref,
      );
      return false;
    }
  }
}
