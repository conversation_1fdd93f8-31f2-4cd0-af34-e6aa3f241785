import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/permissions.dart';
import 'package:venvi/json_converters/permission_list_converter.dart';

part 'permission_model.freezed.dart';
part 'permission_model.g.dart';

@freezed
sealed class PermissionModel with _$PermissionModel {
  const factory PermissionModel({String? id, @PermissionListConverter() List<Permissions>? permissions}) =
      _PermissionModel;

  factory PermissionModel.fromJson(Map<String, dynamic> json) => _$PermissionModelFromJson(json);
}
