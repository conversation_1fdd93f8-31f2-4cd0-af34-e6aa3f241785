import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:venvi/models/location_model.dart';
import 'package:venvi/utils/logger.dart';

class LocationRepository {
  final FirebaseFunctions _functions;

  LocationRepository(this._functions);

  Future<List<LocationModel>> getLocations(String query) async {
    try {
      final result = await _functions.httpsCallable('placeApiProxy').call({'search': query});

      final Map<String, dynamic>? data = result.data;
      if (data == null) {
        Logger.error(message: 'Place function data is null');
        return [];
      }

      final places = data['places'];
      if (places == null) {
        Logger.error(message: 'Places are null');
        return [];
      }
      if (places is! List) {
        Logger.error(message: 'Places are not a list');
        return [];
      }

      return places.map((place) {
        final displayName = place['displayName']['text'];
        final formattedAddress = place['formattedAddress'];
        final latitude = place['location']['latitude'];
        final longitude = place['location']['longitude'];
        return LocationModel(name: displayName, address: formattedAddress, geoPoint: GeoPoint(latitude, longitude));
      }).toList();
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace, message: 'Failed to load places: $query');
      return [];
    }
  }

  Future<String?> getTimeZoneId(double latitude, double longitude) async {
    try {
      final result = await _functions.httpsCallable('timeZoneApiProxy').call({
        'lat': latitude.toString(),
        'lng': longitude.toString(),
      });

      final Map<String, dynamic>? data = result.data;
      if (data == null) {
        Logger.error(message: 'Time zone function data is null');
        return null;
      }

      return data['timeZoneId'];
    } catch (e, stackTrace) {
      Logger.error(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get time zone for place: $latitude, $longitude',
      );
      return null;
    }
  }
}
