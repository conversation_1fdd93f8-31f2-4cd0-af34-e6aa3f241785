import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/venue_model.dart';
import 'package:venvi/screens/map/venue_editor_view_model.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/surface_input_field.dart';

class VenueEditorTile extends HookWidget {
  final int index;
  final VenueModel model;
  final VenueEditorViewModel viewModel;

  VenueEditorTile({
    required ConData conData,
    required this.index,
    required this.model,
    required this.viewModel,
  }) : super(key: ValueKey('${conData.conWidgetKey}-${model.id}'));

  @override
  Widget build(BuildContext context) {
    final textController = useTextEditingController(text: model.name);

    return ContentArea(
      padding: EdgeInsets.zero,
      child: ListTile(
        title: <PERSON>In<PERSON><PERSON><PERSON>(
          child: TextField(
            autofocus: model.name == null,
            controller: textController,
            onChanged: (value) => viewModel.updateVenueName(index, value),
            textInputAction: TextInputAction.done,
            textCapitalization: TextCapitalization.words,
            maxLength: InputConstants.maxVenueNameLength,
            decoration: const InputDecoration(
              hintText: 'Venue Name',
              counterText: '',
            ),
          ),
        ),
        trailing: IconButton(
          icon: const Icon(Icons.delete),
          onPressed: () async {
            final confirmDelete = await Dialogs.showConfirmationDialog(
              context,
              title: 'Delete Venue?',
              message: '${model.name ?? 'This'} will be removed from all events scheduled in this venue',
            );
            if (confirmDelete == true && context.mounted) {
              viewModel.removeVenue(index);
            }
          },
        ),
      ),
    );
  }
}
