import 'dart:async';

import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/metadata_field.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/metadata_listener_view_model.dart';
import 'package:venvi/global_view_models/metadata_view_model.dart';
import 'package:venvi/models/venue_model.dart';
import 'package:venvi/repositories/venue_repository.dart';

class VenueViewModel extends MetadataListenerViewModel<VenueModel, List<VenueModel>?> {
  final ConData _conData;
  final VenueRepository _repository;

  VenueViewModel(this._conData, this._repository, MetadataViewModel metadataViewModel)
      : super(null, _conData, metadataViewModel, MetadataField.venues);

  @override
  Future<VenueModel?> getData(String id, DataLocation dataLocation) async {
    return await _repository.getVenue(_conData, id, dataLocation: dataLocation);
  }

  @override
  void onData(Map<String, VenueModel> data) {
    if (isClosed) {
      return;
    }

    final sortedData = data.values.toList();
    sortedData.sort((a, b) => a.name != null
        ? b.name != null
            ? a.name!.compareTo(b.name!)
            : 1
        : -1);
    emit(sortedData);
  }

  VenueModel? getById(String? id) {
    if (id == null) {
      return null;
    }
    try {
      return state?.firstWhere((element) => element.id == id);
    } catch (e) {
      return null;
    }
  }
}
