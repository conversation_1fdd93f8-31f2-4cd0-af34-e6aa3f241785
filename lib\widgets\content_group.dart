import 'package:flutter/material.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/content_area.dart';

class ContentGroup extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final bool additionalTitlePadding;
  final bool padHorizontally;
  final bool padBottom;
  final void Function()? onTap;
  final Widget? child;
  final List<Widget>? children;

  const ContentGroup({
    super.key,
    this.title,
    this.subtitle,
    this.additionalTitlePadding = false,
    this.padHorizontally = true,
    this.padBottom = true,
    this.onTap,
    this.child,
    this.children,
  }) : assert(child == null || children == null);

  @override
  Widget build(BuildContext context) {
    final horizontalPadding = padHorizontally ? AppTheme.screenPadding : 0.0;
    final bottomPadding = padBottom ? AppTheme.screenPadding : 0.0;

    return ContentArea(
      onTap: onTap,
      padding: title != null
          ? EdgeInsets.fromLTRB(
              horizontalPadding,
              AppTheme.widgetPadding,
              horizontalPadding,
              bottomPadding,
            )
          : EdgeInsets.zero,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (title != null)
            Text(
              title!,
              style: Theme.of(context).textTheme.titleSmall,
              textAlign: TextAlign.center,
            ),
          if (subtitle != null)
            Text(
              subtitle!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context)
                        .colorScheme
                        .onPrimaryContainer
                        .withValues(alpha: AppTheme.subtitleTextOpacity),
                  ),
              textAlign: TextAlign.center,
            ),
          if (title != null || subtitle != null)
            SizedBox(height: additionalTitlePadding ? AppTheme.widgetPadding : AppTheme.widgetPaddingSmall),
          if (child != null) child!,
          if (children != null) ...children!,
        ],
      ),
    );
  }
}
