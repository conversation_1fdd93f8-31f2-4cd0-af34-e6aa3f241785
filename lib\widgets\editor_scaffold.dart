import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/logger.dart';
import 'package:venvi/utils/snack_bars.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/glass_tile_button.dart';

class EditorScaffold<
  VM extends EditorScaffoldViewModel<S>,
  S,
  ExternalCubit extends Cubit<ExternalCubitState>,
  ExternalCubitState
>
    extends StatefulWidget {
  /// Whether to alert the user to external conflicts
  final bool checkForExternalConflicts;

  /// Whether or not to wait for external cubit to emit
  final bool waitForExternalCubitOnApply;

  /// Streams changes to the initial state usually by global view model changes
  final ExternalCubit externalCubit;

  /// Builds the state from the stream raw data
  final S? Function(ExternalCubitState externalCubitState) buildState;

  /// Initializes data and builds the view model or null if there was an error
  final VM? Function(S state) initViewModel;

  /// The title of the app bar
  final Widget? title;

  /// Is full-screen dialog app bar with X button instead of back arrow
  final bool isFullScreenDialog;

  /// The message to show when changes are applied
  final String successMessage;

  /// The function to call after changes are applied instead of popping the screen
  final void Function(BuildContext context)? postApplyChangesOverride;

  /// The function to build the content of the scaffold and provides the view model for convenience
  final Widget Function(BuildContext context, VM viewModel, S state) buildContent;

  const EditorScaffold({
    super.key,
    this.checkForExternalConflicts = true,
    this.waitForExternalCubitOnApply = true,
    required this.externalCubit,
    required this.buildState,
    required this.initViewModel,
    required this.title,
    this.isFullScreenDialog = false,
    required this.successMessage,
    required this.buildContent,
    this.postApplyChangesOverride,
  });

  @override
  State<EditorScaffold> createState() => _EditorScaffoldState<VM, S, ExternalCubit, ExternalCubitState>();
}

class _EditorScaffoldState<VM extends EditorScaffoldViewModel<S>, S, C extends Cubit<CS>, CS>
    extends State<EditorScaffold<VM, S, C, CS>> {
  late final StreamSubscription<CS>? _externalCubitSubscription;

  VM? _viewModel;
  bool _listeningForConflicts = true;

  @override
  void initState() {
    final initialState = widget.buildState(widget.externalCubit.state);
    if (initialState != null) {
      _viewModel = widget.initViewModel(initialState);

      if (widget.checkForExternalConflicts) {
        _externalCubitSubscription = widget.externalCubit.stream.listen((event) async {
          if (!_listeningForConflicts) {
            return;
          }

          final streamedState = widget.buildState(event);
          if (streamedState == null) {
            return;
          }

          final hasChanges = _viewModel?.checkChanges(_viewModel!.initialState, streamedState);
          final context = this.context;
          if (hasChanges == true && context.mounted) {
            await Dialogs.showInfoDialog(
              context,
              title: 'Changes Detected',
              message: 'Data has been updated\n\nThis may be from another user\nor an automated process',
            );

            if (context.mounted) {
              context.pop();
            }
          }
        });
      } else {
        _externalCubitSubscription = null;
      }
    }

    if (mounted) {
      setState(() {});
    }
    super.initState();
  }

  @override
  void dispose() {
    _externalCubitSubscription?.cancel();
    _viewModel?.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_viewModel == null) {
      return Scaffold(
        appBar: GlassAppBar(title: widget.title, isFullScreenDialog: widget.isFullScreenDialog),
        body: const ContentArea(fillWidth: false, child: Text('Could not load data')),
      );
    }

    return BlocProvider.value(
      value: _viewModel!,
      child: BlocBuilder<VM, S>(
        builder: (context, state) {
          final viewModel = context.read<VM>();
          return PopScope(
            canPop: !viewModel.hasChanges,
            onPopInvokedWithResult: (didPop, result) async {
              if (didPop) {
                return;
              }
              FocusManager.instance.primaryFocus?.unfocus();
              if (viewModel.hasChanges) {
                return await showDialog(
                  context: context,
                  builder: (_) => BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: AppTheme.dialogBlurSigma, sigmaY: AppTheme.dialogBlurSigma),
                    child: AlertDialog(
                      actionsPadding: EdgeInsets.zero,
                      actions: [
                        GlassTileButton(
                          text: 'Apply Changes',
                          icon: Icons.check,
                          foregroundColor: Theme.of(context).colorScheme.onTertiaryContainer,
                          onTap: () async {
                            Navigator.of(context, rootNavigator: true).pop();
                            await _applyChanges(context, viewModel);
                          },
                        ),
                        GlassTileButton(
                          text: 'Discard Changes',
                          icon: Icons.undo,
                          foregroundColor: Theme.of(context).colorScheme.onTertiaryContainer,
                          onTap: () {
                            Navigator.of(context, rootNavigator: true).pop();
                            context.pop();
                          },
                        ),
                        GlassTileButton(
                          text: 'Cancel',
                          icon: Icons.close,
                          foregroundColor: Theme.of(context).colorScheme.onTertiaryContainer,
                          onTap: () => Navigator.of(context, rootNavigator: true).pop(),
                        ),
                      ],
                    ),
                  ),
                );
              } else {
                context.pop();
              }
            },
            child: Scaffold(
              appBar: GlassAppBar(title: widget.title, isFullScreenDialog: widget.isFullScreenDialog),
              body: Column(
                children: [
                  Expanded(child: widget.buildContent(context, viewModel, state)),
                  if (viewModel.hasChanges)
                    Padding(
                      padding: const EdgeInsets.all(AppTheme.screenPadding),
                      child: ContentArea(
                        padding: EdgeInsets.zero,
                        child: GlassTileButton(
                          text: 'Apply Changes',
                          icon: Icons.check,
                          isAccent: true,
                          isCompact: true,
                          onTap: () => _applyChanges(context, viewModel),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _applyChanges(BuildContext context, VM viewModel) async {
    FocusManager.instance.primaryFocus?.unfocus();
    _listeningForConflicts = false;
    Dialogs.showLoadingDialog(context);

    final awaitedFuture = widget.waitForExternalCubitOnApply ? widget.externalCubit.stream.first : Future.value();
    final applyFuture = viewModel.applyChanges(viewModel.initialState, viewModel.state);

    try {
      final returnState = await applyFuture;
      // Waits for change to take place
      await awaitedFuture;

      if (context.mounted) {
        Navigator.of(context, rootNavigator: true).pop();
        SnackBars.showInfoSnackBar(context, widget.successMessage);
        if (widget.postApplyChangesOverride != null) {
          widget.postApplyChangesOverride!(context);
        } else {
          context.pop(returnState);
        }
        return;
      }
    } catch (e, stackTrace) {
      if (e is EditorScaffoldException && context.mounted) {
        Navigator.of(context, rootNavigator: true).pop();
        awaitedFuture.then((_) => _listeningForConflicts = true);
        Dialogs.showErrorDialog(context, message: e.toString());
      } else {
        Logger.error(exception: e, stackTrace: stackTrace, message: 'Failed to apply changes');
      }
    }
  }
}
