// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/cloud_message_type.dart';

part 'cloud_message_announcement_notification_model.freezed.dart';
part 'cloud_message_announcement_notification_model.g.dart';

@freezed
sealed class CloudMessageAnnouncementNotificationModel with _$CloudMessageAnnouncementNotificationModel {
  const factory CloudMessageAnnouncementNotificationModel({
    @JsonKey(unknownEnumValue: JsonKey.nullForUndefinedEnumValue) CloudMessageType? messageType,
    String? orgId,
    String? conId,
    String? announcementId,
  }) = _CloudMessageAnnouncementNotificationModel;

  factory CloudMessageAnnouncementNotificationModel.fromJson(Map<String, dynamic> json) =>
      _$CloudMessageAnnouncementNotificationModelFromJson(json);
}
