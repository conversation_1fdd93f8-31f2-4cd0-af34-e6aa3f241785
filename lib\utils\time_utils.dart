import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'package:timezone/timezone.dart';
import 'package:venvi/models/location_model.dart';

class TimeUtils {
  /// yMMMd -> Jan 1, 2022
  static String? printDateWithYear(LocationModel? locationModel, Timestamp? timestamp) {
    final conDateTime = convertToConTimeZone(locationModel, timestamp);
    if (conDateTime == null) {
      return null;
    }

    return DateFormat.yMMMd().format(conDateTime);
  }

  /// MMMEd -> Mon, Jan 1
  static String? printDate(LocationModel? locationModel, Timestamp? timestamp) {
    final conDateTime = convertToConTimeZone(locationModel, timestamp);
    if (conDateTime == null) {
      return null;
    }

    return DateFormat.MMMEd().format(conDateTime);
  }

  /// E -> Mon
  static String? printWeekday(LocationModel? locationModel, Timestamp? timestamp) {
    final conDateTime = convertToConTimeZone(locationModel, timestamp);
    if (conDateTime == null) {
      return null;
    }

    return DateFormat.E().format(conDateTime);
  }

  /// d -> 28
  static String? printDayNumber(LocationModel? locationModel, Timestamp? timestamp) {
    final conDateTime = convertToConTimeZone(locationModel, timestamp);
    if (conDateTime == null) {
      return null;
    }

    return DateFormat.d().format(conDateTime);
  }

  /// jm -> 5:08 PM
  static String? printTimeOfDay(LocationModel? locationModel, Timestamp? timestamp) {
    final conDateTime = convertToConTimeZone(locationModel, timestamp);
    if (conDateTime == null) {
      return null;
    }

    return DateFormat.jm().format(conDateTime);
  }

  static String? exportToCsvDate(LocationModel? locationModel, Timestamp? timestamp) {
    final conDateTime = convertToConTimeZone(locationModel, timestamp);
    if (conDateTime == null) {
      return null;
    }

    return DateFormat('yyyy-MM-dd').format(conDateTime);
  }

  static String? exportToCsvTimeOfDay(LocationModel? locationModel, Timestamp? timestamp) {
    final conDateTime = convertToConTimeZone(locationModel, timestamp);
    if (conDateTime == null) {
      return null;
    }

    return DateFormat('HH:mm').format(conDateTime);
  }

  static Timestamp? importFromCsvDateTime(LocationModel? locationModel, String? dateString, String? timeString) {
    final timeZoneId = locationModel?.timeZoneId;
    if (timeZoneId == null || dateString == null || timeString == null) {
      return null;
    }

    final location = getLocation(timeZoneId);
    final dateTime = DateFormat('yyyy-MM-dd HH:mm').parse('$dateString $timeString');
    return Timestamp.fromDate(TZDateTime.from(dateTime, location));
  }

  static TZDateTime? convertToConTimeZone(LocationModel? locationModel, Timestamp? timestamp) {
    final timeZoneId = locationModel?.timeZoneId;
    if (timestamp == null || timeZoneId == null) {
      return null;
    }
    final location = getLocation(timeZoneId);
    return TZDateTime.fromMillisecondsSinceEpoch(location, timestamp.millisecondsSinceEpoch);
  }

  /// Returns a TZDateTime with only year, month, day, and an optional hour
  static TZDateTime? getConDay(LocationModel? locationModel, Timestamp? timestamp, {int hour = 0}) {
    final conDateTime = convertToConTimeZone(locationModel, timestamp);
    if (conDateTime == null) {
      return null;
    }
    return TZDateTime(conDateTime.location, conDateTime.year, conDateTime.month, conDateTime.day, hour);
  }

  /// Returns a TZDateTime with only year, month, day
  static TZDateTime? createConDateTime(
    LocationModel? locationModel, {
    required int year,
    int month = 1,
    int day = 1,
    int hour = 0,
    int minute = 0,
  }) {
    final timeZoneId = locationModel?.timeZoneId;
    if (timeZoneId == null) {
      return null;
    }
    final location = getLocation(timeZoneId);

    return TZDateTime(location, year, month, day, hour, minute);
  }
}
