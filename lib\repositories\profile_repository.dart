import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/models/profile_model.dart';
import 'package:venvi/utils/logger.dart';
import 'package:venvi/utils/search_util.dart';

class ProfileRepository {
  final FirebaseFirestore _firestore;

  ProfileRepository(this._firestore);

  Future<List<ProfileModel>?> searchForProfile(String searchTerm, int limit) async {
    final sanitizedSearch = SearchUtil.sanitizeForFirestoreSearch(searchTerm);
    final query = _firestore
        .collection('profiles')
        .where('searchUsername', isGreaterThanOrEqualTo: sanitizedSearch)
        .where('searchUsername', isLessThan: '$sanitizedSearch\uf8ff')
        .orderBy('searchUsername')
        .limit(limit);
    try {
      final usernameSnapshot = await query.get();
      final results =
          usernameSnapshot.docs
              .map((doc) {
                try {
                  return ProfileModel.fromJson(doc.data());
                } catch (e, stackTrace) {
                  Logger.error(exception: e, stackTrace: stackTrace);
                  return null;
                }
              })
              .whereType<ProfileModel>()
              .toList();

      if (results.length < limit) {
        final displayNameSnapshot =
            await _firestore
                .collection('profiles')
                .where('searchDisplayName', isGreaterThanOrEqualTo: sanitizedSearch)
                .where('searchDisplayName', isLessThan: '$sanitizedSearch\uf8ff')
                .orderBy('searchDisplayName')
                .limit(limit - results.length)
                .get();

        results.addAll(
          displayNameSnapshot.docs
              .map((doc) {
                try {
                  return ProfileModel.fromJson(doc.data());
                } catch (e, stackTrace) {
                  Logger.error(exception: e, stackTrace: stackTrace);
                  return null;
                }
              })
              .whereType<ProfileModel>()
              .toList(),
        );
      }

      return results.toSet().toList();
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to search for profile',
        query: query,
      );
      return null;
    }
  }

  Future<ProfileModel?> getProfileByUsername(String username) async {
    final sanitizedSearch = SearchUtil.sanitizeForFirestoreSearch(username);
    final query = _firestore.collection('profiles').where('searchUsername', isEqualTo: sanitizedSearch).limit(1);
    try {
      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        final doc = snapshot.docs.first;
        return ProfileModel.fromJson(doc.data());
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get profile by username',
        query: query,
      );
      return null;
    }
  }

  Future<bool> checkIfUsernameExists(String username) async {
    final sanitizedSearch = SearchUtil.sanitizeForFirestoreSearch(username);
    final query = _firestore.collection('profiles').where('searchUsername', isEqualTo: sanitizedSearch).limit(1);
    try {
      final snapshot = await query.get();

      return snapshot.docs.isNotEmpty;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to check if username exists',
        query: query,
      );
      return false;
    }
  }

  Stream<ProfileModel?> getProfileStream(String userId) {
    final ref = _firestore.collection('profiles').doc(userId);
    try {
      return ref.snapshots().map((snapshot) {
        if (snapshot.exists) {
          final data = snapshot.data();
          if (data != null) {
            return ProfileModel.fromJson(data);
          } else {
            return null;
          }
        }
        return const ProfileModel();
      });
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc stream',
        documentReference: ref,
      );
      return Stream.value(null);
    }
  }

  Future<ProfileModel?> getProfile(String userId, {DataLocation? dataLocation}) async {
    final ref = _firestore.collection('profiles').doc(userId);
    try {
      final snapshot = await ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));

      if (snapshot.exists) {
        final data = snapshot.data();
        if (data != null) {
          return ProfileModel.fromJson(data);
        }
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        documentReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<bool> updateProfile(String userId, ProfileModel profile) async {
    final ref = _firestore.collection('profiles').doc(userId);
    try {
      await ref.update(profile.toJson());
      return true;
    } catch (e, stackTrace) {
      if (e is FirebaseException && e.code == 'not-found') {
        // New profile
        await _firestore.collection('profiles').doc(userId).set(profile.toJson(), SetOptions(merge: true));
        return true;
      } else {
        Logger.firestoreError(
          exception: e,
          stackTrace: stackTrace,
          message: 'Failed to update doc',
          documentReference: ref,
        );
      }
    }
    return false;
  }

  Future<bool> updateProfileFromAuth(User? user) async {
    final userId = user?.uid;
    final ref = _firestore.collection('profiles').doc(userId);
    try {
      if (user == null || userId == null || user.isAnonymous) {
        return false;
      }

      final providerData = user.providerData.first;
      final currentProfile = await getProfile(userId);
      final Map<String, dynamic> newProfileFields = {};
      final authUserDisplayName = (providerData.displayName?.isEmpty ?? true) ? null : providerData.displayName;
      final currentDisplayNameEmpty = currentProfile?.displayName?.isEmpty ?? true;
      final currentUsernameEmpty = currentProfile?.username?.isEmpty ?? true;

      if (currentDisplayNameEmpty && authUserDisplayName != null) {
        newProfileFields['displayName'] = authUserDisplayName;
      }
      if (currentUsernameEmpty) {
        String createdUsername =
            authUserDisplayName?.replaceAll(InputConstants.usernameRegEx, '') ?? _generateUsername();
        bool usernameExists = await checkIfUsernameExists(createdUsername);
        while (usernameExists) {
          createdUsername = _generateUsername();
          usernameExists = await checkIfUsernameExists(createdUsername);
        }
        newProfileFields['username'] = createdUsername;
      }
      // Second chance to fill display name
      if (currentDisplayNameEmpty && newProfileFields['displayName'] == null) {
        // Tries using the new username since it was verified to be unique
        newProfileFields['displayName'] = newProfileFields['username'] ?? _generateUsername();
      }
      if (currentProfile?.photoUrl == null && (providerData.photoURL?.isNotEmpty ?? false)) {
        newProfileFields['photoUrl'] = providerData.photoURL;
      }

      if (newProfileFields.isEmpty) {
        return true;
      }

      if (currentProfile == null) {
        await ref.set(newProfileFields, SetOptions(merge: true));
      } else {
        await ref.update(newProfileFields);
      }

      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to update profile from auth',
        documentReference: ref,
      );
      return false;
    }
  }

  String _generateUsername() {
    const chars = '0123456789abcdefghijklmnopqrstuvwxyz';
    final rand = Random();
    return 'user-${List.generate(8, (_) => chars[rand.nextInt(chars.length)]).join()}';
  }
}
