import 'package:freezed_annotation/freezed_annotation.dart';

part 'event_participant_model.freezed.dart';
part 'event_participant_model.g.dart';

@freezed
sealed class EventParticipantModel with _$EventParticipantModel {
  const factory EventParticipantModel({String? participantId, String? roleId}) = _EventParticipantModel;

  factory EventParticipantModel.fromJson(Map<String, dynamic> json) => _$EventParticipantModelFromJson(json);
}
