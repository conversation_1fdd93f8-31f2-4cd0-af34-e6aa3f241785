import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_network_image_platform_interface/cached_network_image_platform_interface.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/global_view_models/auth_view_model.dart';
import 'package:venvi/router.dart';

class MyAccountIconButton extends StatelessWidget {
  const MyAccountIconButton({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthViewModel, User?>(
      builder: (context, state) => IconButton(
        onPressed: () => AppRouter.pushNamedRoute(context, 'settings'),
        icon: state == null || state.isAnonymous
            ? const Icon(Icons.account_circle_outlined)
            : state.photoURL == null
                ? const Icon(Icons.account_circle)
                : CachedNetworkImage(
                    imageRenderMethodForWeb: ImageRenderMethodForWeb.HttpGet,
                    imageUrl: state.photoURL!,
                    placeholder: (context, url) => const Icon(Icons.account_circle),
                    errorWidget: (context, url, error) => const Icon(Icons.account_circle),
                    imageBuilder: (context, imageProvider) => ClipOval(
                      clipBehavior: Clip.hardEdge,
                      child: Image(
                        image: imageProvider,
                        width: 20,
                        height: 20,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
      ),
    );
  }
}
