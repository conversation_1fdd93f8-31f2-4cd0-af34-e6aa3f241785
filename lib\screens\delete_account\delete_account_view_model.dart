import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/screens/delete_account/delete_account_state.dart';

class DeleteAccountViewModel extends Cubit<DeleteAccountState> {
  final AuthRepository _authRepository;

  bool _reAuthed = false;

  DeleteAccountViewModel(this._authRepository) : super(const DeleteAccountState(canDelete: false));

  void reAuth(User? user) {
    _reAuthed = user != null;
    emit(DeleteAccountState(canDelete: _reAuthed));
  }

  Future<bool> deleteAccount() async {
    if (_reAuthed) {
      return await _authRepository.deleteAccount();
    }
    return false;
  }
}
