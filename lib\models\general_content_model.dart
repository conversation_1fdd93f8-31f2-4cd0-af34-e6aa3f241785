import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/general_content_tile_model.dart';
import 'package:venvi/models/image_model.dart';

part 'general_content_model.freezed.dart';
part 'general_content_model.g.dart';

@freezed
sealed class GeneralContentModel with _$GeneralContentModel {
  const factory GeneralContentModel({
    String? id,
    int? orderIndex,
    String? title,
    ImageModel? primaryImage,
    List<String>? tileImageIds,
    Map<int, GeneralContentTileModel>? tiles,
  }) = _GeneralContentModel;

  factory GeneralContentModel.fromJson(Map<String, dynamic> json) => _$GeneralContentModelFromJson(json);
}
