import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/utils/logger.dart';

enum LoadingState { notStarted, loading, complete, error }

class BottomNavLoadingViewModel extends Cubit<LoadingState> {
  final List<Future> _loadFutures;

  bool _initiated = false;

  BottomNavLoadingViewModel(this._loadFutures) : super(LoadingState.notStarted);

  void load() {
    if (!_initiated) {
      _initiated = true;
      emit(LoadingState.loading);

      Future.wait(_loadFutures, eagerError: true).then(
        (value) {
          if (!isClosed) {
            emit(LoadingState.complete);
          }
        },
        onError: (error, stackTrace) {
          Logger.error(exception: error, stackTrace: stackTrace, message: 'Error loading bottom navigation data');
          if (!isClosed) {
            emit(LoadingState.error);
          }
        },
      );
    }
  }
}
