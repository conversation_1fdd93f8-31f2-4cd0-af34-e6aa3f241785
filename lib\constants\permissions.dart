enum Permissions {
  orgOwner(
    text: 'Owner',
    description: 'Can manage all aspects of the organization',
    access: [
      'Create new cons',
      'Edit active con',
      'Publish/unpublish cons',
      'Request invoices',
      'Everything other roles are allowed',
    ],
  ),
  billingManager(
    text: 'Billing Manager',
    description: 'User that invoices are sent to\nIf no billing manager is set, invoices are sent to the org owner',
    access: ['Pay invoices', 'View unpublished cons', 'View analytics screen'],
  ),
  orgAdmin(
    text: 'Admin',
    description: 'Can manage all aspects of the organization besides billing',
    access: [
      'Edit con',
      'Edit events',
      'Edit event properties',
      'Edit roles/participants',
      'Edit venues',
      'Edit maps',
      'Edit general content',
      'Edit reusable event images',
      'Edit home screen photo gallery',
      'Import/export schedule',
      'Make announcements',
      'View unpublished cons',
      'View analytics screen',
    ],
  ),
  eventEditor(
    text: 'Event Editor',
    description: 'Manages events and event properties',
    access: [
      'Edit events',
      'Edit event properties',
      'Edit roles/participants',
      'Edit reusable event images',
      'Import/export schedule',
      'View unpublished cons',
      'View analytics screen',
    ],
  ),
  announcementCreator(
    text: 'Announcement Creator',
    description: 'Creates announcements',
    access: ['Make announcements'],
  ),
  photographer(
    text: 'Photographer',
    description: 'Manages the con\'s photo gallery',
    access: ['Edit home screen photo gallery'],
  ),
  analyticsViewer(text: 'Analytics Viewer', description: 'View analytics', access: ['View analytics screen']);

  final String text;
  final String description;
  final List<String> access;

  const Permissions({required this.text, required this.description, required this.access});
}
