import 'package:flutter/material.dart';
import 'package:venvi/theme.dart';

class InputFieldLabel extends StatelessWidget {
  final String label;
  final bool isTertiary;

  const InputFieldLabel({
    super.key,
    required this.label,
    this.isTertiary = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(
        AppTheme.widgetPaddingSmall,
        0,
        AppTheme.widgetPaddingSmall,
        AppTheme.widgetPaddingVerySmall,
      ),
      child: Text(
        label,
        style: Theme.of(context).textTheme.labelMedium?.copyWith(
              color: isTertiary
                  ? Theme.of(context).colorScheme.onTertiaryContainer
                  : Theme.of(context).colorScheme.onPrimaryContainer,
            ),
      ),
    );
  }
}
