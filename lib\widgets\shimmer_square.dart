import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerSquare extends StatelessWidget {
  const ShimmerSquare({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Theme.of(context).colorScheme.primaryContainer,
      highlightColor: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.5),
      child: const ColoredBox(
        color: Colors.grey,
      ),
    );
  }
}
