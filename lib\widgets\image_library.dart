import 'package:flutter/material.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/adaptive_view.dart';
import 'package:venvi/widgets/image_updater_view.dart';

class ImageLibrary extends StatelessWidget {
  final IconData icon;
  final List<ImageModel> images;
  final Function(ImageModel image) onSelect;

  const ImageLibrary({
    super.key,
    this.icon = Icons.edit,
    required this.images,
    required this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    return AdaptiveView(
      desktopView: _createImageGrid(images, true),
      mobileView: _createImageGrid(images, false),
    );
  }

  Widget _createImageGrid(List<ImageModel> images, bool isDesktop) {
    return LayoutBuilder(
      builder: (context, constraints) => GridView.builder(
        shrinkWrap: true,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: constraints.maxWidth ~/ (isDesktop ? 200 : 125),
          crossAxisSpacing: AppTheme.widgetPaddingSmall,
          mainAxisSpacing: AppTheme.widgetPaddingSmall,
        ),
        itemCount: images.length,
        itemBuilder: (context, index) => ImageUpdaterView(
          icon: icon,
          imageUrl: images[index].downloadUrl ?? '',
          onTap: () => onSelect.call(images[index]),
        ),
      ),
    );
  }
}
