// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart' show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAJAuRoR78bTeamE7ppHnjzJqYsXYCNqXM',
    appId: '1:42378229751:web:647178e554cb516c8620f2',
    messagingSenderId: '42378229751',
    projectId: 'venvi-a83ac',
    authDomain: 'venvi-a83ac.firebaseapp.com',
    storageBucket: 'venvi-a83ac.appspot.com',
    measurementId: 'G-WGM0RQVDB0',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDWoJlfwA5epKrm2cOB95VLsQm2d7oWFjs',
    appId: '1:42378229751:android:2792858b394fbdd48620f2',
    messagingSenderId: '42378229751',
    projectId: 'venvi-a83ac',
    storageBucket: 'venvi-a83ac.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDo96pBoEpfHd-mq4uQCXH47mnKtKwfR1Q',
    appId: '1:42378229751:ios:7a4cde92cf797b858620f2',
    messagingSenderId: '42378229751',
    projectId: 'venvi-a83ac',
    storageBucket: 'venvi-a83ac.appspot.com',
    androidClientId: '42378229751-7topch2gu1vl1t1h4umg79b4h4nvj8ut.apps.googleusercontent.com',
    iosClientId: '42378229751-65459hdk9cgb46q3rjpmgk5f5umig3h7.apps.googleusercontent.com',
    iosBundleId: 'app.venvi.venvi',
  );
}
