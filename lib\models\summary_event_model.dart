import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/json_converters/timestamp_converter.dart';
import 'package:venvi/models/venue_snippet_model.dart';

part 'summary_event_model.freezed.dart';
part 'summary_event_model.g.dart';

@freezed
sealed class SummaryEventModel with _$SummaryEventModel {
  const factory SummaryEventModel({
    String? id,
    bool? spotlight,
    String? title,
    VenueSnippetModel? venue,
    @TimestampConverter() Timestamp? startTime,
    @TimestampConverter() Timestamp? endTime,
    bool? adultOnly,
  }) = _SummaryEventModel;

  factory SummaryEventModel.fromJson(Map<String, dynamic> json) => _$SummaryEventModelFromJson(json);
}
