name: venvi
publish_to: "none"

version: 1.13.4+87

environment:
  sdk: ^3.8.0

dependencies:
  flutter:
    sdk: flutter

  # Utils
  intl: 0.20.2
  async: 2.13.0
  http: 1.4.0
  uuid: 4.5.1

  # General
  provider: 6.1.5
  flutter_bloc: 9.1.1
  equatable: 2.0.7
  go_router: 15.1.2
  flutter_hooks: 0.21.2
  package_info_plus: 8.3.0
  url_launcher: 6.3.1
  timezone: 0.10.1
  mime: 2.0.0
  flutter_local_notifications: 19.2.1
  permission_handler: 12.0.0+1
  app_links: 6.4.0

  # UI
  cached_network_image: 3.4.1
  cached_network_image_platform_interface: 4.1.1
  visibility_detector: 0.4.0+2
  shimmer: 3.0.0
  flex_color_picker: 3.7.1
  sticky_headers: 0.3.0+2

  # Camera
  image_picker: 1.1.2
  image_cropper: 9.1.0

  # Sharing and file exporting
  share_plus: 11.0.0
  universal_html: 2.2.4
  csv: 6.0.0
  pdf: 3.11.3
  printing: 5.14.2
  file_picker: 10.1.9

  # Local storage
  shared_preferences: 2.5.3

  # Firebase
  firebase_core: 3.13.1
  firebase_auth: 5.5.4
  cloud_firestore: 5.6.8
  cloud_functions: 5.5.1
  firebase_storage: 12.4.6
  firebase_app_check: 0.3.2+6
  firebase_messaging: 15.2.6
  firebase_analytics: 11.4.6

  # Social sign-in
  sign_in_button: 3.2.0
  google_sign_in: 6.3.0

  # Logging
  sentry_flutter: 8.14.2

  # Code generators
  json_annotation: 4.9.0
  freezed_annotation: 3.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: 6.0.0

  # Logging
  sentry_dart_plugin: 2.4.1

  # Code generators
  build_runner: 2.4.15
  json_serializable: 6.9.5
  freezed: 3.0.6

  #  Native generators
  flutter_native_splash: 2.4.6
  flutter_launcher_icons: 0.14.3

sentry:
  project: venvi-app
  org: venvi
  auth_token: 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
  upload_source_maps: true

flutter_launcher_icons:
  image_path: "assets/icon/icon.png"
  android: true
  adaptive_icon_background: "assets/icon/icon_android_background.png"
  adaptive_icon_foreground: "assets/icon/icon_android_foreground.png"
  ios: true
  remove_alpha_ios: true
  image_path_ios_dark_transparent: "assets/icon/icon_ios_dark_mode.png"
  image_path_ios_tinted_grayscale: "assets/icon/icon_ios_tinted.png"
  web:
    generate: true
    image_path: "assets/icon/icon_web.png"
    background_color: "#0000FF"
    theme_color: "#0000FF"

flutter_native_splash:
  color: "#000000"
  android_12:
    color: "#000000"

flutter:
  uses-material-design: true
  assets:
    - assets/icon/icon.png
  fonts:
    - family: Comfortaa
      fonts:
        - asset: assets/fonts/Comfortaa-VariableFont_wght.ttf
    - family: FontAwesomeBrands
      fonts:
        - asset: assets/fonts/fa-brands-400.ttf
