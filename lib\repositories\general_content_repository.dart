import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/general_content_model.dart';
import 'package:venvi/utils/logger.dart';

class GeneralContentRepository {
  final FirebaseFirestore _firestore;

  GeneralContentRepository(this._firestore);

  Future<GeneralContentModel?> getGeneralContent(ConData conData, String id, {DataLocation? dataLocation}) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('generalContent')
        .doc(id);
    try {
      final snapshot = await ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));

      if (snapshot.exists) {
        final data = snapshot.data();
        if (data != null) {
          return GeneralContentModel.fromJson(data);
        }
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        documentReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<bool> updateOrderIndexes(ConData conData, List<String> orderedIds) async {
    final collectionRef = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('generalContent');

    final batch = _firestore.batch();
    for (int i = 0; i < orderedIds.length; i++) {
      final id = orderedIds[i];
      final ref = collectionRef.doc(id);
      batch.update(ref, {'orderIndex': i});
    }
    try {
      await batch.commit();
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to update order indexes',
        collectionReference: collectionRef,
      );
      return false;
    }
  }

  Future<String?> createGeneralContent(ConData conData, GeneralContentModel generalContent) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('generalContent');
    try {
      final docRef = await ref.add(generalContent.toJson());
      return docRef.id;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to create doc',
        collectionReference: ref,
      );
      return null;
    }
  }

  Future<bool> updateGeneralContent(
    ConData conData,
    String generalContentId,
    GeneralContentModel generalContent,
  ) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('generalContent')
        .doc(generalContentId);
    try {
      await ref.update(generalContent.toJson());
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to update doc',
        documentReference: ref,
      );
      return false;
    }
  }

  Future<bool> deleteGeneralContent(ConData conData, String generalContentId) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('generalContent')
        .doc(generalContentId);
    try {
      await ref.delete();
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to delete doc',
        documentReference: ref,
      );
      return false;
    }
  }
}
