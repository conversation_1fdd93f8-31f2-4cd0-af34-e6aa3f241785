import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/event_type.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/global_view_models/event_view_model.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/models/event_model.dart';
import 'package:venvi/models/user_model.dart';
import 'package:venvi/screens/schedule/schedule_search_state.dart';
import 'package:venvi/utils/search_util.dart';

class ScheduleSearchViewModel extends Cubit<ScheduleSearchState> {
  final UserViewModel _userViewModel;
  final EventViewModel _eventViewModel;
  final ConViewModel _conViewModel;

  late final StreamSubscription<UserModel> _userSubscription;
  late final StreamSubscription<List<EventModel>?> _eventSubscription;

  List<EventModel>? _allEvents;

  bool _spotlightOnly = false;
  String _searchTerm = '';
  EventType? _typeFilter;
  String? _venueFilter;
  List<String> _tagsFilter = [];
  bool? _showAdultContent;

  ScheduleSearchViewModel(ConData conData, this._userViewModel, this._eventViewModel, this._conViewModel)
    : super(const ScheduleSearchState(searchTerm: '', spotlightOnly: false, tagFilter: [])) {
    _userSubscription = _userViewModel.stream.listen((state) {
      _showAdultContent = state.showAdultContent;
      _filter();
    });
    _showAdultContent = _userViewModel.state.showAdultContent;

    _eventSubscription = _eventViewModel.stream.listen((state) {
      _allEvents = state;
      _filter();
    });
    _allEvents = _eventViewModel.state;
    _filter();
  }

  @override
  Future<void> close() {
    _userSubscription.cancel();
    _eventSubscription.cancel();
    return super.close();
  }

  DateTime? findClosestTime(List<DateTime> dateTimes) {
    final now = DateTime.now();
    for (final dateTime in dateTimes) {
      if (dateTime.isAfter(now)) {
        return dateTime;
      }
    }
    return null;
  }

  void toggleSpotlightOnly() {
    _spotlightOnly = !_spotlightOnly;
    _filter();
  }

  void setSearchText(String searchText) {
    _searchTerm = searchText.trim();
    _filter();
  }

  void setType(EventType? type) {
    _typeFilter = type;
    _filter();
  }

  void toggleTag(String tag) {
    if (_tagsFilter.contains(tag)) {
      _tagsFilter = _tagsFilter.where((element) => element != tag).toList();
    } else {
      _tagsFilter = [..._tagsFilter, tag];
    }
    _filter();
  }

  void clearFilters() {
    _spotlightOnly = false;
    _typeFilter = null;
    _tagsFilter.clear();

    _filter();
  }

  void setVenueFilter(String? venue) {
    _venueFilter = venue;
    _filter();
  }

  void _filter() {
    if (_allEvents == null) {
      emit(
        ScheduleSearchState(
          events: null,
          searchTerm: _searchTerm,
          spotlightOnly: _spotlightOnly,
          eventTypeFilter: _typeFilter,
          venueFilter: _venueFilter,
          tagFilter: _tagsFilter.toList(),
        ),
      );
      return;
    }

    final filteredEvents = _allEvents!
        .where(
          (event) =>
              event.startTime != null &&
              (!_spotlightOnly || event.spotlight == true) &&
              (_searchTerm.isEmpty ||
                  (event.title != null && SearchUtil.searchParts(event.title, _searchTerm)) ||
                  (event.tags != null && SearchUtil.searchList(event.tags, _searchTerm))) &&
              (_typeFilter == null || event.type == _typeFilter) &&
              (_venueFilter == null || event.venue?.name == _venueFilter) &&
              (_tagsFilter.isEmpty || _tagsFilter.every((tag) => event.tags?.contains(tag) ?? false)) &&
              (event.adultOnly != true || _showAdultContent != false),
        )
        .toList();

    filteredEvents.sort((a, b) => a.startTime!.compareTo(b.startTime!));

    emit(
      ScheduleSearchState(
        events: _eventViewModel.groupEventsByDateAndTime(_conViewModel.state, filteredEvents),
        searchTerm: _searchTerm,
        spotlightOnly: _spotlightOnly,
        eventTypeFilter: _typeFilter,
        venueFilter: _venueFilter,
        tagFilter: _tagsFilter.toList(),
      ),
    );
  }
}
