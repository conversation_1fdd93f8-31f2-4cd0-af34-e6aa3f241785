import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:venvi/constants/rules.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/permission_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/app_bar_logo.dart';
import 'package:venvi/widgets/content_area.dart';

class DesktopNavBar extends StatelessWidget implements PreferredSizeWidget {
  final StatefulNavigationShell navigationShell;

  const DesktopNavBar({super.key, required this.navigationShell});

  @override
  Widget build(BuildContext context) {
    final showAdminTab = context.watch<PermissionViewModel>().isPermitted(Rules.seeAdminScreen);

    return SafeArea(
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isCompact = constraints.maxHeight < 500;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: !isCompact ? const EdgeInsets.all(AppTheme.widgetPadding) : EdgeInsets.zero,
                child: InkWell(
                  onTap: () => context.go('${context.read<ConData>().conPath}/home'),
                  overlayColor: WidgetStateProperty.all(Colors.transparent),
                  child: const AppBarLogo(),
                ),
              ),
              TextButton.icon(
                label: const Text('Con Search'),
                icon: const Icon(Icons.manage_search),
                onPressed: () => context.go('/home'),
                style: Theme.of(context).textButtonTheme.style?.copyWith(
                  padding: WidgetStateProperty.all(const EdgeInsets.symmetric(horizontal: AppTheme.widgetPadding)),
                  overlayColor: WidgetStateProperty.all(Theme.of(context).colorScheme.primaryContainer),
                  foregroundColor: WidgetStateProperty.resolveWith((states) {
                    if (states.contains(WidgetState.hovered) || states.contains(WidgetState.focused)) {
                      return Theme.of(context).colorScheme.onPrimaryContainer;
                    }
                    return Theme.of(context).appBarTheme.actionsIconTheme?.color;
                  }),
                ),
              ),
              if (!isCompact) const SizedBox(height: AppTheme.widgetPaddingLarge * 2),
              ContentArea(
                fillWidth: false,
                padding:
                    isCompact
                        ? const EdgeInsets.symmetric(horizontal: AppTheme.widgetPadding)
                        : const EdgeInsets.all(AppTheme.widgetPadding),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _desktopNavButton(context, 0, Icons.home, Icons.home_outlined, 'Home'),
                    if (!isCompact) const SizedBox(height: AppTheme.widgetPadding),
                    _desktopNavButton(context, 1, Icons.favorite, Icons.favorite_border, 'Favorites'),
                    if (!isCompact) const SizedBox(height: AppTheme.widgetPadding),
                    _desktopNavButton(context, 2, Icons.calendar_today, Icons.calendar_today_outlined, 'Schedule'),
                    if (!isCompact) const SizedBox(height: AppTheme.widgetPadding),
                    _desktopNavButton(context, 3, Icons.map, Icons.map_outlined, 'Map'),
                    if (showAdminTab && !isCompact) const SizedBox(height: AppTheme.widgetPadding),
                    if (showAdminTab) _desktopNavButton(context, 4, Icons.edit, Icons.edit_outlined, 'Admin'),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _desktopNavButton(BuildContext context, int index, IconData activeIcon, IconData icon, String label) {
    if (navigationShell.currentIndex == index) {
      return ElevatedButton.icon(
        style: ElevatedButton.styleFrom(padding: const EdgeInsets.symmetric(horizontal: AppTheme.widgetPadding)),
        onPressed: () => _onPressed(context, index),
        icon: Icon(activeIcon),
        label: Text(label),
      );
    }
    return TextButton.icon(
      style: TextButton.styleFrom(padding: const EdgeInsets.symmetric(horizontal: AppTheme.widgetPadding)),
      onPressed: () => _onPressed(context, index),
      icon: Icon(icon),
      label: Text(label),
    );
  }

  void _onPressed(BuildContext context, int index) {
    FocusManager.instance.primaryFocus?.unfocus();
    navigationShell.goBranch(index, initialLocation: index == navigationShell.currentIndex);
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
