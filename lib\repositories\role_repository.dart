import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/role_model.dart';
import 'package:venvi/utils/logger.dart';

class RoleRepository {
  final FirebaseFirestore _firestore;

  RoleRepository(this._firestore);

  Future<RoleModel?> getRole(ConData conData, String roleId, {DataLocation? dataLocation}) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('roles')
        .doc(roleId);
    try {
      final snapshot = await ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));

      if (snapshot.exists) {
        final data = snapshot.data();
        if (data != null) {
          return RoleModel.fromJson(data);
        }
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        documentReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<bool> updateOrderIndexes(ConData conData, List<String> orderedIds) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('roles');
    final batch = _firestore.batch();
    for (int i = 0; i < orderedIds.length; i++) {
      final id = orderedIds[i];
      batch.update(ref.doc(id), {'orderIndex': i});
    }
    try {
      await batch.commit();
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to update order indexes',
        collectionReference: ref,
      );
      return false;
    }
  }

  Future<bool> updateName(ConData conData, String roleId, String name) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('roles')
        .doc(roleId);
    try {
      await ref.update({'name': name});
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to update role name',
        documentReference: ref,
      );
      return false;
    }
  }

  Future<String?> createRole(ConData conData, RoleModel role) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('roles');
    try {
      final docRef = await ref.add(role.toJson());
      return docRef.id;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to add doc',
        collectionReference: ref,
      );
      return null;
    }
  }

  Future<bool> updateRole(ConData conData, RoleModel role) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('roles')
        .doc(role.id);
    try {
      await ref.update(role.toJson());
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to update doc',
        documentReference: ref,
      );
      return false;
    }
  }

  Future<bool> deleteRole(ConData conData, String roleId) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('roles')
        .doc(roleId);
    try {
      await ref.delete();
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to delete doc',
        documentReference: ref,
      );
      return false;
    }
  }
}
