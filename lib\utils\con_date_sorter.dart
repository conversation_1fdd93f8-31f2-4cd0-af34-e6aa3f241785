import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:venvi/models/con_model.dart';

abstract class ConDateSorter {
  // Sorts the list by the soonest upcoming, then most recent in past, with null at end
  static void sortResults(List<ConModel> cons) {
    final now = Timestamp.now().millisecondsSinceEpoch;
    cons.sort((a, b) {
      final aStart = a.startDate;
      final bStart = b.startDate;
      // Move nulls to the bottom
      if (aStart == null && bStart == null) {
        return 0;
      }
      if (aStart == null) {
        return 1;
      }
      if (bStart == null) {
        return -1;
      }

      // Sort upcoming dates (soonest to furthest)
      if (aStart.millisecondsSinceEpoch > now && bStart.millisecondsSinceEpoch > now) {
        return aStart.compareTo(bStart);
      }

      // Sort past dates (most recent to furthest)
      if (aStart.millisecondsSinceEpoch < now && bStart.millisecondsSinceEpoch < now) {
        return -aStart.compareTo(bStart);
      }

      // Sort upcoming dates before past dates
      if (aStart.millisecondsSinceEpoch > now && bStart.millisecondsSinceEpoch < now) {
        return -1;
      }

      // Sort past dates after upcoming dates
      if (aStart.millisecondsSinceEpoch < now && bStart.millisecondsSinceEpoch > now) {
        return 1;
      }

      // Default case (no sorting needed)
      return 0;
    });
  }
}
