import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/models/base_profile.dart';
import 'package:venvi/repositories/profile_repository.dart';

class ProfileStreamCubit extends Cubit<BaseProfile?> {
  late final StreamSubscription? _participantSubscription;
  late final StreamSubscription _profileSubscription;

  ProfileStreamCubit(ProfileRepository profileRepository, ParticipantViewModel? participantViewModel, String userId)
      : super(null) {
    final participant = participantViewModel?.getParticipant(userId);
    if (participant != null) {
      emit(participant);
      _participantSubscription = participantViewModel!.stream.listen((event) {
        try {
          final participant = event?.firstWhere((element) => element.id == userId);
          emit(participant);
        } catch (e) {
          emit(null);
        }
      });
    } else {
      _participantSubscription = null;
    }

    bool skipFirst = participant != null; // Will only use stream if checking for changes in back-end
    // First caches the latest data
    profileRepository.getProfile(userId, dataLocation: DataLocation.server).then(
      (value) {
        _profileSubscription = profileRepository.getProfileStream(userId).listen((event) {
          if (skipFirst) {
            skipFirst = false;
            return;
          }
          emit(event);
        });
      },
    );
  }

  @override
  Future<void> close() {
    _participantSubscription?.cancel();
    _profileSubscription.cancel();
    return super.close();
  }
}
