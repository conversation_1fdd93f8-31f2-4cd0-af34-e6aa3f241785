import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/models/org_model.dart';
import 'package:venvi/utils/logger.dart';
import 'package:venvi/utils/search_util.dart';

class OrgRepository {
  final FirebaseFirestore _firestore;

  OrgRepository(this._firestore);

  Future<OrgModel?> getOrg(String id, {DataLocation? dataLocation}) async {
    final ref = _firestore.collection('orgs').doc(id);
    try {
      final snapshot = await ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));

      if (snapshot.exists) {
        final data = snapshot.data();
        if (data != null) {
          return OrgModel.fromJson(data);
        }
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        documentReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<List<OrgModel>?> searchForOrgs(String searchTerm, int limit) async {
    final sanitizedSearch = SearchUtil.sanitizeForFirestoreSearch(searchTerm);
    final query = _firestore
        .collection('orgs')
        .where('isSearchable', isEqualTo: true)
        .where('activeCon.searchName', isGreaterThanOrEqualTo: sanitizedSearch)
        .where('activeCon.searchName', isLessThan: '$sanitizedSearch\uf8ff')
        .orderBy('activeCon.searchName')
        .limit(limit);
    try {
      final snapshot = await query.get();

      return snapshot.docs
          .map((doc) {
            try {
              return OrgModel.fromJson(doc.data());
            } catch (e, stackTrace) {
              Logger.error(exception: e, stackTrace: stackTrace);
              return null;
            }
          })
          .whereType<OrgModel>()
          .toList();
    } catch (e, stackTrace) {
      Logger.firestoreError(exception: e, stackTrace: stackTrace, message: 'Failed to get docs', query: query);
      return null;
    }
  }

  Future<bool> updateOrgAndPublishedCons(
    String orgId,
    bool? isSearchable,
    String? activeConId,
    List<String> allConIds,
    List<String> publishedConIds,
  ) async {
    try {
      final orgRef = _firestore.collection('orgs').doc(orgId);
      final batch = _firestore.batch();

      batch.update(orgRef, {'isSearchable': isSearchable, 'activeConId': activeConId});

      for (String conId in allConIds) {
        final conRef = orgRef.collection('cons').doc(conId);
        batch.update(conRef, {'isPublished': publishedConIds.contains(conId)});
      }

      await batch.commit();

      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(exception: e, stackTrace: stackTrace, message: 'Failed to update org and published cons');
      return false;
    }
  }
}
