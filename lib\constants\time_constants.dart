class TimeConstants {
  /// eventDuration >= longRunningEventDuration is considered long running event
  static const Duration longRunningEventDuration = Duration(hours: 6);

  /// Furthest in the future priority operation hours are shown
  static const Duration priorityUpcomingOperationHour = Duration(hours: 1);

  /// Operations hours prioritized because they're ending soon
  static const Duration priorityEndingOperationHour = Duration(hours: 1);

  /// hour of the latest time of day that an event will be shown on the next day
  static const int latestTimeOfDayHourOverlap = 3;
}
