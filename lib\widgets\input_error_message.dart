import 'package:flutter/material.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/accent_label.dart';

class InputErrorMessage extends StatelessWidget {
  final String? message;

  const InputErrorMessage({super.key, this.message});

  @override
  Widget build(BuildContext context) {
    return message?.isNotEmpty == true
        ? Padding(
          padding: const EdgeInsets.only(top: AppTheme.widgetPaddingVerySmall),
          child: AccentLabel(text: message!, isOnContainer: true),
        )
        : const SizedBox.shrink();
  }
}
