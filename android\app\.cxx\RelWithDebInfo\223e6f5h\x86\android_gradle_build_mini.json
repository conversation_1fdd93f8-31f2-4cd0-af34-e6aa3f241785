{"buildFiles": ["C:\\Projects\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Projects\\venvi\\android\\app\\.cxx\\RelWithDebInfo\\223e6f5h\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Projects\\venvi\\android\\app\\.cxx\\RelWithDebInfo\\223e6f5h\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}