import 'package:venvi/constants/permissions.dart';

enum Rules {
  seeAdminScreen(permissions: Permissions.values),
  editOrg(permissions: [Permissions.orgOwner]),
  editCon(permissions: [Permissions.orgOwner, Permissions.orgAdmin]),
  editRoles(permissions: [Permissions.orgOwner, Permissions.orgAdmin]),
  editParticipantsAndRoles(permissions: [Permissions.orgOwner, Permissions.orgAdmin, Permissions.eventEditor]),
  editEventProperties(permissions: [Permissions.orgOwner, Permissions.orgAdmin, Permissions.eventEditor]),
  editEvents(permissions: [Permissions.orgOwner, Permissions.orgAdmin, Permissions.eventEditor]),
  editVenues(permissions: [Permissions.orgOwner, Permissions.orgAdmin]),
  makeAnnouncements(permissions: [Permissions.orgOwner, Permissions.orgAdmin, Permissions.announcementCreator]),
  useReusableEventImages(permissions: [Permissions.orgOwner, Permissions.orgAdmin, Permissions.eventEditor]),
  editHomeScreenGallery(permissions: [Permissions.orgOwner, Permissions.orgAdmin, Permissions.photographer]),
  editMaps(permissions: [Permissions.orgOwner, Permissions.orgAdmin]),
  editGeneralContent(permissions: [Permissions.orgOwner, Permissions.orgAdmin]),
  viewUnpublishedCons(
    permissions: [
      Permissions.orgOwner,
      Permissions.billingManager,
      Permissions.orgAdmin,
      Permissions.eventEditor,
      Permissions.analyticsViewer,
    ],
  ),
  viewAnalytics(
    permissions: [
      Permissions.orgOwner,
      Permissions.billingManager,
      Permissions.orgAdmin,
      Permissions.eventEditor,
      Permissions.analyticsViewer,
    ],
  ),
  importExportSchedule(permissions: [Permissions.orgOwner, Permissions.orgAdmin, Permissions.eventEditor]),
  upgradeToPremium(permissions: [Permissions.orgOwner]);

  final List<Permissions> permissions;

  const Rules({required this.permissions});
}
