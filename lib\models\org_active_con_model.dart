// ignore_for_file: invalid_annotation_target

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/tier.dart';
import 'package:venvi/json_converters/timestamp_converter.dart';
import 'package:venvi/models/base_con.dart';
import 'package:venvi/models/location_model.dart';

part 'org_active_con_model.freezed.dart';
part 'org_active_con_model.g.dart';

@freezed
sealed class OrgActiveConModel with _$OrgActiveConModel implements BaseCon {
  const factory OrgActiveConModel({
    String? id,
    String? orgId,
    @JsonKey(unknownEnumValue: JsonKey.nullForUndefinedEnumValue) Tier? tier,
    String? name,
    String? logoUrl,
    String? color,
    LocationModel? location,
    @TimestampConverter() Timestamp? startDate,
    @TimestampConverter() Timestamp? endDate,
    String? searchName,
  }) = _OrgActiveConModel;

  factory OrgActiveConModel.fromJson(Map<String, dynamic> json) => _$OrgActiveConModelFromJson(json);
}
