import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:venvi/constants/metadata_field.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/metadata_model.dart';
import 'package:venvi/utils/logger.dart';

class MetadataRemoteDataSource {
  final FirebaseFirestore _firestore;

  MetadataRemoteDataSource(this._firestore);

  Stream<MetadataModel?> getMetadataStream(ConData conData) {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('metadata')
        .doc('metadata');
    try {
      return ref.snapshots().map((snapshot) {
        if (snapshot.exists) {
          final data = snapshot.data();
          if (data != null) {
            final MetadataModel metadata = {};
            data.forEach((key, value) {
              if (key == 'id') {
                return;
              }
              final metadataField = MetadataField.fromName(key);
              if (metadataField != null) {
                if (value is Map<String, dynamic>) {
                  final Map<String, Timestamp> convertedMap = {};
                  value.forEach((key, value) {
                    if (value is Timestamp) {
                      convertedMap[key] = value;
                    }
                  });
                  if (convertedMap.isNotEmpty) {
                    metadata[metadataField] = convertedMap;
                  }
                }
              }
            });
            return metadata.isNotEmpty ? metadata : null;
          }
        }
        return null;
      });
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc stream',
        documentReference: ref,
      );
      return Stream.value(null);
    }
  }
}
