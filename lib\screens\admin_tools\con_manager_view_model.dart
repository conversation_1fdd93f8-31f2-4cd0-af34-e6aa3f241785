import 'package:image_picker/image_picker.dart';
import 'package:venvi/constants/image_type.dart';
import 'package:venvi/constants/social_media.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/location_model.dart';
import 'package:venvi/repositories/con_repository.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/screens/admin_tools/con_manager_state.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class ConManagerViewModel extends EditorScaffoldViewModel<ConManagerState> {
  final ConData _conData;
  final ConRepository _conRepository;
  final ImageRepository _imageRepository;

  ConManagerViewModel(super.initialState, this._conData, this._conRepository, this._imageRepository);

  @override
  bool checkChanges(ConManagerState initialState, ConManagerState currentState) {
    return currentState.logoFile != null ||
        currentState.bannerFile != null ||
        initialState.conModel != currentState.conModel;
  }

  @override
  Future<ConManagerState> applyChanges(ConManagerState initialState, ConManagerState state) async {
    var model = state.conModel;
    final logoFile = state.logoFile;
    final bannerFile = state.bannerFile;
    final errors = <String>[];

    if (model.name?.isNotEmpty != true) {
      errors.add('Con name cannot be empty');
    }

    if (errors.isNotEmpty) {
      throw EditorScaffoldException(errors);
    }

    // Removed empty social media links
    final socialMediaMap = Map<SocialMedia, String>.from(model.socialMediaLinks ?? {});
    final keys = socialMediaMap.keys.toList();
    for (final key in keys) {
      if (socialMediaMap[key]?.isNotEmpty != true) {
        socialMediaMap.remove(key);
      }
    }
    model = model.copyWith(socialMediaLinks: socialMediaMap);

    bool success = await _conRepository.updateCon(_conData, model);

    final List<Future<bool>> imageFutures = [];
    if (success) {
      if (logoFile != null && model.logo != null) {
        imageFutures.add(_imageRepository.uploadImage(model.logo!, logoFile));
      }
      if (bannerFile != null && model.banner != null) {
        imageFutures.add(_imageRepository.uploadImage(model.banner!, bannerFile));
      }
      success = (await Future.wait(imageFutures)).every((element) => element);
    }

    if (success) {
      return state.copyWith(conModel: model);
    } else {
      throw const EditorScaffoldException(['Failed to update con']);
    }
  }

  void setName(String? name) {
    name = name?.trim();
    final model = state.conModel.copyWith(name: name);
    emit(state.copyWith(conModel: model));
  }

  void setLogo(XFile file) {
    final logo = _imageRepository.createImageModel(_conData, ImageType.logo);
    final model = state.conModel.copyWith(logo: logo);
    emit(state.copyWith(conModel: model, logoFile: file));
  }

  void setBanner(XFile file) {
    final banner = _imageRepository.createImageModel(_conData, ImageType.banner);
    final model = state.conModel.copyWith(banner: banner);
    emit(state.copyWith(conModel: model, bannerFile: file));
  }

  void changeColor(String? colorHex) {
    final model = state.conModel.copyWith(color: colorHex);
    emit(state.copyWith(conModel: model));
  }

  void setHomepageUrl(String? url) {
    url = url?.trim();
    final model = state.conModel.copyWith(homepageUrl: url);
    emit(state.copyWith(conModel: model));
  }

  void setRegistrationUrl(String? url) {
    url = url?.trim();
    final model = state.conModel.copyWith(registrationUrl: url);
    emit(state.copyWith(conModel: model));
  }

  void setSocialMediaLink(SocialMedia socialMedia, String link) {
    final socialMediaMap = Map<SocialMedia, String>.from(state.conModel.socialMediaLinks ?? {});
    socialMediaMap[socialMedia] = link.trim();
    final model = state.conModel.copyWith(socialMediaLinks: socialMediaMap);
    emit(state.copyWith(conModel: model));
  }

  void removeSocialMediaLink(SocialMedia socialMedia) {
    final socialMediaMap = Map<SocialMedia, String>.from(state.conModel.socialMediaLinks ?? {});
    socialMediaMap.remove(socialMedia);
    final model = state.conModel.copyWith(socialMediaLinks: socialMediaMap);
    emit(state.copyWith(conModel: model));
  }

  void setLocation(LocationModel location) {
    final model = state.conModel.copyWith(location: location);
    emit(state.copyWith(conModel: model));
  }
}
