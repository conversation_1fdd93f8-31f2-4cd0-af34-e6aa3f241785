import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/tier.dart';
import 'package:venvi/custom_classes/date_fields.dart';
import 'package:venvi/models/location_model.dart';
import 'package:venvi/models/profile_model.dart';

part 'create_con_state.freezed.dart';

@freezed
sealed class CreateConState with _$CreateConState {
  const factory CreateConState({
    ProfileModel? orgOwner,
    String? name,
    LocationModel? location,
    DateFields? firstDate,
    DateFields? lastDate,
    String? color,
    Tier? tier,
    bool? showTierSelector,
  }) = _CreateConState;
}
