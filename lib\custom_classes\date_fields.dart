class DateFields {
  final int year;
  final int month;
  final int day;

  DateFields({
    required this.year,
    this.month = 1,
    this.day = 1,
  });

  bool isBefore(DateFields other) {
    if (year < other.year) {
      return true;
    } else if (year == other.year) {
      if (month < other.month) {
        return true;
      } else if (month == other.month) {
        return day < other.day;
      }
    }
    return false;
  }

  bool isAfter(DateFields other) {
    if (year > other.year) {
      return true;
    } else if (year == other.year) {
      if (month > other.month) {
        return true;
      } else if (month == other.month) {
        return day > other.day;
      }
    }
    return false;
  }

  DateTime toDateTime() {
    return DateTime(year, month, day);
  }

  @override
  bool operator ==(Object other) {
    return other is DateFields && other.year == year && other.month == month && other.day == day;
  }

  @override
  int get hashCode => year.hashCode ^ month.hashCode ^ day.hashCode;
}
