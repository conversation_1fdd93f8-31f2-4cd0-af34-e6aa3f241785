import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_network_image_platform_interface/cached_network_image_platform_interface.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/screens/map/map_editor_state.dart';
import 'package:venvi/screens/map/map_editor_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/image_selector.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/shimmer_square.dart';
import 'package:venvi/widgets/surface_input_field.dart';

class MapEditorTile extends HookWidget {
  final int index;
  final MapEditorState mapState;
  final MapEditorViewModel viewModel;

  MapEditorTile({required ConData conData, required this.index, required this.mapState, required this.viewModel})
    : super(key: ValueKey('${conData.conWidgetKey}-${mapState.model.id}'));

  @override
  Widget build(BuildContext context) {
    final model = mapState.model;
    final imageOverride = mapState.imageFile;

    final textController = useTextEditingController(text: model.name);

    return ContentArea(
      key: ValueKey('${context.read<ConData>().conWidgetKey}-${model.id}'),
      padding: const EdgeInsets.fromLTRB(AppTheme.widgetPadding, AppTheme.widgetPadding, AppTheme.widgetPadding, 0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(AppTheme.borderRadius),
            clipBehavior: Clip.hardEdge,
            child:
                imageOverride != null
                    ? kIsWeb
                        ? _buildImageLayout(context, Image.network(imageOverride.path))
                        : _buildImageLayout(context, Image.file(File(imageOverride.path)))
                    : model.image?.downloadUrl != null
                    ? CachedNetworkImage(
                      imageRenderMethodForWeb: ImageRenderMethodForWeb.HttpGet,
                      imageUrl: model.image!.downloadUrl!,
                      imageBuilder:
                          (context, imageProvider) =>
                              _buildImageLayout(context, Image(image: imageProvider, fit: BoxFit.cover)),
                      placeholder: (context, url) => _buildImageLayout(context, const ShimmerSquare()),
                      errorWidget: (context, url, error) => const SizedBox.shrink(),
                    )
                    : const SizedBox.shrink(),
          ),
          OutlinedButton.icon(
            icon: const Icon(Icons.upload),
            label: const Text('Upload Map'),
            onPressed: () async {
              final file = await ImageSelector().selectImage(context, cropAspectRatio: null);
              if (file != null && context.mounted) {
                viewModel.updateMap(index, mapState.copyWith(imageFile: file));
              }
            },
          ),
          const SizedBox(height: AppTheme.widgetPaddingSmall),
          SurfaceInputField(
            child: TextField(
              controller: textController,
              onChanged:
                  (value) =>
                      viewModel.updateMap(index, mapState.copyWith(model: mapState.model.copyWith(name: value.trim()))),
              textInputAction: TextInputAction.done,
              textCapitalization: TextCapitalization.words,
              maxLength: InputConstants.maxMapNameLength,
              decoration: const InputDecoration(hintText: 'Map Name', counterText: ''),
            ),
          ),
          const SizedBox(height: AppTheme.widgetPaddingSmall),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_upward),
                onPressed: index > 0 ? () => viewModel.moveMap(index, index - 1) : null,
              ),
              IconButton(
                icon: const Icon(Icons.arrow_downward),
                onPressed: index < viewModel.state.length - 1 ? () => viewModel.moveMap(index, index + 1) : null,
              ),
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () async {
                  final confirmDelete = await Dialogs.showConfirmationDialog(
                    context,
                    title: 'Delete Map?',
                    message: model.name,
                  );
                  if (confirmDelete == true && context.mounted) {
                    viewModel.removeMap(index);
                  }
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildImageLayout(BuildContext context, Widget child) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.widgetPaddingSmall),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppTheme.borderRadius),
        clipBehavior: Clip.hardEdge,
        child: AspectRatio(aspectRatio: 1, child: child),
      ),
    );
  }
}
