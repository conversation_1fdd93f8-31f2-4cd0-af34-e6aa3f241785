import 'package:flutter/material.dart';
import 'package:venvi/constants/type_icon.dart';

enum EventType {
  type1(
    defaultText: 'Main Event',
    defaultIcon: TypeIcon.ticket,
    color: Color(0xffcc0010),
    foregroundColor: Colors.white,
  ),
  type2(
    defaultText: 'Panels',
    defaultIcon: TypeIcon.lecture,
    color: Color(0xffeadf00),
    foregroundColor: Colors.black,
  ),
  type3(
    defaultText: 'Social',
    defaultIcon: TypeIcon.social,
    color: Color(0xff00d4e4),
    foregroundColor: Colors.black,
  ),
  type4(
    defaultText: 'Vending',
    defaultIcon: TypeIcon.shopping,
    color: Color(0xff00d000),
    foregroundColor: Colors.black,
  ),
  type5(
    defaultText: 'Resources',
    defaultIcon: TypeIcon.info,
    color: Color(0xff7e00ff),
    foregroundColor: Colors.white,
  ),
  type6(
    defaultText: 'Custom',
    defaultIcon: TypeIcon.dashboard,
    color: Color(0xffd600d2),
    foregroundColor: Colors.white,
    optional: true,
  ),
  type7(
    defaultText: 'Custom',
    defaultIcon: TypeIcon.dashboard,
    color: Color(0xff555555),
    foregroundColor: Colors.white,
    optional: true,
  );

  final String defaultText;
  final TypeIcon defaultIcon;
  final Color color;
  final Color foregroundColor;
  final bool optional;

  const EventType({
    required this.defaultText,
    required this.color,
    required this.foregroundColor,
    required this.defaultIcon,
    this.optional = false,
  });
}
