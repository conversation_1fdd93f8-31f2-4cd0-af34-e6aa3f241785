import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/announcement_model.dart';
import 'package:venvi/utils/logger.dart';

class AnnouncementRepository {
  final FirebaseFirestore _firestore;

  AnnouncementRepository(this._firestore);

  Future<AnnouncementModel?> getAnnouncement(ConData conData, String id, {DataLocation? dataLocation}) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('announcements')
        .doc(id);
    try {
      final snapshot = await ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));

      if (snapshot.exists) {
        final data = snapshot.data();
        if (data != null) {
          return AnnouncementModel.fromJson(data);
        }
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        documentReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<String?> createAnnouncement(ConData conData, AnnouncementModel announcement) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('announcements');
    try {
      final data = announcement.toJson();
      data['timestamp'] = FieldValue.serverTimestamp();

      final docRef = await ref.add(data);
      return docRef.id;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to create doc',
        collectionReference: ref,
      );
      return null;
    }
  }

  Future<bool> deleteAnnouncement(ConData conData, String announcementId) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('announcements')
        .doc(announcementId);
    try {
      await ref.delete();
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to delete doc',
        documentReference: ref,
      );
      return false;
    }
  }
}
