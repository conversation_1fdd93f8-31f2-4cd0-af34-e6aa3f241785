import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/constants/tier.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/custom_classes/date_fields.dart';
import 'package:venvi/custom_classes/user_friendly_exception.dart';
import 'package:venvi/json_converters/color_converter.dart';
import 'package:venvi/models/create_con_location_model.dart';
import 'package:venvi/models/create_con_model.dart';
import 'package:venvi/models/location_model.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/repositories/create_con_repository.dart';
import 'package:venvi/repositories/financial_repository.dart';
import 'package:venvi/repositories/profile_repository.dart';
import 'package:venvi/screens/create_con/create_con_state.dart';
import 'package:venvi/utils/time_utils.dart';

class CreateConViewModel extends Cubit<CreateConState> {
  final String? orgId;
  final CreateConRepository _createConRepository;
  final AuthRepository _authRepository;
  final ProfileRepository _profileRepository;
  final FinancialRepository _financialRepository;

  bool get submitEnabled =>
      (orgId != null || state.orgOwner?.id != null) &&
      state.name?.isNotEmpty == true &&
      state.location != null &&
      state.firstDate != null &&
      state.lastDate != null &&
      state.color?.isNotEmpty == true &&
      ColorConverter.tryStringToColor(state.color!) != null &&
      state.tier != null;

  CreateConViewModel(
    this.orgId,
    this._createConRepository,
    this._authRepository,
    this._profileRepository,
    this._financialRepository,
  ) : super(const CreateConState()) {
    init();
  }

  Future<void> init() async {
    if (orgId != null) {
      final financialModel = await _financialRepository.getFinancial(orgId!);
      if (financialModel?.starterTierLimitReached == true) {
        emit(state.copyWith(showTierSelector: false, tier: Tier.premium));
      } else {
        emit(state.copyWith(showTierSelector: true));
      }
    } else {
      final ownerId = _authRepository.currentUser?.uid;
      if (ownerId == null) {
        emit(state.copyWith(showTierSelector: true));
        return;
      }

      final profile = await _profileRepository.getProfile(ownerId);
      emit(state.copyWith(orgOwner: profile, showTierSelector: true));
    }
  }

  void setOrgOwner(String? orgOwnerId) async {
    if (orgId != null) {
      return;
    }

    if (orgOwnerId == null) {
      emit(state.copyWith(orgOwner: null));
      return;
    }

    final profile = await _profileRepository.getProfile(orgOwnerId);
    emit(state.copyWith(orgOwner: profile));
  }

  void setName(String name) {
    emit(state.copyWith(name: name.trim()));
  }

  void setLocation(LocationModel? location) {
    emit(state.copyWith(location: location));
  }

  void setFirstDate(DateFields firstDate) {
    emit(state.copyWith(firstDate: firstDate));
  }

  void setLastDate(DateFields lastDate) {
    emit(state.copyWith(lastDate: lastDate));
  }

  void changeColor(String? colorHex) {
    emit(state.copyWith(color: colorHex));
  }

  void setTier(Tier tier) {
    emit(state.copyWith(tier: tier));
  }

  Future<ConData?> submit() async {
    final createConModel = _buildCreateConModel();
    if (createConModel == null) {
      return null;
    }

    if (createConModel.endDate.millisecondsSinceEpoch - Duration(hours: 12).inMilliseconds <=
        createConModel.startDate.millisecondsSinceEpoch) {
      throw UserFriendlyException('End date cannot be before start date');
    } else if (createConModel.endDate.toDate().isAfter(
      createConModel.startDate.toDate().add(Duration(days: InputConstants.maxConDays, hours: 12)),
    )) {
      throw UserFriendlyException('Max con length is ${InputConstants.maxConDays} days');
    }

    return _createConRepository.createCon(createConModel);
  }

  CreateConModel? _buildCreateConModel() {
    final state = this.state;

    final orgOwnerId = state.orgOwner?.id;
    final name = state.name;
    final locationModel = state.location;
    final locationAddress = locationModel?.address;
    final locationName = locationModel?.name;
    final locationGeoPoint = locationModel?.geoPoint;
    final locationTimeZoneId = locationModel?.timeZoneId;
    final firstDate = state.firstDate;
    final lastDate = state.lastDate;
    final color = state.color;
    final tier = state.tier;

    if (name == null ||
        locationModel == null ||
        locationAddress == null ||
        locationName == null ||
        locationGeoPoint == null ||
        locationTimeZoneId == null ||
        firstDate == null ||
        lastDate == null ||
        color == null ||
        tier == null) {
      return null;
    } else if (orgId != null && orgOwnerId != null) {
      return null;
    } else if (orgId == null && orgOwnerId == null) {
      return null;
    }

    final startDate = TimeUtils.createConDateTime(
      locationModel,
      year: firstDate.year,
      month: firstDate.month,
      day: firstDate.day,
    );
    final endDate = TimeUtils.createConDateTime(
      locationModel,
      year: lastDate.year,
      month: lastDate.month,
      day: lastDate.day + 1,
    );
    if (startDate == null || endDate == null) {
      return null;
    }

    return CreateConModel(
      orgId: orgId,
      orgOwnerId: orgOwnerId,
      tier: tier,
      name: name,
      location: CreateConLocationModel(
        address: locationAddress,
        name: locationName,
        geoPoint: locationGeoPoint,
        timeZoneId: locationTimeZoneId,
      ),
      startDate: Timestamp.fromDate(startDate),
      endDate: Timestamp.fromDate(endDate),
      color: color,
    );
  }
}
