import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/named_doc.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/analytics_model.dart';
import 'package:venvi/models/summary_model.dart';
import 'package:venvi/utils/logger.dart';

class NamedDocRepository {
  final FirebaseFirestore _firestore;

  NamedDocRepository(this._firestore);

  Future<dynamic> getNamedDoc(ConData conData, String id, {DataLocation? dataLocation}) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('namedDocs')
        .doc(id);
    try {
      final snapshot = await ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));

      if (snapshot.exists) {
        return snapshot.data();
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        documentReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<SummaryModel?> getSummary(ConData conData, {DataLocation? dataLocation}) async {
    try {
      final data = await getNamedDoc(conData, NamedDoc.summary.name, dataLocation: dataLocation);
      if (data is Map<String, dynamic>) {
        return SummaryModel.fromJson(data);
      }
      return null;
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace);
      return null;
    }
  }

  Stream<AnalyticsModel?> getAnalyticsStream(ConData conData) {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('namedDocs')
        .doc(NamedDoc.analytics.name);
    try {
      return ref.snapshots().map((snapshot) {
        if (snapshot.exists) {
          final data = snapshot.data();
          if (data != null) {
            try {
              return AnalyticsModel.fromJson(data);
            } catch (e, stackTrace) {
              Logger.error(exception: e, stackTrace: stackTrace);
            }
          }
        }
        return null;
      });
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc stream',
        documentReference: ref,
      );
      return Stream.value(null);
    }
  }

  Future<bool> updateNamedDoc(ConData conData, NamedDoc namedDoc, Map<String, dynamic> json) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('namedDocs')
        .doc(namedDoc.name);
    try {
      await ref.update(json);
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to update doc',
        documentReference: ref,
      );
      return false;
    }
  }
}
