import 'dart:async';

import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/metadata_field.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/metadata_listener_view_model.dart';
import 'package:venvi/global_view_models/metadata_view_model.dart';
import 'package:venvi/models/map_model.dart';
import 'package:venvi/repositories/map_repository.dart';

class MapViewModel extends MetadataListenerViewModel<MapModel, List<MapModel>?> {
  final ConData _conData;
  final MapRepository _repository;

  MapViewModel(this._conData, this._repository, MetadataViewModel metadataViewModel)
      : super(null, _conData, metadataViewModel, MetadataField.maps);

  @override
  Future<MapModel?> getData(String id, DataLocation dataLocation) async {
    return await _repository.getMap(_conData, id, dataLocation: dataLocation);
  }

  @override
  void onData(Map<String, MapModel> data) {
    if (isClosed) {
      return;
    }

    final sortedData = data.values.toList();
    sortedData.sort((a, b) => a.orderIndex != null
        ? b.orderIndex != null
            ? a.orderIndex!.compareTo(b.orderIndex!)
            : 1
        : -1);
    emit(sortedData);
  }
}
