import 'package:flutter/material.dart';
import 'package:venvi/theme.dart';

class ContentArea extends StatelessWidget {
  final EdgeInsets padding;
  final bool fillWidth;
  final void Function()? onTap;
  final Widget? child;

  const ContentArea({
    super.key,
    this.padding = const EdgeInsets.all(AppTheme.widgetPadding),
    this.fillWidth = true,
    this.onTap,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        clipBehavior: Clip.hardEdge,
        width: fillWidth ? AppTheme.maxContentWidth : null,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primaryContainer,
          borderRadius: BorderRadius.circular(AppTheme.borderRadius),
        ),
        padding: onTap != null ? const EdgeInsets.all(0) : padding,
        child: onTap != null ? _createTappableArea(context) : child,
      ),
    );
  }

  Widget _createTappableArea(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: padding,
          child: child,
        ),
      ),
    );
  }
}
