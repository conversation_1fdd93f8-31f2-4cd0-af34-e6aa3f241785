import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/data_sources/device_variables_local_data_source.dart';
import 'package:venvi/global_view_models/analytics_consent_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/url_handler.dart';

class AnalyticsConsentBanner extends StatefulWidget {
  const AnalyticsConsentBanner({super.key});

  @override
  State<AnalyticsConsentBanner> createState() => _AnalyticsConsentBannerState();
}

class _AnalyticsConsentBannerState extends State<AnalyticsConsentBanner> {
  bool _showBanner = false;

  @override
  void initState() {
    if (kIsWeb) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        final analyticsConsentPrompted =
            await context.read<DeviceVariablesLocalDataSource>().analyticsConsentPrompted();
        if (!analyticsConsentPrompted && context.mounted) {
          setState(() {
            _showBanner = true;
          });
        }
      });
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _showBanner
        ? Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            margin: const EdgeInsets.all(AppTheme.screenPadding),
            padding: EdgeInsets.all(AppTheme.widgetPadding),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.tertiary,
              borderRadius: BorderRadius.circular(AppTheme.borderRadius),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Wrap(
                  alignment: WrapAlignment.center,
                  crossAxisAlignment: WrapCrossAlignment.center,
                  spacing: AppTheme.widgetPadding,
                  runSpacing: AppTheme.widgetPaddingSmall,
                  children: [
                    const SizedBox(width: AppTheme.widgetPadding),
                    Text(
                      'We use analytics to improve your experience',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyLarge?.copyWith(color: Theme.of(context).colorScheme.onTertiary),
                    ),
                    OutlinedButton(
                      onPressed: () {
                        context.read<AnalyticsConsentViewModel>().disableAnalytics();
                        setState(() {
                          _showBanner = false;
                        });
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.onTertiary,
                        side: BorderSide(color: Theme.of(context).colorScheme.onTertiary),
                      ),
                      child: Text('Decline'),
                    ),
                    OutlinedButton(
                      onPressed: () {
                        context.read<AnalyticsConsentViewModel>().showAnalyticsConsentDialog(context);
                        setState(() {
                          _showBanner = false;
                        });
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.onTertiary,
                        side: BorderSide(color: Theme.of(context).colorScheme.onTertiary),
                      ),
                      child: Text('Learn more'),
                    ),
                  ],
                ),
                const SizedBox(height: AppTheme.widgetPaddingSmall),
                Divider(color: Theme.of(context).colorScheme.onTertiary, height: 0),
                const SizedBox(height: AppTheme.widgetPadding),
                RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    style: TextStyle(color: Theme.of(context).colorScheme.onTertiary),
                    children: [
                      const TextSpan(text: 'By using this ${kIsWeb ? 'site' : 'app'}, you agree to our '),
                      TextSpan(
                        text: 'Privacy Policy',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onTertiary,
                          decoration: TextDecoration.underline,
                        ),
                        recognizer: TapGestureRecognizer()..onTap = () => UrlHandler.open('https://privacy.venvi.app'),
                      ),
                      const TextSpan(text: ' and '),
                      TextSpan(
                        text: 'Terms of Service',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onTertiary,
                          decoration: TextDecoration.underline,
                        ),
                        recognizer: TapGestureRecognizer()..onTap = () => UrlHandler.open('https://terms.venvi.app'),
                      ),
                      const TextSpan(text: '.'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        )
        : const SizedBox.shrink();
  }
}
