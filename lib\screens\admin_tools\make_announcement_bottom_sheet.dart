import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/repositories/announcement_repository.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/screens/admin_tools/make_announcement_bottom_sheet_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/snack_bars.dart';
import 'package:venvi/widgets/input_field_label.dart';
import 'package:venvi/widgets/surface_input_field.dart';

class MakeAnnouncementBottomSheet extends StatelessWidget {
  const MakeAnnouncementBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MakeAnnouncementBottomSheetViewModel(),
      child: Bloc<PERSON><PERSON>er<MakeAnnouncementBottomSheetViewModel, bool>(
        builder: (context, state) => Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                'Make Announcement',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onTertiaryContainer,
                    ),
              ),
            ),
            const SizedBox(height: AppTheme.widgetPadding),
            const InputFieldLabel(
              label: 'Headline',
              isTertiary: true,
            ),
            SurfaceInputField(
              isTertiary: true,
              child: TextField(
                onChanged: (value) => context.read<MakeAnnouncementBottomSheetViewModel>().setHeadline(value),
                textInputAction: TextInputAction.next,
                textCapitalization: TextCapitalization.sentences,
                maxLength: InputConstants.maxAnnouncementHeadlineLength,
                decoration: const InputDecoration(
                  hintText: 'Headline',
                  counterText: '',
                ),
              ),
            ),
            const SizedBox(height: AppTheme.widgetPadding),
            const InputFieldLabel(
              label: 'Details',
              isTertiary: true,
            ),
            SurfaceInputField(
              isTertiary: true,
              child: TextField(
                onChanged: (value) => context.read<MakeAnnouncementBottomSheetViewModel>().setDetails(value),
                textInputAction: TextInputAction.newline,
                maxLines: 5,
                textCapitalization: TextCapitalization.sentences,
                maxLength: InputConstants.maxAnnouncementDetailsLength,
                decoration: const InputDecoration(
                  contentPadding: EdgeInsets.all(AppTheme.widgetPadding),
                  hintText: 'Details',
                  counterText: '',
                ),
              ),
            ),
            const SizedBox(height: AppTheme.widgetPaddingSmall),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  label: const Text('Cancel'),
                  icon: const Icon(Icons.clear),
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.onTertiary,
                    iconColor: Theme.of(context).colorScheme.onTertiary,
                    backgroundColor: Theme.of(context).colorScheme.tertiary,
                  ),
                  onPressed: () => context.pop(),
                ),
                const SizedBox(width: AppTheme.widgetPadding),
                ElevatedButton.icon(
                  label: const Text('Send'),
                  icon: const Icon(Icons.send),
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.onTertiary,
                    iconColor: Theme.of(context).colorScheme.onTertiary,
                    backgroundColor: Theme.of(context).colorScheme.tertiary,
                  ),
                  onPressed: state
                      ? () async {
                          final confirmation = await Dialogs.showConfirmationDialog(
                            context,
                            title: 'Make Announcement?',
                          );
                          if (confirmation != true || !context.mounted) {
                            return;
                          }
                          Dialogs.showLoadingDialog(context);
                          final success = await context.read<MakeAnnouncementBottomSheetViewModel>().makeAnnouncement(
                                context.read<ConData>(),
                                context.read<AnnouncementRepository>(),
                                context.read<AuthRepository>().currentUserId,
                              );
                          if (!context.mounted) {
                            return;
                          }
                          Navigator.of(context, rootNavigator: true).pop();
                          if (success) {
                            SnackBars.showInfoSnackBar(context, 'Announcement sent');
                            context.pop();
                          } else {
                            SnackBars.showInfoSnackBar(context, 'Failed to make announcement');
                          }
                        }
                      : null,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
