import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:venvi/constants/event_type.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/constants/type_icon.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/named_doc_state.dart';
import 'package:venvi/global_view_models/named_doc_view_model.dart';
import 'package:venvi/models/event_properties_model.dart';
import 'package:venvi/repositories/named_doc_repository.dart';
import 'package:venvi/screens/admin_tools/event_properties_manager_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/bottom_sheets.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/content_group.dart';
import 'package:venvi/widgets/editor_scaffold.dart';
import 'package:venvi/widgets/glass_tile_icon_button.dart';
import 'package:venvi/widgets/surface_input_field.dart';

class EventPropertiesManagerScreen extends HookWidget {
  const EventPropertiesManagerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final Map<EventType, TextEditingController> controllers = {};
    for (final type in EventType.values) {
      controllers[type] = useTextEditingController();
    }

    return EditorScaffold<EventPropertiesManagerViewModel, EventPropertiesModel, NamedDocViewModel, NamedDocState>(
      externalCubit: context.read<NamedDocViewModel>(),
      buildState: (externalCubitState) => externalCubitState.eventProperties ?? const EventPropertiesModel(),
      initViewModel: (state) {
        controllers.forEach((key, value) {
          final name = state.eventTypes?[key]?.name;
          if (name != null) {
            value.text = name;
          }
        });

        return EventPropertiesManagerViewModel(state, context.read<ConData>(), context.read<NamedDocRepository>());
      },
      title: const Text('Event Properties'),
      successMessage: 'Event Properties Updated',
      buildContent:
          (context, viewModel, state) => ListView(
            padding: const EdgeInsets.all(AppTheme.screenPadding),
            children: [
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: EventType.values.length,
                separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                itemBuilder: (context, index) {
                  final type = EventType.values[index];
                  final enabled = !type.optional || (state.eventTypes?[type]?.enabled ?? false);
                  return ContentArea(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (type.optional)
                          SwitchListTile(
                            title: const Text('Use Additional Type'),
                            value: enabled,
                            onChanged: (value) async {
                              if (!value) {
                                viewModel.setEnabledEventType(type, false);
                                return;
                              }

                              final confirm = await Dialogs.showConfirmationDialog(
                                context,
                                title: 'Enable Additional Type?',
                                message:
                                    'Adding more than 5 can make types less meaningful for guests\n\nConsider using tags for additional information',
                              );
                              if (confirm == true) {
                                viewModel.setEnabledEventType(type, true);
                              }
                            },
                            visualDensity: VisualDensity.compact,
                          ),
                        if (enabled)
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(AppTheme.widgetPaddingSmall),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: type.color.withValues(alpha: AppTheme.coloredContainerOpacity),
                                ),
                                child: Icon(
                                  state.eventTypes?[type]?.icon?.icon ?? type.defaultIcon.icon,
                                  color: type.foregroundColor,
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.edit),
                                onPressed:
                                    () async => await _showTypeIconBottomSheet(
                                      context,
                                      eventType: type,
                                      onChanged: (typeIcon) => viewModel.updateEventTypeIcon(type, typeIcon),
                                    ),
                              ),
                              Expanded(
                                child:
                                    type == EventType.type1
                                        ? Padding(
                                          padding: const EdgeInsets.only(left: AppTheme.widgetPaddingSmall),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                EventType.type1.defaultText,
                                                style: Theme.of(context).textTheme.titleMedium,
                                              ),
                                              Text('Cannot be renamed', style: Theme.of(context).textTheme.bodyMedium),
                                            ],
                                          ),
                                        )
                                        : SurfaceInputField(
                                          child: TextField(
                                            controller: controllers[type],
                                            onChanged: (value) => viewModel.updateEventTypeName(type, value),
                                            textInputAction: TextInputAction.next,
                                            textCapitalization: TextCapitalization.words,
                                            maxLength: InputConstants.maxTypeNameLength,
                                            decoration: InputDecoration(hintText: type.defaultText, counterText: ''),
                                          ),
                                        ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  );
                },
              ),
              const SizedBox(height: AppTheme.widgetPadding),
              ContentGroup(
                title: 'Event Tags',
                padHorizontally: false,
                children: [
                  Builder(
                    builder: (context) {
                      final tags = state.eventTags?.toList();
                      if (tags == null || tags.isEmpty) {
                        return const SizedBox.shrink();
                      }
                      tags.sort(
                        (a, b) =>
                            a != null
                                ? b != null
                                    ? a.compareTo(b)
                                    : 1
                                : -1,
                      );
                      return ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: tags.length,
                        itemBuilder: (context, index) {
                          final tag = tags[index];
                          return ListTile(
                            title: Text(tag ?? ''),
                            contentPadding: Theme.of(context).listTileTheme.contentPadding?.subtract(
                              const EdgeInsets.only(right: AppTheme.widgetPadding),
                            ),
                            trailing: IconButton(
                              tooltip: 'Remove Tag',
                              onPressed:
                                  () => Dialogs.showConfirmationDialog(
                                    context,
                                    title: 'Remove ${tag ?? 'unnamed tag'}?',
                                  ).then((value) {
                                    if (value == true && tag != null && context.mounted) {
                                      context.read<EventPropertiesManagerViewModel>().removeTag(tag);
                                    }
                                  }),
                              icon: const Icon(Icons.delete),
                            ),
                          );
                        },
                      );
                    },
                  ),
                  if ((state.eventTags?.length ?? 0) < InputConstants.maxConTags)
                    GlassTileIconButton(
                      icon: Icons.add,
                      tooltip: 'Add Tag',
                      onTap: () async {
                        final newTag = await Dialogs.showTextInputDialog(
                          context,
                          title: 'Add Tag',
                          hintText: 'Tag Name',
                          maxLength: InputConstants.maxTagLength,
                        );
                        if (newTag != null && newTag.isNotEmpty) {
                          viewModel.addTag(newTag);
                        }
                      },
                    ),
                ],
              ),
            ],
          ),
    );
  }

  static Future<void> _showTypeIconBottomSheet(
    BuildContext context, {
    required EventType eventType,
    required void Function(TypeIcon typeIcon) onChanged,
  }) {
    return BottomSheets.showBottomSheet(
      context: context,
      child: BlocBuilder<EventPropertiesManagerViewModel, EventPropertiesModel>(
        bloc: context.read<EventPropertiesManagerViewModel>(),
        builder:
            (context, state) => Wrap(
              alignment: WrapAlignment.center,
              spacing: AppTheme.widgetPaddingSmall,
              children: List.generate(TypeIcon.values.length, (index) {
                final eventIcon = TypeIcon.values[index];
                final backgroundColor = eventType.color.withValues(alpha: AppTheme.coloredContainerOpacity);
                final eventTypeForegroundColor = eventType.foregroundColor;

                return ChoiceChip(
                  label: Icon(eventIcon.icon, color: eventTypeForegroundColor),
                  checkmarkColor: eventTypeForegroundColor,
                  color: WidgetStateProperty.all<Color>(backgroundColor),
                  selected: state.eventTypes?[eventType]?.icon == eventIcon,
                  onSelected: (selected) => onChanged(eventIcon),
                );
              }),
            ),
      ),
    );
  }
}
