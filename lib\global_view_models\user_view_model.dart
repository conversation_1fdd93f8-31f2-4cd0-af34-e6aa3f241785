import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/notification_type.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/auth_view_model.dart';
import 'package:venvi/models/user_model.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/repositories/cloud_messaging_repository.dart';
import 'package:venvi/repositories/device_repository.dart';
import 'package:venvi/repositories/user_repository.dart';
import 'package:venvi/utils/logger.dart';

class UserViewModel extends Cubit<UserModel> {
  final UserRepository _userRepository;
  final AuthRepository _authRepository;
  final AuthViewModel _authViewModel;
  final CloudMessagingRepository _cloudMessagingRepository;
  final DeviceRepository _deviceRepository;

  late final StreamSubscription _authSubscription;
  late final StreamSubscription _deviceSubscription;

  StreamSubscription<UserModel?>? _userSubscription;

  UserViewModel(
    this._userRepository,
    this._authRepository,
    this._authViewModel,
    this._cloudMessagingRepository,
    this._deviceRepository,
  ) : super(const UserModel()) {
    _authSubscription = _authViewModel.stream.listen((data) => _onNewAuthState(data));
    _onNewAuthState(_authViewModel.state);

    _deviceSubscription = _deviceRepository
        .onTokenRefresh()
        .listen((event) => _deviceRepository.updateDeviceToken(_authRepository.currentUserId, state, event));
  }

  @override
  Future<void> close() async {
    await _authSubscription.cancel();
    await _deviceSubscription.cancel();
    await _userSubscription?.cancel();
    return super.close();
  }

  Future<void> signOut() async {
    try {
      await _userSubscription?.cancel();
      await _removeDevice();
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace, message: 'Error signing out');
    }
  }

  void _onNewAuthState(User? user) {
    _userSubscription?.cancel();

    final userId = user?.uid;
    if (userId != null) {
      _userSubscription = _userRepository.getUserStream(userId).listen((event) {
        _cloudMessagingRepository.scheduleNotifications();
        emit(event ?? const UserModel());
      });
      _addDevice();
    } else {
      _cloudMessagingRepository.clearAllNotifications();
    }
  }

  Future<void> addFavoriteCon(ConData conData) async {
    if (state.favoriteOrgs?.contains(conData.orgId) == true &&
        state.favoriteCons?.contains('${conData.orgId}_${conData.conId}') == true) {
      return;
    }

    final favoriteOrgs = state.favoriteOrgs?.toList() ?? [];
    if (!favoriteOrgs.contains(conData.orgId)) {
      favoriteOrgs.add(conData.orgId);
    }

    final favoriteCons = state.favoriteCons?.toList() ?? [];
    if (!favoriteCons.contains('${conData.orgId}_${conData.conId}')) {
      favoriteCons.add('${conData.orgId}_${conData.conId}');
    }

    await _updateUser(state.copyWith(
      favoriteOrgs: favoriteOrgs,
      favoriteCons: favoriteCons,
    ));
  }

  Future<void> removeFavoriteCon(String orgId) async {
    final favoriteOrgs = state.favoriteOrgs?.toList() ?? [];
    favoriteOrgs.remove(orgId);

    // Removes all favorite cons
    final favoriteCons = state.favoriteCons?.toList() ?? [];
    favoriteCons.removeWhere((element) => element.startsWith('${orgId}_'));

    await _updateUser(state.copyWith(favoriteOrgs: favoriteOrgs, favoriteCons: favoriteCons));
  }

  Future<void> addFavoriteEvent(ConData conData, String eventId) async {
    final favoriteEvents = state.favoriteEvents?.toList() ?? [];
    favoriteEvents.add('${conData.orgId}_${conData.conId}_$eventId');
    await _updateUser(state.copyWith(favoriteEvents: favoriteEvents));
  }

  Future<void> removeFavoriteEvent(ConData conData, String eventId) async {
    final favoriteEvents = state.favoriteEvents?.toList() ?? [];
    favoriteEvents.remove('${conData.orgId}_${conData.conId}_$eventId');
    await _updateUser(state.copyWith(favoriteEvents: favoriteEvents));
  }

  Future<void> markAnnouncementRead(ConData conData, String announcementId) async {
    final readAnnouncements = state.readAnnouncements?.toList() ?? [];
    readAnnouncements.add('${conData.orgId}_${conData.conId}_$announcementId');
    await _updateUser(state.copyWith(readAnnouncements: readAnnouncements));
  }

  Future<void> enableNotificationType(NotificationType notificationType) async {
    switch (notificationType) {
      case NotificationType.announcements:
        await _updateUser(state.copyWith(announcementNotifications: true));
        break;
      case NotificationType.spotlight:
        await _updateUser(state.copyWith(spotlightEventNotifications: true));
        break;
      case NotificationType.favorites:
        await _updateUser(state.copyWith(favoriteEventNotifications: true));
        break;
    }
  }

  Future<void> disableNotificationType(NotificationType notificationType) async {
    switch (notificationType) {
      case NotificationType.announcements:
        await _updateUser(state.copyWith(announcementNotifications: false));
        break;
      case NotificationType.spotlight:
        await _updateUser(state.copyWith(spotlightEventNotifications: false));
        break;
      case NotificationType.favorites:
        await _updateUser(state.copyWith(favoriteEventNotifications: false));
        break;
    }
  }

  Future<void> _addDevice() async {
    await _deviceRepository.addDevice(_authRepository.currentUserId, state);
  }

  Future<void> _removeDevice() async {
    await _deviceRepository.removeDevice(_authRepository.currentUserId, state);
    await _cloudMessagingRepository.clearAllNotifications();
  }

  Future<void> enableUserInteractiveDeviceNotifications(
    AuthRepository authRepository, {
    BuildContext? errorMessageContext,
  }) async {
    await _deviceRepository.enableUserInteractiveDeviceNotifications(
      authRepository,
      state,
      errorMessageContext: errorMessageContext,
    );
  }

  Future<void> disableDeviceNotifications() async {
    await _deviceRepository.disableDeviceNotifications(_authRepository.currentUserId, state);
  }

  Future<void> setShowAdultContent(bool showAdultContent) async {
    await _updateUser(state.copyWith(showAdultContent: showAdultContent));
  }

  Future<void> _updateUser(UserModel newUser) async {
    final userId = _authRepository.currentUserId;
    if (userId == null) {
      return;
    }
    await _userRepository.updateUser(userId, newUser);

    if (!isClosed) {
      emit(newUser);
    }
  }
}
