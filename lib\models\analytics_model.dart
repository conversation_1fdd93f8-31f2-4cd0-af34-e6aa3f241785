import 'package:freezed_annotation/freezed_annotation.dart';

part 'analytics_model.freezed.dart';
part 'analytics_model.g.dart';

@freezed
sealed class AnalyticsModel with _$AnalyticsModel {
  const factory AnalyticsModel({
    String? id,
    int? scheduleDownloads,
    int? androidScheduleDownloads,
    int? iosScheduleDownloads,
    int? webScheduleDownloads,
    int? finalScheduleDownloads,
    int? finalAndroidScheduleDownloads,
    int? finalIosScheduleDownloads,
    int? finalWebScheduleDownloads,
    int? totalConFavorites,
    int? currentConFavorites,
    int? totalConFavoritesNewUsers,
    int? totalConFavoritesReturningUsers,
    Map<String, int>? totalEventFavorites,
    Map<String, int>? currentEventFavorites,
  }) = _AnalyticsModel;

  factory AnalyticsModel.fromJson(Map<String, dynamic> json) => _$AnalyticsModelFrom<PERSON><PERSON>(json);
}
