import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/profile_model.dart';

part 'profile_search_result.freezed.dart';

@freezed
sealed class ProfileSearchResult with _$ProfileSearchResult {
  @Assert(
    '(profileModel != null && participantModel == null) || '
    '(profileModel == null && participantModel != null)',
  )
  const factory ProfileSearchResult({ProfileModel? profileModel, ParticipantModel? participantModel}) =
      _ProfileSearchResult;
}
