import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_network_image_platform_interface/cached_network_image_platform_interface.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/models/base_con.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/time_utils.dart';

class ConListItem extends StatelessWidget {
  final BaseCon conModel;
  final bool? isFavorite;

  const ConListItem({super.key, required this.conModel, this.isFavorite});

  @override
  Widget build(BuildContext context) {
    final location = conModel.location;
    final locationName = location?.name;
    final address = location?.address;
    final startDateText = TimeUtils.printDateWithYear(location, conModel.startDate);
    final endDate = conModel.endDate;
    final endDateText =
        endDate != null
            ? TimeUtils.printDateWithYear(
              location,
              Timestamp.fromMillisecondsSinceEpoch(endDate.millisecondsSinceEpoch - Duration(hours: 12).inMilliseconds),
            )
            : null;

    final conData = ConData.tryCreate(orgId: conModel.orgId, conId: conModel.id);

    return ListTile(
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppTheme.widgetPadding,
        vertical: AppTheme.widgetPaddingSmall,
      ),
      title: Center(
        child: Text(
          conModel.name ?? '',
          textAlign: TextAlign.center,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(color: Theme.of(context).colorScheme.onPrimaryContainer),
        ),
      ),
      subtitle: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: AppTheme.widgetPaddingSmall),
          if (startDateText != null || endDateText != null)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (startDateText != null)
                  Text(
                    startDateText,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Theme.of(context).colorScheme.onPrimaryContainer),
                  ),
                if (startDateText != null && endDateText != null) const SizedBox(width: AppTheme.widgetPaddingSmall),
                if (endDateText != null)
                  Text(
                    endDateText,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onPrimaryContainer.withValues(alpha: AppTheme.subtleWidgetOpacity),
                    ),
                  ),
              ],
            ),
          if (locationName != null || address != null)
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  WidgetSpan(
                    alignment: PlaceholderAlignment.middle,
                    child: Padding(
                      padding: const EdgeInsets.only(right: AppTheme.widgetPaddingVerySmall),
                      child: Icon(Icons.place, size: 12),
                    ),
                  ),
                  if (locationName != null) TextSpan(text: locationName, style: Theme.of(context).textTheme.bodySmall),
                  if (address != null)
                    TextSpan(
                      text: '\n$address',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onPrimaryContainer.withValues(alpha: AppTheme.subtleWidgetOpacity),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
      leading: SizedBox(
        width: 50,
        height: 50,
        child:
            conModel.logoUrl != null
                ? ClipRRect(
                  borderRadius: BorderRadius.circular(AppTheme.borderRadius),
                  clipBehavior: Clip.hardEdge,
                  child: CachedNetworkImage(
                    imageRenderMethodForWeb: ImageRenderMethodForWeb.HttpGet,
                    fit: BoxFit.contain,
                    imageUrl: conModel.logoUrl!,
                    errorWidget: (context, url, error) => const SizedBox.shrink(),
                  ),
                )
                : const SizedBox.shrink(),
      ),
      trailing:
          isFavorite != null
              ? IconButton(
                tooltip: isFavorite == true ? 'Remove favorite' : 'Add favorite',
                icon: isFavorite == true ? const Icon(Icons.favorite) : const Icon(Icons.favorite_border),
                onPressed: conData != null ? () => _toggleFavoriteOrg(context, conData, isFavorite == true) : null,
              )
              : null,
      onTap: conData != null ? () => context.go(conData.conPath) : null,
    );
  }

  Future<void> _toggleFavoriteOrg(BuildContext context, ConData conData, bool isCurrentlyFavorite) async {
    final userViewModel = context.read<UserViewModel>();
    if (isCurrentlyFavorite) {
      final confirmed = await Dialogs.showConfirmationDialog(
        context,
        title: 'Remove Favorite?',
        message: 'You will no longer receive notifications from here',
      );
      if (confirmed == true) {
        await userViewModel.removeFavoriteCon(conData.orgId);
      }
    } else {
      userViewModel.addFavoriteCon(conData);
    }
  }
}
