import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/profile_model.dart';
import 'package:venvi/repositories/profile_repository.dart';
import 'package:venvi/screen_dialogs/profile_search/profile_search_state.dart';

class ProfileSearchViewModel<ProfileSearchResult> extends Cubit<ProfileSearchState> {
  static const int _maxCloudSearchResults = 10;

  final ParticipantViewModel? _participantViewModel;
  final ProfileRepository _profileRepository;
  final List<String>? _excludedIds;
  final bool showManualEntries;

  String _latestSearchText = '';

  ProfileSearchViewModel(this._participantViewModel, this._profileRepository, this._excludedIds, this.showManualEntries)
    : super(const ProfileSearchState(participants: [], profiles: []));

  Future<void> searchTextUpdated(String searchText) async {
    searchText = searchText.trim();
    if (_latestSearchText == searchText) {
      return;
    }
    _latestSearchText = searchText;
    if (searchText.isEmpty) {
      emit(const ProfileSearchState(participants: [], profiles: []));
      return;
    }

    final List<ParticipantModel> searchParticipantModels = [];
    final List<String> participantIds = [];
    final participantModels = _participantViewModel?.searchParticipants(searchText);
    if (participantModels != null && participantModels.isNotEmpty) {
      for (final participant in participantModels) {
        final id = participant.id;
        if (id != null &&
            _excludedIds?.contains(id) != true &&
            (showManualEntries || participant.manualEntry != true)) {
          searchParticipantModels.add(participant);
          participantIds.add(id);
        }
      }
      // Emits while still searching profiles
      emit(ProfileSearchState(participants: searchParticipantModels, profiles: []));
    }

    final profileModels = await _profileRepository.searchForProfile(searchText, _maxCloudSearchResults);

    // Makes sure there wasn't a newer search since this one started
    if (_latestSearchText != searchText || isClosed) {
      return;
    }

    final List<ProfileModel> searchProfileModels = [];
    if (profileModels != null && profileModels.isNotEmpty) {
      for (final profile in profileModels) {
        final id = profile.id;
        if (id != null && !participantIds.contains(id) && _excludedIds?.contains(id) != true) {
          searchProfileModels.add(profile);
        }
      }
      emit(ProfileSearchState(participants: searchParticipantModels, profiles: searchProfileModels));
    }
  }
}
