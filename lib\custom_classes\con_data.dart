import 'package:freezed_annotation/freezed_annotation.dart';

part 'con_data.freezed.dart';

@freezed
sealed class ConData with _$ConData {
  static ConData? tryCreate({required String? orgId, required String? conId}) {
    if (orgId == null || orgId.isEmpty || conId == null || conId.isEmpty) {
      return null;
    }
    return ConData(orgId: orgId, conId: conId);
  }

  const factory ConData({required String orgId, required String conId}) = _ConData;
}

extension ConDataExtension on ConData {
  String get conPath => '/$orgId/$conId';

  String get conWidgetKey => '${orgId}_$conId';
}
