import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sign_in_button/sign_in_button.dart';
import 'package:venvi/constants/notification_type.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/custom_classes/user_friendly_exception.dart';
import 'package:venvi/global_view_models/analytics_consent_view_model.dart';
import 'package:venvi/global_view_models/auth_view_model.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/models/user_model.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/repositories/device_repository.dart';
import 'package:venvi/router.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_profile_screen.dart';
import 'package:venvi/screens/settings/settings_state.dart';
import 'package:venvi/screens/settings/settings_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/content_group.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/glass_tile_button.dart';
import 'package:venvi/widgets/glass_tile_switch.dart';
import 'package:venvi/widgets/legal_banner.dart';

class SettingsScreen extends StatelessWidget {
  final ConData? conData;

  const SettingsScreen({super.key, required this.conData});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SettingsViewModel(
        context.read<AuthViewModel>(),
        context.read<UserViewModel>(),
        context.read<DeviceRepository>(),
      ),
      child: Scaffold(
        appBar: const GlassAppBar(title: Text('Settings')),
        body: Column(
          children: [
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(AppTheme.screenPadding),
                children: [
                  BlocBuilder<AuthViewModel, User?>(
                    builder: (context, state) => state == null || state.isAnonymous
                        ? Padding(
                            padding: const EdgeInsets.only(bottom: AppTheme.widgetPadding),
                            child: ContentGroup(
                              title: 'Sign In or Register',
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SignInButton(
                                    Buttons.google,
                                    elevation: 0,
                                    onPressed: () async {
                                      try {
                                        await context.read<AuthRepository>().signInWithGoogle();
                                      } catch (e) {
                                        if (!context.mounted) {
                                          return;
                                        }
                                        if (e is UserFriendlyException) {
                                          if (e.message != null) {
                                            Dialogs.showErrorDialog(context, message: e.message);
                                          }
                                        } else {
                                          Dialogs.showErrorDialog(context, message: 'Failed to sign in with Google');
                                        }
                                      }
                                    },
                                  ),
                                  if (!kIsWeb && Platform.isIOS)
                                    Padding(
                                      padding: const EdgeInsets.only(top: AppTheme.widgetPaddingSmall),
                                      child: SignInButton(
                                        Buttons.apple,
                                        elevation: 0,
                                        onPressed: () async {
                                          try {
                                            await context.read<AuthRepository>().signInWithApple();
                                          } catch (e) {
                                            if (!context.mounted) {
                                              return;
                                            }
                                            if (e is UserFriendlyException) {
                                              if (e.message != null) {
                                                Dialogs.showErrorDialog(context, message: e.message);
                                              }
                                            } else {
                                              Dialogs.showErrorDialog(context, message: 'Failed to sign in with Apple');
                                            }
                                          }
                                        },
                                      ),
                                    ),
                                  if (kDebugMode)
                                    Padding(
                                      padding: const EdgeInsets.only(top: AppTheme.widgetPaddingSmall),
                                      child: SignInButton(
                                        Buttons.email,
                                        elevation: 0,
                                        onPressed: () async => await Dialogs.showEmailSignInDialog(context),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          )
                        : const SizedBox(),
                  ),
                  BlocBuilder<AuthViewModel, User?>(
                    builder: (context, state) => state != null && !state.isAnonymous
                        ? Padding(
                            padding: const EdgeInsets.only(bottom: AppTheme.widgetPadding),
                            child: ContentArea(
                              padding: EdgeInsets.zero,
                              child: GlassTileButton(
                                icon: Icons.edit,
                                text: 'Edit Profile',
                                subtitle: state.email,
                                onTap: () => AppRouter.pushFullScreenDialog(context, const EditProfileScreen()),
                              ),
                            ),
                          )
                        : const SizedBox(),
                  ),
                  if (!kIsWeb)
                    Padding(
                      padding: const EdgeInsets.only(bottom: AppTheme.widgetPadding),
                      child: ContentGroup(
                        title: 'Notifications',
                        padHorizontally: false,
                        child: BlocBuilder<SettingsViewModel, SettingsState>(
                          builder: (context, settingState) => BlocBuilder<UserViewModel, UserModel?>(
                            builder: (context, userState) => Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                GlassTileSwitch(
                                  title: 'Enabled',
                                  desc: 'Enable notifications for this device',
                                  value: settingState.deviceNotificationsEnabled,
                                  onChanged: (value) => value
                                      ? context.read<UserViewModel>().enableUserInteractiveDeviceNotifications(
                                          context.read<AuthRepository>(),
                                          errorMessageContext: context,
                                        )
                                      : context.read<UserViewModel>().disableDeviceNotifications(),
                                ),
                                if (settingState.deviceNotificationsEnabled)
                                  GlassTileSwitch(
                                    title: 'Announcements',
                                    desc: 'Messages from con admins',
                                    value: userState?.announcementNotifications ?? true,
                                    onChanged: (value) => value
                                        ? context.read<UserViewModel>().enableNotificationType(
                                            NotificationType.announcements,
                                          )
                                        : context.read<UserViewModel>().disableNotificationType(
                                            NotificationType.announcements,
                                          ),
                                  ),
                                if (settingState.deviceNotificationsEnabled)
                                  GlassTileSwitch(
                                    title: 'Spotlight Events',
                                    desc: '20 minutes before start and if time or location change',
                                    value: userState?.spotlightEventNotifications ?? true,
                                    onChanged: (value) => value
                                        ? context.read<UserViewModel>().enableNotificationType(
                                            NotificationType.spotlight,
                                          )
                                        : context.read<UserViewModel>().disableNotificationType(
                                            NotificationType.spotlight,
                                          ),
                                  ),
                                if (settingState.deviceNotificationsEnabled)
                                  GlassTileSwitch(
                                    title: 'My Favorite Events',
                                    desc: '15 minutes before start and if time or location change',
                                    value: userState?.favoriteEventNotifications ?? true,
                                    onChanged: (value) => value
                                        ? context.read<UserViewModel>().enableNotificationType(
                                            NotificationType.favorites,
                                          )
                                        : context.read<UserViewModel>().disableNotificationType(
                                            NotificationType.favorites,
                                          ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ContentArea(
                    padding: EdgeInsets.zero,
                    child: GlassTileSwitch(
                      title: 'Show Adult-Only Events',
                      value: context.watch<UserViewModel>().state.showAdultContent ?? true,
                      onChanged: (value) => context.read<UserViewModel>().setShowAdultContent(value),
                    ),
                  ),
                  const SizedBox(height: AppTheme.widgetPadding),
                  ContentArea(
                    padding: EdgeInsets.zero,
                    child: BlocBuilder<AnalyticsConsentViewModel, bool>(
                      builder: (context, state) => GlassTileSwitch(
                        title: 'Share analytics and crash reports',
                        desc: 'Help us improve the experience',
                        value: state,
                        onChanged: (value) => value
                            ? context.read<AnalyticsConsentViewModel>().enableAnalytics()
                            : context.read<AnalyticsConsentViewModel>().disableAnalytics(),
                      ),
                    ),
                  ),
                  BlocBuilder<AuthViewModel, User?>(
                    builder: (context, state) => state != null && !state.isAnonymous
                        ? Padding(
                            padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                            child: ContentGroup(
                              children: [
                                GlassTileButton(
                                  icon: Icons.logout,
                                  text: 'Sign Out',
                                  onTap: () => Dialogs.showSignOutDialog(context),
                                ),
                                GlassTileButton(
                                  icon: Icons.delete,
                                  text: 'Delete Account',
                                  onTap: () => AppRouter.pushNamedRoute(context, 'delete-account'),
                                ),
                              ],
                            ),
                          )
                        : const SizedBox(),
                  ),
                ],
              ),
            ),
            FutureBuilder(
              future: PackageInfo.fromPlatform(),
              builder: (context, snapshot) => snapshot.hasData
                  ? Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.screenPadding,
                        vertical: AppTheme.widgetPaddingVerySmall,
                      ),
                      child: Text(
                        'Version: ${snapshot.data!.version}',
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
            const Padding(
              padding: EdgeInsets.fromLTRB(AppTheme.screenPadding, 0, AppTheme.screenPadding, AppTheme.screenPadding),
              child: LegalBanner(),
            ),
          ],
        ),
      ),
    );
  }
}
