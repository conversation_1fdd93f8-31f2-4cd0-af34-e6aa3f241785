import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/venue_model.dart';

part 'venue_snippet_model.freezed.dart';
part 'venue_snippet_model.g.dart';

@freezed
sealed class VenueSnippetModel with _$VenueSnippetModel {
  const factory VenueSnippetModel({String? id, String? name}) = _VenueSnippetModel;

  factory VenueSnippetModel.fromVenueModel(VenueModel venueModel) =>
      VenueSnippetModel(id: venueModel.id, name: venueModel.name);

  factory VenueSnippetModel.fromJson(Map<String, dynamic> json) => _$VenueSnippetModelFromJson(json);
}
