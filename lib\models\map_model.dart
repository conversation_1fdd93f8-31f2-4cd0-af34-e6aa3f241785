import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/image_model.dart';

part 'map_model.freezed.dart';
part 'map_model.g.dart';

@freezed
sealed class MapModel with _$MapModel {
  const factory MapModel({String? id, int? orderIndex, ImageModel? image, String? name}) = _MapModel;

  factory MapModel.fromJson(Map<String, dynamic> json) => _$MapModelFromJson(json);
}
