import 'package:venvi/constants/event_type.dart';
import 'package:venvi/constants/named_doc.dart';
import 'package:venvi/constants/type_icon.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/con_event_type_model.dart';
import 'package:venvi/models/event_properties_model.dart';
import 'package:venvi/repositories/named_doc_repository.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class EventPropertiesManagerViewModel extends EditorScaffoldViewModel<EventPropertiesModel> {
  final ConData _conData;
  final NamedDocRepository _namedDocRepository;

  EventPropertiesManagerViewModel(super.initialState, this._conData, this._namedDocRepository);

  @override
  bool checkChanges(EventPropertiesModel initialState, EventPropertiesModel currentState) {
    return initialState != currentState;
  }

  @override
  Future<EventPropertiesModel> applyChanges(EventPropertiesModel initialState, EventPropertiesModel state) async {
    final success = await _namedDocRepository.updateNamedDoc(_conData, NamedDoc.eventProperties, state.toJson());
    if (success) {
      return state;
    } else {
      throw const EditorScaffoldException(['Failed to update properties']);
    }
  }

  void addTag(String tag) {
    final model = state.copyWith(
      eventTags: [...state.eventTags ?? [], tag],
    );
    emit(model);
  }

  void removeTag(String tag) {
    final model = state.copyWith(
      eventTags: state.eventTags?.where((element) => element != tag).toList(),
    );
    emit(model);
  }

  void updateEventTypeName(EventType type, String name) {
    final eventTypes = _createEventTypeMap(type);
    eventTypes[type] = eventTypes[type]!.copyWith(name: name.trim());
    emit(state.copyWith(eventTypes: eventTypes));
  }

  void updateEventTypeIcon(EventType type, TypeIcon icon) {
    final eventTypes = _createEventTypeMap(type);
    eventTypes[type] = eventTypes[type]!.copyWith(icon: icon);
    emit(state.copyWith(eventTypes: eventTypes));
  }

  void setEnabledEventType(EventType type, bool enabled) {
    final eventTypes = _createEventTypeMap(type);
    eventTypes[type] = eventTypes[type]!.copyWith(enabled: enabled);
    emit(state.copyWith(eventTypes: eventTypes));
  }

  Map<EventType, ConEventTypeModel> _createEventTypeMap(EventType type) {
    final conEventTypes = Map<EventType, ConEventTypeModel>.from(state.eventTypes ?? {});
    conEventTypes[type] = conEventTypes[type] ?? const ConEventTypeModel();
    return conEventTypes;
  }
}
