import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/models/location_model.dart';
import 'package:venvi/repositories/location_repository.dart';
import 'package:venvi/screen_dialogs/location_search/location_search_state.dart';

class LocationSearchViewModel extends Cubit<LocationSearchState> {
  static const _queryDebounceDuration = Duration(milliseconds: 800);

  final LocationRepository _locationRepository;

  String _query = '';
  DateTime _lastQueryTime = DateTime.now();

  LocationSearchViewModel(this._locationRepository) : super(const LocationSearchState(isLoading: false, locations: []));

  Future<void> updateQuery(String query) async {
    query = query.trim();
    if (query == _query) {
      return;
    }
    _query = query;

    if (query.length < 3) {
      emit(const LocationSearchState(isLoading: false, locations: []));
      return;
    }

    if (DateTime.now().difference(_lastQueryTime) >= _queryDebounceDuration) {
      _startSearch(query);
    } else {
      await Future.delayed(_queryDebounceDuration);
      if (query == _query && !isClosed) {
        _startSearch(query);
      }
    }
  }

  Future<void> _startSearch(String query) async {
    emit(state.copyWith(isLoading: true));
    _lastQueryTime = DateTime.now();

    final result = await _locationRepository.getLocations(query);

    // If query was most recent
    if (query == _query && !isClosed) {
      emit(LocationSearchState(isLoading: false, locations: result));
    }
  }

  Future<LocationModel?> completeLocationModel(LocationModel locationModel) async {
    final geoPoint = locationModel.geoPoint;
    if (geoPoint == null) {
      return null;
    }

    final timeZoneId = await _locationRepository.getTimeZoneId(geoPoint.latitude, geoPoint.longitude);
    if (timeZoneId == null) {
      return null;
    }

    return locationModel.copyWith(timeZoneId: timeZoneId);
  }
}
