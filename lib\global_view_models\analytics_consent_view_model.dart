import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/analytics_consent.dart';
import 'package:venvi/data_sources/device_variables_local_data_source.dart';
import 'package:venvi/utils/dialogs.dart';

class AnalyticsConsentViewModel extends Cubit<bool> {
  final AnalyticsConsent _analyticsConsent;
  final DeviceVariablesLocalDataSource _deviceVariablesLocalDataSource;

  AnalyticsConsentViewModel(this._analyticsConsent, this._deviceVariablesLocalDataSource)
      : super(_analyticsConsent.analyticsEnabled);

  Future<void> tryShowInitialConsentDialog(BuildContext context) async {
    if (kIsWeb) {
      return;
    }
    final analyticsConsentPrompted = await _deviceVariablesLocalDataSource.analyticsConsentPrompted();
    if (!analyticsConsentPrompted && context.mounted) {
      showAnalyticsConsentDialog(context);
    }
  }

  Future<void> showAnalyticsConsentDialog(BuildContext context) async {
    await Dialogs.showAnalyticsConsentDialog(context);
    emit(_analyticsConsent.analyticsEnabled);
  }

  Future<void> enableAnalytics() async {
    final success = await _analyticsConsent.enableAnalytics();
    if (success) {
      emit(_analyticsConsent.analyticsEnabled);
    }
  }

  Future<void> disableAnalytics() async {
    final success = await _analyticsConsent.disableAnalytics();
    if (success) {
      emit(_analyticsConsent.analyticsEnabled);
    }
  }
}
