import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:venvi/models/app_status_model.dart';
import 'package:venvi/utils/logger.dart';

class AppStatusRepository {
  final FirebaseFirestore _firestore;

  AppStatusRepository(this._firestore);

  Stream<AppStatusModel?> getAppStatusStream() {
    final ref = _firestore.collection('appStatus').doc('appStatus');
    try {
      return ref.snapshots().map((snapshot) {
        if (snapshot.exists) {
          final data = snapshot.data();
          if (data != null) {
            try {
              return AppStatusModel.fromJson(data);
            } catch (e, stackTrace) {
              Logger.error(exception: e, stackTrace: stackTrace);
            }
          }
        }
        return null;
      });
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc stream',
        documentReference: ref,
      );
      return Stream.value(null);
    }
  }
}
