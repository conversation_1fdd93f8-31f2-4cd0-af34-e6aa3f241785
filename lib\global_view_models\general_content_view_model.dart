import 'dart:async';

import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/metadata_field.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/metadata_listener_view_model.dart';
import 'package:venvi/global_view_models/metadata_view_model.dart';
import 'package:venvi/models/general_content_model.dart';
import 'package:venvi/repositories/general_content_repository.dart';

class GeneralContentViewModel extends MetadataListenerViewModel<GeneralContentModel, List<GeneralContentModel>?> {
  final ConData _conData;
  final GeneralContentRepository _repository;

  GeneralContentViewModel(this._conData, this._repository, MetadataViewModel metadataViewModel)
      : super(null, _conData, metadataViewModel, MetadataField.generalContent);

  @override
  Future<GeneralContentModel?> getData(String id, DataLocation dataLocation) async {
    return await _repository.getGeneralContent(_conData, id, dataLocation: dataLocation);
  }

  @override
  void onData(Map<String, GeneralContentModel> data) {
    if (isClosed) {
      return;
    }

    final sortedData = data.values.toList();
    sortedData.sort((a, b) => a.orderIndex != null
        ? b.orderIndex != null
            ? a.orderIndex!.compareTo(b.orderIndex!)
            : 1
        : -1);
    emit(sortedData);
  }

  GeneralContentModel? getById(String? id) {
    if (id == null) {
      return null;
    }
    try {
      return state?.firstWhere((element) => element.id == id);
    } catch (e) {
      return null;
    }
  }
}
