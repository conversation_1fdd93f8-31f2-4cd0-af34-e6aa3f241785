import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class EditManualParticipantViewModel extends EditorScaffoldViewModel<ParticipantModel> {
  final ConData conData;
  final ParticipantRepository _participantRepository;
  final String? participantId;
  final bool isNewParticipant;

  EditManualParticipantViewModel(
      super.initialState, this.conData, this._participantRepository, this.participantId, this.isNewParticipant);

  @override
  bool checkChanges(ParticipantModel initialState, ParticipantModel currentState) {
    return currentState != initialState;
  }

  @override
  Future<ParticipantModel> applyChanges(ParticipantModel initialState, ParticipantModel state) async {
    final errors = <String>[];

    if (state.displayName?.isNotEmpty != true) {
      errors.add('Display name is required');
    }

    if (errors.isNotEmpty) {
      throw EditorScaffoldException(errors);
    }

    if (isNewParticipant) {
      final docId = await _participantRepository.createManualParticipant(conData, state);
      if (docId != null) {
        return state.copyWith(id: docId);
      } else {
        throw const EditorScaffoldException(['Failed to create profile']);
      }
    } else {
      final success = await _participantRepository.updateManualParticipant(conData, participantId!, state);
      if (success) {
        return state;
      } else {
        throw const EditorScaffoldException(['Failed to update profile']);
      }
    }
  }

  void setDisplayName(String displayName) {
    emit(state.copyWith(
      displayName: displayName.trim(),
    ));
  }

  void setConHeadline(String conHeadline) {
    emit(state.copyWith(
      conHeadline: conHeadline.trim(),
    ));
  }

  void setConBio(String conBio) {
    emit(state.copyWith(
      conBio: conBio.trim(),
    ));
  }
}
