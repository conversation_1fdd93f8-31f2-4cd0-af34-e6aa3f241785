import 'package:flutter_bloc/flutter_bloc.dart';

abstract class EditorScaffoldViewModel<S> extends Cubit<S> {
  final S _initialState;

  S get initialState => _initialState;

  bool get hasChanges => checkChanges(_initialState, state);

  EditorScaffoldViewModel(this._initialState) : super(_initialState);

  /// Returns true if there are changes
  bool checkChanges(S initialState, S currentState);

  /// Returns S if successful. This will then pop and return S in the navigator
  ///
  /// Throws [EditorScaffoldException] if an error occurs
  Future<S> applyChanges(S initialState, S state);
}
