import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/metadata_doc_status.dart';
import 'package:venvi/constants/metadata_field.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/metadata_view_model.dart';

abstract class MetadataListenerViewModel<T, State> extends Cubit<State> {
  late final StreamSubscription _metadataSubscription;
  final _loadingCompleter = Completer<void>();

  final ConData _conData;
  final MetadataViewModel _metadataViewModel;
  final MetadataField _metadataField;

  MetadataListenerViewModel(super.initialState, this._conData, this._metadataViewModel, this._metadataField) {
    _init();
  }

  Future<void> waitForLoading() async {
    return await _loadingCompleter.future;
  }

  Future<void> _init() async {
    // Listen for metadata updates
    _metadataSubscription = _metadataViewModel.stream.listen((metadataUpdate) async {
      final metadataDocStatus = metadataUpdate?[_metadataField];
      if (metadataDocStatus != null) {
        final successfulIds = await _onMetadataUpdate(metadataDocStatus);
        await _metadataViewModel.saveUpdateTimestamp(_conData, _metadataField, successfulIds);
      } else {
        onData({});
      }
      if (!_loadingCompleter.isCompleted) {
        _loadingCompleter.complete();
      }
    });

    // Initial listen since streams don't initially trigger
    final initialMetadata = _metadataViewModel.state?[_metadataField];
    if (initialMetadata != null) {
      final successfulIds = await _onMetadataUpdate(initialMetadata);
      await _metadataViewModel.saveUpdateTimestamp(_conData, _metadataField, successfulIds);
      if (!_loadingCompleter.isCompleted) {
        _loadingCompleter.complete();
      }
    }
  }

  Future<List<String>> _onMetadataUpdate(Map<String, MetadataDocStatus> metadataUpdate) async {
    // Get data from cache or server depending on MetadataDocStatus
    final Map<String, Future<T?> Function()> getDataFutures = {};
    List<String> deletedIds = [];
    metadataUpdate.forEach((key, value) {
      switch (value) {
        case MetadataDocStatus.upToDate:
          getDataFutures[key] = () async {
            final cachedData = await getData(key, DataLocation.cache);
            if (cachedData != null) {
              return cachedData;
            } else {
              return getData(key, DataLocation.server);
            }
          };
          break;
        case MetadataDocStatus.updateNeeded:
          getDataFutures[key] = () => getData(key, DataLocation.server);
          break;
        case MetadataDocStatus.deleted:
          deletedIds.add(key);
          break;
      }
    });

    await _metadataViewModel.deleteLocalMetadataId(_conData, _metadataField, deletedIds);
    final dataMap = await _waitForData(getDataFutures);
    onData(dataMap);
    return dataMap.keys.toList();
  }

  Future<Map<String, T>> _waitForData(Map<String, Future<T?> Function()> futureFunctions) async {
    final Map<String, T> results = {};
    await Future.wait(
      futureFunctions.keys.map((key) async {
        final value = await futureFunctions[key]!();
        if (value != null) {
          results[key] = value;
        }
      }),
    );
    return results;
  }

  Future<T?> getData(String id, DataLocation dataLocation);

  void onData(Map<String, T> data);

  @override
  Future<void> close() async {
    await _metadataSubscription.cancel();
    return super.close();
  }
}
