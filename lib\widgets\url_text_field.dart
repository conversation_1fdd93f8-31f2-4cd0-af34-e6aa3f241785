import 'package:flutter/material.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/input_field_label.dart';
import 'package:venvi/widgets/surface_input_field.dart';

class UrlTextField extends StatelessWidget {
  final TextEditingController? controller;
  final void Function(String)? onChanged;
  final String label;
  final String prefix;
  final IconData? icon;
  final String? value;
  final TextInputAction textInputAction;
  final bool isTertiary;

  const UrlTextField({
    super.key,
    this.controller,
    this.onChanged,
    required this.label,
    required this.prefix,
    required this.icon,
    required this.value,
    this.textInputAction = TextInputAction.next,
    this.isTertiary = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InputFieldLabel(label: label),
        SurfaceInputField(
          child: TextField(
            controller: controller,
            onChanged: onChanged,
            textInputAction: textInputAction,
            textCapitalization: TextCapitalization.none,
            keyboardType: TextInputType.url,
            decoration: InputDecoration(prefixIcon: icon != null ? Icon(icon) : null),
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(AppTheme.widgetPaddingSmall, 0, AppTheme.widgetPaddingSmall, 0),
          child: Text(
            '$prefix${value ?? ''}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: (isTertiary
                      ? Theme.of(context).colorScheme.onTertiaryContainer
                      : Theme.of(context).colorScheme.onPrimaryContainer)
                  .withValues(alpha: AppTheme.subtitleTextOpacity),
            ),
          ),
        ),
      ],
    );
  }
}
