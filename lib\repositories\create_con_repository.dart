import 'package:cloud_functions/cloud_functions.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/create_con_model.dart';
import 'package:venvi/models/http_create_con_model.dart';
import 'package:venvi/models/http_location_model.dart';
import 'package:venvi/utils/logger.dart';

class CreateConRepository {
  final FirebaseFunctions _functions;

  CreateConRepository(this._functions);

  Future<ConData?> createCon(CreateConModel createConModel) async {
    final httpCreateConModel = _convertModel(createConModel);

    try {
      final result = await _functions.httpsCallable('createCon').call(httpCreateConModel.toJson());

      final Map<String, dynamic>? data = result.data;
      if (data == null) {
        Logger.error(message: 'Failed to create con, data is null');
        return null;
      }

      final orgId = data['orgId'] as String?;
      final conId = data['conId'] as String?;
      return ConData.tryCreate(orgId: orgId, conId: conId);
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace, message: 'Failed to create con');
      return null;
    }
  }

  HttpCreateConModel _convertModel(CreateConModel createConModel) {
    return HttpCreateConModel(
      orgId: createConModel.orgId,
      orgOwnerId: createConModel.orgOwnerId,
      tier: createConModel.tier.name,
      name: createConModel.name,
      location: HttpLocationModel(
        name: createConModel.location.name,
        address: createConModel.location.address,
        latitude: createConModel.location.geoPoint.latitude.toString(),
        longitude: createConModel.location.geoPoint.longitude.toString(),
        timeZoneId: createConModel.location.timeZoneId,
      ),
      startDate: createConModel.startDate.toDate().toUtc().toIso8601String(),
      endDate: createConModel.endDate.toDate().toUtc().toIso8601String(),
      color: createConModel.color,
    );
  }
}
