import 'package:flutter/material.dart';
import 'package:venvi/theme.dart';

class GlassTileIconButton extends StatelessWidget {
  final IconData? icon;
  final Color? color;
  final String? tooltip;
  final void Function()? onTap;

  const GlassTileIconButton({super.key, this.icon, this.color, this.tooltip, this.onTap});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      tooltip: tooltip,
      constraints: const BoxConstraints(minWidth: double.infinity, minHeight: 48),
      style: TextButton.styleFrom(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppTheme.borderRadius)),
      ),
      onPressed: onTap,
      icon: Icon(icon, color: color),
    );
  }
}
