import 'package:freezed_annotation/freezed_annotation.dart';

part 'csv_row_string_model.freezed.dart';

@freezed
sealed class CsvRowStringModel with _$CsvRowStringModel {
  const factory CsvRowStringModel({
    String? id,
    String? startDate,
    String? startTime,
    String? endDate,
    String? endTime,
    String? title,
    String? desc,
    String? venueName,
    String? spotlight,
    String? adultOnly,
    String? eventType,
    String? tag1,
    String? tag2,
    String? tag3,
    String? adminUsername,
  }) = _CsvRowStringModel;
}
