import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:venvi/constants/metadata_field.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/metadata_model.dart';
import 'package:venvi/utils/logger.dart';

class MetadataLocalDataSource {
  static const _metadataKey = 'metadata';

  final SharedPreferencesAsync _sharedPreferences;

  MetadataLocalDataSource(this._sharedPreferences);

  Future<MetadataModel?> getMetadata(ConData conData) async {
    try {
      final jsonString = await _sharedPreferences.getString(_getMetadataKey(conData));
      if (jsonString != null) {
        final Map<String, dynamic> data = jsonDecode(jsonString);
        final MetadataModel metadata = {};
        data.forEach((key, value) {
          final metadataField = MetadataField.fromName(key);
          if (metadataField != null && value is Map<String, dynamic>) {
            final Map<String, Timestamp> convertedMap = {};
            value.forEach((subKey, subValue) {
              final dateTime = DateTime.tryParse(subValue.toString());
              if (dateTime != null) {
                convertedMap[subKey] = Timestamp.fromDate(dateTime);
              }
            });
            if (convertedMap.isNotEmpty) {
              metadata[metadataField] = convertedMap;
            }
          }
        });
        return metadata.isNotEmpty ? metadata : null;
      }
      return null;
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace);
      return null;
    }
  }

  Future<bool> saveMetadataJson(ConData conData, MetadataModel metadata) async {
    try {
      final Map<String, dynamic> data = {};
      metadata.forEach((key, value) {
        final Map<String, String> convertedMap = {};
        value.forEach((subKey, subValue) {
          convertedMap[subKey] = subValue.toDate().toUtc().toIso8601String();
        });
        data[key.name] = convertedMap;
      });
      await _sharedPreferences.setString(_getMetadataKey(conData), jsonEncode(data));
      return true;
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace);
      return false;
    }
  }

  String _getMetadataKey(ConData conData) => '${_metadataKey}_${conData.orgId}_${conData.conId}';
}
