import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:uuid/uuid.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/general_content_view_model.dart';
import 'package:venvi/models/general_content_model.dart';
import 'package:venvi/repositories/general_content_repository.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/screens/general_content/general_content_page_editor_state.dart';
import 'package:venvi/screens/general_content/general_content_page_editor_view_model.dart';
import 'package:venvi/screens/general_content/general_content_tile.dart';
import 'package:venvi/screens/general_content/general_content_tile_editor_bottom_sheet.dart';
import 'package:venvi/screens/general_content/general_content_tile_state.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/bottom_sheets.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/image_selector.dart';
import 'package:venvi/utils/snack_bars.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/content_group.dart';
import 'package:venvi/widgets/editor_scaffold.dart';
import 'package:venvi/widgets/glass_tile_button.dart';
import 'package:venvi/widgets/image_updater_view.dart';
import 'package:venvi/widgets/surface_input_field.dart';

class GeneralContentPageEditorScreen extends HookWidget {
  final String? generalContentId;

  const GeneralContentPageEditorScreen({super.key, this.generalContentId});

  @override
  Widget build(BuildContext context) {
    final scrollController = useScrollController();
    final titleController = useTextEditingController();

    return EditorScaffold<
      GeneralContentPageEditorViewModel,
      GeneralContentPageEditorState,
      GeneralContentViewModel,
      List<GeneralContentModel>?
    >(
      externalCubit: context.read<GeneralContentViewModel>(),
      buildState: (externalCubitState) {
        late final GeneralContentModel? page;
        try {
          page = externalCubitState?.firstWhere((element) => element.id == generalContentId);
        } catch (e) {
          page = null;
        }

        // If the page is not found
        if (generalContentId != null && page == null) {
          return null;
        }

        final orderIndex = page?.orderIndex ?? externalCubitState?.length;

        final List<GeneralContentTileState> tileStates = [];
        for (int i = 0; i < (page?.tiles?.length ?? 0); i++) {
          if (page?.tiles?.containsKey(i) == true) {
            final tileModel = page!.tiles![i]!;
            tileStates.add(GeneralContentTileState(key: const Uuid().v4(), model: tileModel));
          }
        }

        return GeneralContentPageEditorState(
          orderIndex: orderIndex,
          title: page?.title,
          primaryImage: page?.primaryImage,
          tiles: tileStates,
        );
      },
      initViewModel: (state) {
        titleController.text = state.title ?? '';

        return GeneralContentPageEditorViewModel(
          state,
          generalContentId,
          context.read<ConData>(),
          context.read<GeneralContentRepository>(),
          context.read<ImageRepository>(),
        );
      },
      title: const Text('Page Editor'),
      successMessage: generalContentId == null ? 'Content Created' : 'Changes Saved',
      buildContent:
          (context, viewModel, state) => ListView(
            padding: const EdgeInsets.all(AppTheme.screenPadding),
            controller: scrollController,
            children: [
              ContentGroup(
                title: 'Page Name',
                child: SurfaceInputField(
                  child: TextField(
                    controller: titleController,
                    onChanged: (value) => viewModel.updateTitle(value),
                    textInputAction: TextInputAction.next,
                    textCapitalization: TextCapitalization.words,
                    textAlign: TextAlign.center,
                    maxLength: InputConstants.maxGeneralContentSectionTitleLength,
                    decoration: const InputDecoration(hintText: 'Page Name', counterText: ''),
                  ),
                ),
              ),
              const SizedBox(height: AppTheme.widgetPadding),
              ContentGroup(
                title: 'Primary Image',
                child: ImageUpdaterView(
                  imageFile: state.primaryImageOverrideFile,
                  imageUrl: state.primaryImage?.downloadUrl,
                  onTap: () async {
                    final file = await ImageSelector().selectImage(context, cropAspectRatio: 1);
                    if (file != null && context.mounted) {
                      viewModel.updatePrimaryImage(file);
                    }
                  },
                ),
              ),
              if (state.tiles.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: state.tiles.length,
                    separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                    itemBuilder: (context, index) {
                      final tileState = state.tiles[index];
                      return GeneralContentTile(
                        key: ValueKey('${context.read<ConData>().conWidgetKey}-${tileState.key}'),
                        model: tileState.model,
                        imageOverride: tileState.imageFile,
                        isTappable: false,
                        toolbar: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.arrow_upward),
                              onPressed: index > 0 ? () => viewModel.moveTile(index, index - 1) : null,
                            ),
                            IconButton(
                              icon: const Icon(Icons.arrow_downward),
                              onPressed:
                                  index < state.tiles.length - 1 ? () => viewModel.moveTile(index, index + 1) : null,
                            ),
                            IconButton(
                              icon: const Icon(Icons.edit),
                              onPressed:
                                  () => BottomSheets.showBottomSheet(
                                    context: context,
                                    child: GeneralContentTileEditorBottomSheet(
                                      tileState: tileState,
                                      onTileUpdated: (tileState) => viewModel.updateTile(index, tileState),
                                      onDelete: () => viewModel.removeTile(index),
                                    ),
                                  ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete),
                              onPressed: () async {
                                final confirmed = await Dialogs.showConfirmationDialog(context, title: 'Remove Tile?');
                                if (confirmed == true && context.mounted) {
                                  viewModel.removeTile(index);
                                }
                              },
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              if (state.tiles.length < InputConstants.maxGeneralContentSections)
                Padding(
                  padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                  child: ContentArea(
                    padding: EdgeInsets.zero,
                    child: GlassTileButton(
                      text: 'Add Tile',
                      icon: Icons.add,
                      onTap: () {
                        viewModel.addTile();
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          scrollController.animateTo(
                            scrollController.position.maxScrollExtent,
                            duration: const Duration(milliseconds: 500),
                            curve: Curves.easeInOut,
                          );
                        });
                      },
                    ),
                  ),
                ),
              if (generalContentId != null)
                Padding(
                  padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                  child: ContentArea(
                    padding: EdgeInsets.zero,
                    child: GlassTileButton(
                      text: 'Delete Content Page',
                      icon: Icons.delete,
                      onTap: () async {
                        final confirmDelete = await Dialogs.showConfirmationDialog(
                          context,
                          title: 'Delete Content Page?',
                          message: state.title,
                        );
                        if (confirmDelete == true && context.mounted) {
                          final result = await context.read<GeneralContentPageEditorViewModel>().deletePage(
                            context.read<ConData>(),
                            context.read<GeneralContentRepository>(),
                          );
                          if (context.mounted) {
                            if (result) {
                              SnackBars.showInfoSnackBar(context, 'Content page deleted');
                              context.pop();
                              context.pop();
                            } else {
                              SnackBars.showInfoSnackBar(context, 'Failed to delete content page');
                            }
                          }
                        }
                      },
                    ),
                  ),
                ),
            ],
          ),
    );
  }
}
