import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/profile_model.dart';

part 'edit_profile_state.freezed.dart';

@freezed
sealed class EditProfileState with _$EditProfileState {
  const factory EditProfileState({
    required ProfileModel profileModel,
    required bool? useConHeadline,
    required String? conHeadline,
    required bool? useConBio,
    required String? conBio,
    String? usernameErrorLock,
  }) = _EditProfileState;
}
