import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/utils/snack_bars.dart';

class ShareButton extends StatelessWidget {
  final ConData conData;
  final String? text;
  final String? path;

  const ShareButton({super.key, required this.conData, this.text, this.path});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ElevatedButton.icon(
        icon: Theme.of(context).platform == TargetPlatform.iOS ? const Icon(Icons.ios_share) : const Icon(Icons.share),
        label: const Text('Share'),
        onPressed: () {
          final deeplinkPath = 'https://web.venvi.app/#${conData.conPath}${path != null ? '/$path' : ''}';
          if (Theme.of(context).platform == TargetPlatform.android ||
              Theme.of(context).platform == TargetPlatform.iOS) {
            SharePlus.instance.share(ShareParams(text: '${text != null ? '$text\n' : ''}$deeplinkPath'));
          } else {
            Clipboard.setData(ClipboardData(text: deeplinkPath));
            SnackBars.showInfoSnackBar(context, 'Copied to clipboard');
          }
        },
        style: ElevatedButton.styleFrom(
          foregroundColor: Theme.of(context).colorScheme.onSecondary,
          iconColor: Theme.of(context).colorScheme.onSecondary,
          backgroundColor: Theme.of(context).colorScheme.secondary,
        ),
      ),
    );
  }
}
