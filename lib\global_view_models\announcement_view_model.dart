import 'dart:async';

import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/metadata_field.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/metadata_listener_view_model.dart';
import 'package:venvi/global_view_models/metadata_view_model.dart';
import 'package:venvi/models/announcement_model.dart';
import 'package:venvi/repositories/announcement_repository.dart';

class AnnouncementViewModel extends MetadataListenerViewModel<AnnouncementModel, List<AnnouncementModel>?> {
  final ConData _conData;
  final AnnouncementRepository _repository;

  AnnouncementViewModel(this._conData, this._repository, MetadataViewModel metadataViewModel)
      : super(null, _conData, metadataViewModel, MetadataField.announcements);

  @override
  Future<AnnouncementModel?> getData(String id, DataLocation dataLocation) async {
    return await _repository.getAnnouncement(_conData, id, dataLocation: dataLocation);
  }

  @override
  void onData(Map<String, AnnouncementModel> data) {
    if (isClosed) {
      return;
    }

    final sortedData = data.values.toList();
    sortedData.sort((a, b) => a.timestamp == null
        ? 1
        : b.timestamp == null
            ? -1
            : b.timestamp!.compareTo(a.timestamp!));
    emit(sortedData);
  }
}
