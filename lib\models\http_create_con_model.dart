import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/http_location_model.dart';

part 'http_create_con_model.freezed.dart';
part 'http_create_con_model.g.dart';

@freezed
sealed class HttpCreateConModel with _$HttpCreateConModel {
  const factory HttpCreateConModel({
    String? orgId,
    String? orgOwnerId,
    required String tier,
    required String name,
    required HttpLocationModel location,
    required String startDate,
    required String endDate,
    required String color,
  }) = _HttpCreateConModel;

  factory HttpCreateConModel.fromJson(Map<String, dynamic> json) => _$HttpCreateConModelFromJson(json);
}
