import 'dart:async';

import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/data_sources/metadata_local_data_source.dart';
import 'package:venvi/data_sources/metadata_remote_data_source.dart';
import 'package:venvi/models/metadata_model.dart';

class MetadataRepository {
  final MetadataRemoteDataSource _metadataRemoteDataSource;
  final MetadataLocalDataSource _metadataLocalDataSource;

  MetadataRepository(this._metadataRemoteDataSource, this._metadataLocalDataSource);

  Stream<MetadataModel?> getMetadataStream(ConData conData) {
    return _metadataRemoteDataSource.getMetadataStream(conData);
  }

  Future<MetadataModel?> getLocalMetadata(ConData conData) async {
    return await _metadataLocalDataSource.getMetadata(conData);
  }

  Future<bool> saveLocalMetadata(ConData conData, MetadataModel metadata) async {
    return await _metadataLocalDataSource.saveMetadataJson(conData, metadata);
  }
}
