import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/image_model.dart';

part 'general_content_tile_model.freezed.dart';
part 'general_content_tile_model.g.dart';

@freezed
sealed class GeneralContentTileModel with _$GeneralContentTileModel {
  const factory GeneralContentTileModel({
    ImageModel? image,
    String? headlineText,
    String? detailsText,
    String? externalLink,
  }) = _GeneralContentTileModel;

  factory GeneralContentTileModel.fromJson(Map<String, dynamic> json) => _$GeneralContentTileModelFromJson(json);
}
