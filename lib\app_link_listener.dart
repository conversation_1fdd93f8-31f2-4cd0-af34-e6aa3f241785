import 'package:app_links/app_links.dart';
import 'package:venvi/router.dart';

class AppLinkListener {
  static final AppLinkListener _instance = AppLinkListener._internal();
  static bool _isInitialized = false;

  factory AppLinkListener(AppLinks? appLinks) {
    if (!_isInitialized) {
      _isInitialized = true;
      if (appLinks != null) {
        _instance._init(appLinks);
      }
    }
    return _instance;
  }

  AppLinkListener._internal();

  Future<void> _init(AppLinks appLinks) async {
    final initialUri = await appLinks.getInitialLink();
    _handleUri(initialUri);
    appLinks.uriLinkStream.listen((uri) => _handleUri(uri));
  }

  void _handleUri(Uri? uri) {
    final fragment = uri?.fragment;
    if (fragment != null && fragment.isNotEmpty) {
      AppRouter.router.go(fragment);
    }
  }
}
