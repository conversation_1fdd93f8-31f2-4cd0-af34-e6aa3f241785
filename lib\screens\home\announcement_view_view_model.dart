import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/announcement_view_model.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/models/announcement_model.dart';
import 'package:venvi/models/user_model.dart';
import 'package:venvi/screens/home/<USER>';

class AnnouncementViewViewModel extends Cubit<AnnouncementViewState> {
  final ConData conData;

  late final StreamSubscription _userSubscription;
  late final StreamSubscription _announcementSubscription;

  late List<String> _readAnnouncementIds;
  List<AnnouncementModel>? _announcements;

  AnnouncementViewViewModel(this.conData, UserViewModel userViewModel, AnnouncementViewModel announcementViewModel)
    : super(const AnnouncementViewState(showRead: false)) {
    _readAnnouncementIds = userViewModel.state.getReadAnnouncements(conData);
    _announcements = announcementViewModel.state;
    _onUpdate();
    _userSubscription = userViewModel.stream.listen((event) {
      final newReadAnnouncementIds = event.getReadAnnouncements(conData);
      // Checks if anything relevant has changed
      if (_readAnnouncementIds.length != newReadAnnouncementIds.length ||
          _readAnnouncementIds.any((element) => !newReadAnnouncementIds.contains(element))) {
        _readAnnouncementIds = newReadAnnouncementIds;
        _onUpdate();
      }
    });
    _announcementSubscription = announcementViewModel.stream.listen((event) {
      _announcements = event;
      _onUpdate();
    });
  }

  @override
  Future<void> close() {
    _userSubscription.cancel();
    _announcementSubscription.cancel();
    return super.close();
  }

  void toggleShowRead() {
    emit(state.copyWith(showRead: !state.showRead));
  }

  void _onUpdate() {
    final allAnnouncements = _announcements ?? [];
    final readIds = _readAnnouncementIds;

    final List<AnnouncementModel> unread = [];
    final List<AnnouncementModel> read = [];
    for (final announcement in allAnnouncements) {
      if (readIds.contains(announcement.id)) {
        read.add(announcement);
      } else {
        unread.add(announcement);
      }
    }
    emit(state.copyWith(unreadAnnouncements: unread, readAnnouncements: read));
  }
}
