import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/permissions.dart';
import 'package:venvi/utils/logger.dart';

class PermissionListConverter implements JsonConverter<List<Permissions>?, List<dynamic>?> {
  const PermissionListConverter();

  @override
  List<Permissions>? fromJson(List<dynamic>? json) {
    if (json == null) {
      return null;
    }
    return json
        .whereType<String>()
        .map((element) {
          try {
            return Permissions.values.firstWhere((permission) => permission.name == element);
          } catch (e, stackTrace) {
            Logger.error(exception: e, stackTrace: stackTrace, message: 'Could not find enum Role for $element');
            return null;
          }
        })
        .whereType<Permissions>()
        .toList();
  }

  @override
  List<String>? toJson(List<Permissions>? object) {
    if (object == null) {
      return null;
    }
    return object.map((e) => e.name).toList();
  }
}
