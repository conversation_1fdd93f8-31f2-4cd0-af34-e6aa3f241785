import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/role_view_model.dart';
import 'package:venvi/models/role_model.dart';
import 'package:venvi/repositories/role_repository.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/snack_bars.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/glass_tile_icon_button.dart';

class RolesListAdminScreen extends StatelessWidget {
  const RolesListAdminScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: GlassAppBar(
        title: const Text('Roles'),
        actions: [
          IconButton(
            tooltip: 'Change Order',
            icon: const Icon(Icons.swap_vert),
            onPressed: () => context.go('${context.read<ConData>().conPath}/admin/roles/reorder'),
          ),
        ],
      ),
      body: BlocBuilder<RoleViewModel, List<RoleModel>?>(
        builder:
            (context, state) => Column(
              children: [
                Expanded(
                  child: ListView(
                    padding: const EdgeInsets.all(AppTheme.screenPadding),
                    children: [
                      ContentArea(
                        padding: EdgeInsets.zero,
                        child: ListTile(
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: AppTheme.widgetPadding,
                            vertical: AppTheme.widgetPaddingSmall,
                          ),
                          title: const Text('All Participants'),
                          onTap: () => context.go('${context.read<ConData>().conPath}/admin/roles/participant-manager'),
                          trailing: const Icon(Icons.chevron_right),
                        ),
                      ),
                      state != null
                          ? Padding(
                            padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                            child: ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: state.length,
                              separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                              itemBuilder: (context, index) {
                                final roleModel = state[index];
                                return ContentArea(
                                  padding: EdgeInsets.zero,
                                  child: ListTile(
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: AppTheme.widgetPadding,
                                      vertical: AppTheme.widgetPaddingSmall,
                                    ),
                                    title: Text(
                                      roleModel.name ?? '',
                                      style: Theme.of(context).listTileTheme.titleTextStyle,
                                    ),
                                    onTap:
                                        () => context.go(
                                          '${context.read<ConData>().conPath}/admin/roles/role-manager/${roleModel.id}',
                                        ),
                                    trailing: const Icon(Icons.chevron_right),
                                  ),
                                );
                              },
                            ),
                          )
                          : const SizedBox.shrink(),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(AppTheme.screenPadding),
                  child: ContentArea(
                    padding: EdgeInsets.zero,
                    child: GlassTileIconButton(
                      icon: Icons.add,
                      tooltip: 'Add Role',
                      onTap: () async {
                        final newRole = await Dialogs.showTextInputDialog(
                          context,
                          title: 'Add Role',
                          hintText: 'Role Name',
                          maxLength: InputConstants.maxRoleNameLength,
                        );
                        if (context.mounted && newRole != null && newRole.isNotEmpty) {
                          final id = await context.read<RoleRepository>().createRole(
                            context.read<ConData>(),
                            RoleModel(name: newRole, orderIndex: state?.length),
                          );
                          if (context.mounted) {
                            if (id != null) {
                              SnackBars.showInfoSnackBar(context, 'Role created, it may take a minute to appear');
                            } else {
                              SnackBars.showInfoSnackBar(context, 'Role creation failed');
                            }
                          }
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),
      ),
    );
  }
}
