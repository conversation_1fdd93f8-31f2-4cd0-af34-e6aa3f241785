import 'dart:async';

import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/profile_model.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/repositories/role_repository.dart';
import 'package:venvi/screens/roles_and_participants/role_manager_state.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class RoleManagerViewModel extends EditorScaffoldViewModel<RoleManagerState> {
  final String _roleId;
  final ConData _conData;
  final RoleRepository _roleRepository;
  final ParticipantViewModel _participantViewModel;
  final ParticipantRepository _participantRepository;

  RoleManagerViewModel(
    super.initialState,
    this._roleId,
    this._conData,
    this._roleRepository,
    this._participantViewModel,
    this._participantRepository,
  );

  @override
  bool checkChanges(RoleManagerState initialState, RoleManagerState currentState) {
    return initialState.roleName != currentState.roleName ||
        initialState.regularParticipants.length != currentState.regularParticipants.length ||
        initialState.spotlightParticipants.length != currentState.spotlightParticipants.length ||
        !initialState.regularParticipants.every((element) => currentState.regularParticipants.contains(element)) ||
        !initialState.spotlightParticipants.every((element) => currentState.spotlightParticipants.contains(element));
  }

  @override
  Future<RoleManagerState> applyChanges(RoleManagerState initialState, RoleManagerState state) async {
    // Participants changed
    Future<bool>? participantFuture;
    if (initialState.regularParticipants.length != state.regularParticipants.length ||
        initialState.spotlightParticipants.length != state.spotlightParticipants.length ||
        !initialState.regularParticipants.every((element) => state.regularParticipants.contains(element)) ||
        !initialState.spotlightParticipants.every((element) => state.spotlightParticipants.contains(element))) {
      final initialAllParticipants = initialState.regularParticipants + initialState.spotlightParticipants;
      final allParticipants = state.regularParticipants + state.spotlightParticipants;

      final conParticipantIds = _participantViewModel.state?.map((e) => e.id).whereType<String>().toList() ?? [];
      final newParticipants = allParticipants.where((element) => !conParticipantIds.contains(element.id)).toList();

      // Create new participants
      final List<Future<String?>> participantCreationFutures = [];
      for (final participant in newParticipants) {
        participantCreationFutures.add(_participantRepository.addParticipant(_conData, participant.id!, participant));
      }
      final result = await Future.wait(participantCreationFutures);
      final success = result.every((element) => element != null);

      if (!success) {
        throw const EditorScaffoldException(['Failed to create participants']);
      }

      final removedParticipantIds = initialAllParticipants
          .where((element) => !allParticipants.contains(element))
          .map((e) => e.id)
          .whereType<String>()
          .toList();
      final spotlightParticipantIds = state.spotlightParticipants.map((e) => e.id).whereType<String>().toList();
      final allParticipantIds = allParticipants.map((e) => e.id).whereType<String>().toList();

      participantFuture = _participantRepository.updateRole(
          _conData, _roleId, allParticipantIds, spotlightParticipantIds, removedParticipantIds);
    }

    // Role changed
    Future<bool>? roleFuture;
    final newName = state.roleName?.trim() ?? '';
    if (initialState.roleName != newName) {
      roleFuture = _roleRepository.updateName(_conData, _roleId, newName);
    }

    if (await participantFuture == false) {
      throw const EditorScaffoldException(['Failed to update participants']);
    }

    if (await roleFuture == false) {
      throw const EditorScaffoldException(['Failed to update title']);
    }

    return state;
  }

  void updateRoleName(String name) {
    emit(state.copyWith(roleName: name.trim()));
  }

  void addParticipantFromProfile(ProfileModel model) {
    addParticipant(ParticipantModel.fromProfileModel(model, null, null));
  }

  void addParticipant(ParticipantModel model) {
    emit(state.copyWith(
      regularParticipants: [
        ...state.regularParticipants,
        model,
      ],
    ));
  }

  void removeParticipant(String participantId) {
    emit(state.copyWith(
      regularParticipants: state.regularParticipants.where((element) => element.id != participantId).toList(),
      spotlightParticipants: state.spotlightParticipants.where((element) => element.id != participantId).toList(),
    ));
  }

  void setParticipantSpotlight(String participantId, bool spotlight) {
    ParticipantModel? participant;
    try {
      participant = state.regularParticipants.firstWhere((element) => element.id == participantId);
    } catch (_) {
      try {
        participant = state.spotlightParticipants.firstWhere((element) => element.id == participantId);
      } catch (_) {
        return;
      }
    }
    final spotlightParticipants = state.spotlightParticipants.toList();
    final regularParticipants = state.regularParticipants.toList();
    if (spotlight) {
      regularParticipants.remove(participant);
      spotlightParticipants.add(participant);
    } else {
      spotlightParticipants.remove(participant);
      regularParticipants.add(participant);
    }
    emit(state.copyWith(
      spotlightParticipants: spotlightParticipants,
      regularParticipants: regularParticipants,
    ));
  }

  Future<bool> deleteRole(ConData conData, RoleRepository roleRepository) async {
    return await roleRepository.deleteRole(conData, _roleId);
  }
}
