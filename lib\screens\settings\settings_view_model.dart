import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/global_view_models/auth_view_model.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/models/user_model.dart';
import 'package:venvi/repositories/device_repository.dart';
import 'package:venvi/screens/settings/settings_state.dart';

class SettingsViewModel extends Cubit<SettingsState> {
  final AuthViewModel _authViewModel;
  final UserViewModel _userViewModel;
  final DeviceRepository _deviceRepository;

  late final StreamSubscription<UserModel> _userSubscription;

  SettingsViewModel(
    this._authViewModel,
    this._userViewModel,
    this._deviceRepository,
  ) : super(SettingsState(deviceNotificationsEnabled: false)) {
    _userSubscription = _userViewModel.stream.listen(
      (state) => _checkDeviceNotificationsEnabled(state),
    );
    _checkDeviceNotificationsEnabled(_userViewModel.state);
  }

  Future<void> _checkDeviceNotificationsEnabled(UserModel? userModel) async {
    if (await _deviceRepository.deviceNotificationsEnabled(_authViewModel.state?.uid, userModel)) {
      emit(state.copyWith(deviceNotificationsEnabled: true));
    } else {
      emit(state.copyWith(deviceNotificationsEnabled: false));
    }
  }

  @override
  Future<void> close() {
    _userSubscription.cancel();
    return super.close();
  }
}
