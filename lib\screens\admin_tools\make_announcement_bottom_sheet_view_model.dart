import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/announcement_model.dart';
import 'package:venvi/repositories/announcement_repository.dart';

class MakeAnnouncementBottomSheetViewModel extends Cubit<bool> {
  String _headline = '';
  String _details = '';

  MakeAnnouncementBottomSheetViewModel() : super(false);

  void setHeadline(String headline) {
    _headline = headline.trim();
    emit(_headline.isNotEmpty);
  }

  void setDetails(String details) {
    _details = details.trim();
  }

  Future<bool> makeAnnouncement(ConData conData, AnnouncementRepository announcementRepository, String? userId) async {
    final docId = await announcementRepository.createAnnouncement(
      conData,
      AnnouncementModel(
        createdBy: userId,
        headline: _headline,
        details: _details,
      ),
    );
    return docId != null;
  }
}
