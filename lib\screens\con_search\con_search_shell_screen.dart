import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:venvi/global_view_models/analytics_consent_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/adaptive_view.dart';
import 'package:venvi/widgets/analytics_consent_banner.dart';
import 'package:venvi/widgets/gradient_background.dart';

class ConSearchShellScreen extends StatefulWidget {
  final Widget child;

  const ConSearchShellScreen({super.key, required this.child});

  @override
  State<ConSearchShellScreen> createState() => _ConSearchShellScreenState();
}

class _ConSearchShellScreenState extends State<ConSearchShellScreen> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) => context.read<AnalyticsConsentViewModel>().tryShowInitialConsentDialog(context),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveView(
      mobileView: GradientBackground(child: Stack(children: [widget.child, AnalyticsConsentBanner()])),
      desktopView: GradientBackground(
        child: Stack(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(AppTheme.screenPadding),
                  child: TextButton(
                    onPressed: () => context.go('/home'),
                    style: Theme.of(context).textButtonTheme.style?.copyWith(
                      padding: WidgetStateProperty.all(const EdgeInsets.all(AppTheme.widgetPadding)),
                    ),
                    child: Text(
                      'Venvi',
                      style: TextStyle(
                        fontFamily: 'Comfortaa',
                        fontWeight: FontWeight.w900,
                        letterSpacing: 2,
                        fontSize: 20,
                        color: Theme.of(context).appBarTheme.foregroundColor,
                      ),
                    ),
                  ),
                ),
                Expanded(child: widget.child),
              ],
            ),
            AnalyticsConsentBanner(),
          ],
        ),
      ),
    );
  }
}
