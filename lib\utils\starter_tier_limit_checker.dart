import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/global_view_models/event_view_model.dart';

class StarterTierLimitChecker {
  static bool isEventLimitReached(BuildContext context) {
    if (context.read<ConViewModel>().isPremium()) {
      return false;
    }

    final events = context.read<EventViewModel>().state;
    final eventCount = events?.length ?? 0;
    return eventCount >= InputConstants.maxStarterTierEvents;
  }
}
