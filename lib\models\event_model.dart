// ignore_for_file: invalid_annotation_target

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/event_type.dart';
import 'package:venvi/json_converters/timestamp_converter.dart';
import 'package:venvi/models/event_participant_model.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/models/venue_snippet_model.dart';

part 'event_model.freezed.dart';
part 'event_model.g.dart';

@freezed
sealed class EventModel with _$EventModel {
  const factory EventModel({
    String? id,
    String? copyOf,
    bool? spotlight,
    String? primaryImageId,
    List<String>? galleryImageIds,
    Map<String, ImageModel>? imageData,
    String? title,
    String? desc,
    VenueSnippetModel? venue,
    @TimestampConverter() Timestamp? startTime,
    @TimestampConverter() Timestamp? endTime,
    @JsonKey(unknownEnumValue: JsonKey.nullForUndefinedEnumValue) EventType? type,
    List<String>? tags,
    bool? adultOnly,
    String? adminId,
    List<String>? participantIds,
    List<String>? roleIds,
    List<EventParticipantModel>? participants,
  }) = _EventModel;

  factory EventModel.fromJson(Map<String, dynamic> json) => _$EventModelFromJson(json);
}

extension EventModelExtension on EventModel {
  ImageModel? getImageData(String? imageId) {
    if (imageData == null || imageId == null) {
      return null;
    }
    return imageData![imageId];
  }
}
