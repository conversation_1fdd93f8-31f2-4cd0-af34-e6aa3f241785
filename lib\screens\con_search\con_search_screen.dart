import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/models/org_active_con_model.dart';
import 'package:venvi/repositories/org_repository.dart';
import 'package:venvi/screens/con_search/con_search_state.dart';
import 'package:venvi/screens/con_search/con_search_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/accent_label.dart';
import 'package:venvi/widgets/con_list_item.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/content_group.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/host_con_button.dart';
import 'package:venvi/widgets/legal_banner.dart';
import 'package:venvi/widgets/my_account_icon_button.dart';
import 'package:venvi/widgets/search_view.dart';

class ConSearchScreen extends HookWidget {
  const ConSearchScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final searchFocusNode = useFocusNode();

    return BlocProvider(
      create: (context) => ConSearchViewModel(context.read<OrgRepository>(), context.read<UserViewModel>()),
      child: Builder(
        builder:
            (context) => Scaffold(
              appBar: const GlassAppBar(
                title: Text(
                  'Venvi',
                  style: TextStyle(
                    fontFamily: 'Comfortaa',
                    fontWeight: FontWeight.w900,
                    letterSpacing: 4,
                    fontSize: 28,
                  ),
                ),
                actions: [MyAccountIconButton()],
              ),
              body: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.fromLTRB(
                      AppTheme.screenPadding,
                      0,
                      AppTheme.screenPadding,
                      AppTheme.widgetPaddingSmall,
                    ),
                    child: HostConButton(),
                  ),
                  Expanded(
                    child: SearchView(
                      focusNode: searchFocusNode,
                      onSearchChange: (searchText) => context.read<ConSearchViewModel>().setSearchText(searchText),
                      child: BlocBuilder<ConSearchViewModel, ConSearchState>(
                        builder: (context, state) {
                          if (!state.isSearching) {
                            if (state.favoriteConModels.isNotEmpty || state.authorizedConModels.isNotEmpty) {
                              return ListView(
                                children: [
                                  if (state.favoriteConModels.isNotEmpty)
                                    const Padding(
                                      padding: EdgeInsets.only(top: AppTheme.widgetPaddingSmall),
                                      child: AccentLabel(text: 'Favorites', isOnContainer: false),
                                    ),
                                  if (state.favoriteConModels.isNotEmpty)
                                    _buildConList(context, state.favoriteConModels, state.favoriteOrgIds, true),
                                  if (state.authorizedConModels.isNotEmpty)
                                    const Padding(
                                      padding: EdgeInsets.only(top: AppTheme.widgetPaddingSmall),
                                      child: AccentLabel(text: 'Authorized', isOnContainer: false),
                                    ),
                                  if (state.authorizedConModels.isNotEmpty)
                                    _buildConList(context, state.authorizedConModels, state.favoriteOrgIds, true),
                                ],
                              );
                            }
                          } else if (state.conSearchModels.isNotEmpty) {
                            return _buildConList(context, state.conSearchModels, state.favoriteOrgIds, false);
                          }

                          return Center(
                            child: SizedBox(
                              width: 250,
                              child: ContentGroup(
                                title: 'Start a search',
                                additionalTitlePadding: true,
                                child: ElevatedButton.icon(
                                  onPressed: () => searchFocusNode.requestFocus(),
                                  icon: const Icon(Icons.search),
                                  label: const Text('Search Now'),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  const Padding(
                    padding: EdgeInsets.fromLTRB(
                      AppTheme.screenPadding,
                      AppTheme.widgetPaddingSmall,
                      AppTheme.screenPadding,
                      AppTheme.screenPadding,
                    ),
                    child: LegalBanner(),
                  ),
                ],
              ),
            ),
      ),
    );
  }

  Widget _buildConList(
    BuildContext context,
    List<OrgActiveConModel> conModels,
    List<String>? favoriteOrgIds,
    bool shrinkWrap,
  ) {
    return ListView.separated(
      shrinkWrap: shrinkWrap,
      physics: shrinkWrap ? const NeverScrollableScrollPhysics() : null,
      padding: const EdgeInsets.fromLTRB(
        AppTheme.screenPadding,
        AppTheme.widgetPadding,
        AppTheme.screenPadding,
        AppTheme.widgetPadding,
      ),
      itemCount: conModels.length,
      separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
      itemBuilder: (context, index) {
        final conModel = conModels[index];
        return ContentArea(
          padding: EdgeInsets.zero,
          child: ConListItem(conModel: conModel, isFavorite: favoriteOrgIds?.contains(conModel.orgId) ?? false),
        );
      },
    );
  }
}
