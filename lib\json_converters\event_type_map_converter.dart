import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/con_event_type_model.dart';

class EventTypeMapConverter implements JsonConverter<Map<String, ConEventTypeModel>, Map<String, dynamic>> {
  const EventTypeMapConverter();

  @override
  Map<String, ConEventTypeModel> from<PERSON>son(Map<String, dynamic> json) {
    return json.map((key, value) => MapEntry(key, ConEventTypeModel.fromJson(value)));
  }

  @override
  Map<String, dynamic> toJson(Map<String, ConEventTypeModel> dateTimeMap) {
    return dateTimeMap.map((key, value) => MapEntry(key, value.toJson()));
  }
}
