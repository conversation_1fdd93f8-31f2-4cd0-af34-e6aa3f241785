import 'package:flutter/material.dart';

enum TypeIcon {
  art(icon: Icons.brush_outlined),
  competition(icon: Icons.emoji_events_outlined),
  concert(icon: Icons.music_note_outlined),
  car(icon: Icons.directions_car_outlined),
  dashboard(icon: Icons.dashboard_customize_outlined),
  dining(icon: Icons.restaurant_outlined),
  educational(icon: Icons.menu_book_outlined),
  film(icon: Icons.movie_outlined),
  happy(icon: Icons.sentiment_satisfied_alt_outlined),
  health(icon: Icons.directions_run_outlined),
  info(icon: Icons.info_outlined),
  lab(icon: Icons.science_outlined),
  lecture(icon: Icons.co_present_outlined),
  nightlife(icon: Icons.nightlife_outlined),
  photography(icon: Icons.camera_alt_outlined),
  shopping(icon: Icons.shopping_bag_outlined),
  sightSeeing(icon: Icons.landscape_outlined),
  social(icon: Icons.groups_outlined),
  sports(icon: Icons.sports_baseball_outlined),
  theater(icon: Icons.theater_comedy_outlined),
  ticket(icon: Icons.local_activity_outlined),
  tours(icon: Icons.tour_outlined),
  videoGames(icon: Icons.videogame_asset_outlined),
  workshop(icon: Icons.handyman_outlined);

  const TypeIcon({required this.icon});

  final IconData icon;
}
