import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/con_model.dart';
import 'package:venvi/models/financial_model.dart';

part 'org_manager_state.freezed.dart';

@freezed
sealed class OrgManagerState with _$OrgManagerState {
  const factory OrgManagerState({
    required bool isSearchable,
    String? activeConId,
    required bool loadingCons,
    required List<ConModel> conModels,
    required List<String> publishedConIds,
    FinancialModel? financialModel,
    @Default(false) bool showSendInvoiceButton,
    String? missingSendInvoiceMessage,
  }) = _OrgManagerState;
}
