import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/type_icon.dart';

part 'con_event_type_model.freezed.dart';
part 'con_event_type_model.g.dart';

@freezed
sealed class ConEventTypeModel with _$ConEventTypeModel {
  const factory ConEventTypeModel({String? name, TypeIcon? icon, bool? enabled}) = _ConEventTypeModel;

  factory ConEventTypeModel.fromJson(Map<String, dynamic> json) => _$ConEventTypeModelFromJson(json);
}
