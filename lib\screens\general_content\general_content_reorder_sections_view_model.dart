import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/general_content_model.dart';
import 'package:venvi/repositories/general_content_repository.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class GeneralContentReorderSectionsViewModel extends EditorScaffoldViewModel<List<GeneralContentModel>> {
  final ConData _conData;
  final GeneralContentRepository _repository;

  GeneralContentReorderSectionsViewModel(super.initialState, this._conData, this._repository);

  @override
  bool checkChanges(List<GeneralContentModel> initialState, List<GeneralContentModel> currentState) {
    return initialState != currentState;
  }

  @override
  Future<List<GeneralContentModel>> applyChanges(
      List<GeneralContentModel> initialState, List<GeneralContentModel> state) async {
    final orderedIds = state.map((e) => e.id).whereType<String>().toList();
    final updateSuccess = await _repository.updateOrderIndexes(_conData, orderedIds);
    if (updateSuccess) {
      return state;
    } else {
      throw const EditorScaffoldException(['Failed to update order']);
    }
  }

  void moveTile(int oldIndex, int newIndex) {
    if (newIndex < 0 || newIndex >= state.length) {
      return;
    }
    final storedItem = state[oldIndex];
    final newList = state.toList();
    newList.removeAt(oldIndex);
    newList.insert(newIndex, storedItem);
    emit(newList);
  }
}
