import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/device_model.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

@freezed
sealed class UserModel with _$UserModel {
  const factory UserModel({
    String? id,
    String? email,
    String? lastCon,
    List<String>? authorizedOrgs,
    List<String>? favoriteOrgs,
    List<String>? favoriteCons,
    List<String>? favoriteEvents,
    List<String>? readAnnouncements,
    bool? announcementNotifications,
    bool? spotlightEventNotifications,
    bool? favoriteEventNotifications,
    bool? showAdultContent,
    List<String>? pushTokens,
    Map<String, DeviceModel>? devices,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);
}

extension UserModelExtension on UserModel {
  bool isFavoriteEvent(ConData conData, String? eventId) {
    if (eventId == null) {
      return false;
    }
    return favoriteEvents?.contains('${conData.orgId}_${conData.conId}_$eventId') ?? false;
  }

  List<String> getFavoriteEvents(ConData conData) {
    final composites =
        favoriteEvents?.where((element) => element.startsWith('${conData.orgId}_${conData.conId}_')).toList();
    return composites?.map((e) => e.split('_').last).toList() ?? [];
  }

  List<String> getReadAnnouncements(ConData conData) {
    final composites =
        readAnnouncements?.where((element) => element.startsWith('${conData.orgId}_${conData.conId}_')).toList() ?? [];
    return composites.map((e) => e.split('_').last).toList();
  }
}
