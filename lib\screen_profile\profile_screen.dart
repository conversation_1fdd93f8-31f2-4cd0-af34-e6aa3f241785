import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:venvi/constants/rules.dart';
import 'package:venvi/constants/social_media.dart';
import 'package:venvi/global_view_models/auth_view_model.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/global_view_models/event_view_model.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/global_view_models/role_view_model.dart';
import 'package:venvi/models/base_profile.dart';
import 'package:venvi/models/event_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/profile_model.dart';
import 'package:venvi/models/role_model.dart';
import 'package:venvi/repositories/profile_repository.dart';
import 'package:venvi/router.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_manual_participant_screen.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_profile_screen.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/url_handler.dart';
import 'package:venvi/widgets/accent_label.dart';
import 'package:venvi/widgets/agenda_group.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/profile_photo_view.dart';
import 'package:venvi/widgets/restricted_view.dart';

class ProfileScreen extends StatelessWidget {
  final String? profileId;
  final bool isFullScreenDialog;

  const ProfileScreen({super.key, required this.profileId, required this.isFullScreenDialog});

  @override
  Widget build(BuildContext context) {
    if (profileId == null) {
      return Scaffold(
        appBar: GlassAppBar(isFullScreenDialog: isFullScreenDialog),
        body: ContentArea(fillWidth: false, child: Text('No profile ID provided')),
      );
    }

    late final ParticipantModel? participantModel;
    try {
      participantModel = context.read<ParticipantViewModel>().getParticipant(profileId!);
    } catch (e) {
      participantModel = null;
    }

    return FutureBuilder(
      future: participantModel != null
          ? Future.value(participantModel)
          : context.read<ProfileRepository>().getProfile(profileId!),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Scaffold(
            appBar: GlassAppBar(isFullScreenDialog: isFullScreenDialog),
            body: Center(child: ContentArea(child: SelectableText(snapshot.error.toString()))),
          );
        }
        if (!snapshot.hasData) {
          return Scaffold(
            appBar: GlassAppBar(isFullScreenDialog: isFullScreenDialog),
            body: Center(child: CircularProgressIndicator()),
          );
        }
        final profileBase = snapshot.data;
        if (profileBase == null) {
          return Scaffold(
            appBar: GlassAppBar(isFullScreenDialog: isFullScreenDialog),
            body: Center(child: ContentArea(child: Text('Profile not found'))),
          );
        }

        // Profile must either be a participant or profile model, it's safe to assume one is not null at this point
        final participantModel = profileBase is ParticipantModel ? profileBase : null;
        final profileModel = participantModel != null ? null : profileBase as ProfileModel;

        late final String? headline;
        if (participantModel != null) {
          if (participantModel.conHeadline != null) {
            headline = participantModel.conHeadline;
          } else {
            headline = participantModel.headline;
          }
        } else {
          headline = profileModel!.headline;
        }

        late final String? bio;
        if (participantModel != null) {
          if (participantModel.conBio != null) {
            bio = participantModel.conBio;
          } else {
            bio = participantModel.bio;
          }
        } else {
          bio = profileModel!.bio;
        }

        late final Map<SocialMedia, String>? socialMediaLinks;
        if (participantModel != null) {
          socialMediaLinks = participantModel.socialMediaLinks;
        } else {
          socialMediaLinks = profileModel!.socialMediaLinks;
        }

        return Scaffold(
          appBar: GlassAppBar(
            actions: _buildAppBarActions(context, participantModel, profileId),
            isFullScreenDialog: isFullScreenDialog,
          ),
          body: ListView(
            padding: const EdgeInsets.all(AppTheme.screenPadding),
            children: [
              ProfilePhotoView(profile: profileBase, size: 200),
              const SizedBox(height: AppTheme.widgetPaddingLarge),
              ContentArea(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SelectableText(profileBase.displayName ?? '', style: Theme.of(context).textTheme.titleLarge),
                    const SizedBox(height: AppTheme.widgetPaddingVerySmall),
                    SelectableText(
                      profileBase.username ?? '',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onPrimaryContainer.withValues(alpha: AppTheme.subtleWidgetOpacity),
                      ),
                    ),
                    if (headline != null)
                      Padding(
                        padding: const EdgeInsets.only(top: AppTheme.widgetPaddingSmall),
                        child: SelectableText(headline, style: Theme.of(context).textTheme.bodyMedium),
                      ),
                    if (participantModel != null && participantModel.allRoles != null)
                      BlocBuilder<RoleViewModel, List<RoleModel>?>(
                        builder: (context, state) => Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            for (final role in state ?? List<RoleModel>.empty())
                              participantModel.allRoles!.contains(role.id)
                                  ? Padding(
                                      padding: const EdgeInsets.only(top: AppTheme.widgetPaddingSmall),
                                      child: AccentLabel(
                                        text: role.name ?? '',
                                        icon: participantModel.spotlightRoles?.contains(role.id) == true
                                            ? Icons.star
                                            : null,
                                        isOnContainer: true,
                                      ),
                                    )
                                  : const SizedBox.shrink(),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
              if (socialMediaLinks != null && socialMediaLinks.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                  child: ContentArea(
                    child: Wrap(
                      alignment: WrapAlignment.center,
                      spacing: AppTheme.widgetPadding,
                      children: [
                        for (final socialMedia in socialMediaLinks.keys)
                          IconButton(
                            tooltip: socialMedia.text,
                            icon: Icon(socialMedia.icon),
                            iconSize: 32,
                            onPressed: () => UrlHandler.open('${socialMedia.url}${socialMediaLinks![socialMedia]}'),
                          ),
                      ],
                    ),
                  ),
                ),
              if (bio != null)
                Padding(
                  padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                  child: ContentArea(child: SelectableText(bio)),
                ),
              if (participantModel != null) _buildParticipantEventsList(context, profileBase),
            ],
          ),
        );
      },
    );
  }

  List<Widget>? _buildAppBarActions(BuildContext context, ParticipantModel? participantModel, String? profileId) {
    return [
      if (participantModel?.manualEntry == true && participantModel?.id != null)
        RestrictedView(
          rule: Rules.editParticipantsAndRoles,
          child: IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => AppRouter.pushFullScreenDialog<ParticipantModel?>(
              context,
              EditManualParticipantScreen(isNewParticipant: false, participantId: participantModel!.id),
            ),
          ),
        )
      else if (context.watch<AuthViewModel>().state?.uid == profileId)
        IconButton(
          icon: const Icon(Icons.edit),
          onPressed: () => AppRouter.pushFullScreenDialog(context, const EditProfileScreen()),
        ),
    ];
  }

  Widget _buildParticipantEventsList(BuildContext context, BaseProfile baseProfile) {
    return BlocBuilder<EventViewModel, List<EventModel>?>(
      builder: (context, state) {
        final participantEvents = state
            ?.where((event) => event.adminId == profileId || event.participantIds?.contains(profileId) == true)
            .toList();
        if (participantEvents == null || participantEvents.isEmpty) {
          return const SizedBox.shrink();
        }

        final groupedEvents = context.read<EventViewModel>().groupEventsByDateAndTime(
          context.watch<ConViewModel>().state,
          participantEvents,
        );

        List<Widget> agendaGroups = [];
        for (final day in groupedEvents.keys) {
          final List<EventModel> dayEvents = [];
          groupedEvents[day]!.forEach((time, events) {
            dayEvents.addAll(events);
          });

          agendaGroups.add(AgendaGroup(title: _formatDay(day.toDateTime()), events: dayEvents));
        }
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: AppTheme.widgetPaddingLarge),
            AccentLabel(
              text: 'Events with ${baseProfile.displayName != null ? baseProfile.displayName! : 'Participant'}',
              isOnContainer: false,
            ),
            for (final agendaGroup in agendaGroups)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                child: agendaGroup,
              ),
          ],
        );
      },
    );
  }

  String _formatDay(DateTime day) {
    return DateFormat.MMMEd().format(day);
  }
}
