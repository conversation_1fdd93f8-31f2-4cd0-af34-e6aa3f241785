import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/rules.dart';
import 'package:venvi/global_view_models/org_view_model.dart';
import 'package:venvi/global_view_models/permission_view_model.dart';
import 'package:venvi/models/con_model.dart';
import 'package:venvi/repositories/con_repository.dart';
import 'package:venvi/screens/org_con_list/org_con_list_state.dart';

class OrgConListViewModel extends Cubit<OrgConListState> {
  final OrgViewModel _orgViewModel;
  final PermissionViewModel _permissionViewModel;
  final ConRepository _conRepository;

  OrgConListViewModel(this._orgViewModel, this._permissionViewModel, this._conRepository)
    : super(OrgConListState(loadingCons: true, conModels: [], publishedConIds: [])) {
    final orgId = _orgViewModel.state?.id;
    if (orgId == null) {
      emit(OrgConListState(loadingCons: false, conModels: []));
      return;
    }

    final showUnpublishedCons = _permissionViewModel.isPermitted(Rules.viewUnpublishedCons);
    _conRepository.getAllCons(orgId, showUnpublishedCons).then((cons) {
      final conModels = cons ?? [];
      _sortConList(conModels);

      final publishedConIds = <String>[];

      for (final con in conModels) {
        final conId = con.id;
        if (conId == null) {
          continue;
        }
        if (showUnpublishedCons && con.isPublished == true) {
          publishedConIds.add(conId);
        }
      }

      emit(
        OrgConListState(
          loadingCons: false,
          conModels: conModels,
          publishedConIds: showUnpublishedCons ? publishedConIds : null,
          activeConId: _orgViewModel.state?.activeConId,
        ),
      );
    });
  }

  // Sorts by start date desc, end date desc, title asc
  void _sortConList(List<ConModel> conList) {
    conList.sort((a, b) {
      if (a.startDate != null && b.startDate != null) {
        final startTimeComparison = b.startDate!.compareTo(a.startDate!);
        if (startTimeComparison == 0) {
          if (a.endDate != null && b.endDate != null) {
            final endTimeComparison = b.endDate!.compareTo(a.endDate!);
            if (endTimeComparison == 0) {
              if (a.name != null && b.name != null) {
                return a.name!.compareTo(b.name!);
              } else if (a.name != null) {
                return -1;
              } else {
                return 1;
              }
            } else {
              return endTimeComparison;
            }
          } else if (a.endDate != null) {
            return -1;
          } else {
            return 1;
          }
        } else {
          return startTimeComparison;
        }
      } else if (a.startDate != null) {
        return -1;
      } else {
        return 1;
      }
    });
  }
}
