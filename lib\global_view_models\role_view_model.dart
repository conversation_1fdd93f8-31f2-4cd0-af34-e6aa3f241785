import 'dart:async';

import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/metadata_field.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/metadata_listener_view_model.dart';
import 'package:venvi/global_view_models/metadata_view_model.dart';
import 'package:venvi/models/role_model.dart';
import 'package:venvi/repositories/role_repository.dart';

class RoleViewModel extends MetadataListenerViewModel<RoleModel, List<RoleModel>?> {
  final ConData _conData;
  final RoleRepository _repository;

  RoleViewModel(this._conData, this._repository, MetadataViewModel metadataViewModel)
      : super(null, _conData, metadataViewModel, MetadataField.roles);

  @override
  Future<RoleModel?> getData(String id, DataLocation dataLocation) async {
    return await _repository.getRole(_conData, id, dataLocation: dataLocation);
  }

  @override
  void onData(Map<String, RoleModel> data) {
    if (isClosed) {
      return;
    }

    final sortedData = data.values.toList();
    emit(sortRoleFromModels(sortedData));
  }

  List<String> sortRolesFromIds(List<String> roleIds) {
    final List<RoleModel> roleModels = [];
    final List<String> missingRoleIds = [];

    for (final roleId in roleIds) {
      final role = getRoleById(roleId);
      if (role != null) {
        roleModels.add(role);
      } else {
        missingRoleIds.add(roleId);
      }
    }
    final sortedRoles = sortRoleFromModels(roleModels).map((e) => e.id!).toList();

    // Adds missing roles to the end of the list
    return sortedRoles + missingRoleIds;
  }

  List<RoleModel> sortRoleFromModels(List<RoleModel> roles) {
    final sortedData = roles.toList();
    sortedData.sort((a, b) => a.orderIndex != null
        ? b.orderIndex != null
            ? a.orderIndex!.compareTo(b.orderIndex!)
            : -1
        : 1);
    return sortedData;
  }

  RoleModel? getRoleById(String? id) {
    if (id == null) {
      return null;
    }
    try {
      return state?.firstWhere((element) => element.id == id);
    } catch (e) {
      return null;
    }
  }
}
