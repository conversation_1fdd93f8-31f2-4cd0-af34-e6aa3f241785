import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/event_type.dart';
import 'package:venvi/json_converters/event_type_map_converter.dart';
import 'package:venvi/models/con_event_type_model.dart';

part 'event_properties_model.freezed.dart';
part 'event_properties_model.g.dart';

@freezed
sealed class EventPropertiesModel with _$EventPropertiesModel {
  const factory EventPropertiesModel({
    String? id,
    @EventTypeMapConverter() Map<EventType, ConEventTypeModel>? eventTypes,
    List<String?>? eventTags,
  }) = _EventPropertiesModel;

  factory EventPropertiesModel.fromJson(Map<String, dynamic> json) => _$EventPropertiesModelFromJson(json);
}
