import 'package:flutter/material.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/surface_input_field.dart';

class GlassDropDown<T> extends StatelessWidget {
  final String hint;
  final T? value;
  final Function(T?) onChanged;
  final List<T> items;
  final String Function(T) getItemLabel;

  const GlassDropDown({
    super.key,
    required this.hint,
    required this.value,
    required this.onChanged,
    required this.items,
    required this.getItemLabel,
  });

  @override
  Widget build(BuildContext context) {
    return SurfaceInputField(
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppTheme.borderRadius),
          color: Theme.of(context).colorScheme.tertiary,
        ),
        child: DropdownButton<T>(
          value: value,
          onChanged: onChanged,
          hint: Text(
            hint,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onTertiary.withValues(alpha: AppTheme.hintOpacity),
            ),
          ),
          isExpanded: true,
          borderRadius: BorderRadius.circular(AppTheme.borderRadius),
          padding: const EdgeInsets.only(left: AppTheme.widgetPadding, right: AppTheme.widgetPaddingVerySmall),
          underline: const SizedBox.shrink(),
          iconEnabledColor: Theme.of(context).colorScheme.onTertiary,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.onTertiary),
          dropdownColor: Theme.of(context).colorScheme.tertiary,
          items:
              items
                  .map(
                    (item) => DropdownMenuItem(
                      value: item,
                      child: Text(
                        getItemLabel(item),
                        style: Theme.of(
                          context,
                        ).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.onTertiary),
                      ),
                    ),
                  )
                  .whereType<DropdownMenuItem<T>>()
                  .toList(),
        ),
      ),
    );
  }
}
