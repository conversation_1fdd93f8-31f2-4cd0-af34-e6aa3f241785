import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/event_type.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/event_view_model.dart';
import 'package:venvi/global_view_models/user_view_model.dart';
import 'package:venvi/models/event_model.dart';
import 'package:venvi/models/user_model.dart';

enum HighlightedEventsType { current, upcoming }

class HighlightedEventsViewModel extends Cubit<List<EventModel>?> {
  static const int _minEvents = 3;

  final HighlightedEventsType highlightedEventsType;

  late final StreamSubscription? _eventsSubscription;
  late final StreamSubscription? _userSubscription;

  List<EventModel>? _allEvents;
  List<String> _favorites = [];
  bool? _showAdultContent;

  HighlightedEventsViewModel(
    ConData conData,
    EventViewModel eventViewModel,
    UserViewModel userViewModel,
    this.highlightedEventsType,
  ) : super(null) {
    _eventsSubscription = eventViewModel.stream.listen((events) {
      _allEvents = events;
      refresh();
    });
    _userSubscription = userViewModel.stream.listen((user) {
      final newFavorites = user.getFavoriteEvents(conData);
      // Checks if anything relevant has changed
      if (_showAdultContent != user.showAdultContent ||
          _favorites.length != newFavorites.length ||
          _favorites.any((element) => !newFavorites.contains(element))) {
        _favorites = newFavorites;
        _showAdultContent = user.showAdultContent;
        refresh();
      }
    });
    _allEvents = eventViewModel.state;
    _favorites = userViewModel.state.getFavoriteEvents(conData);
    _showAdultContent = userViewModel.state.showAdultContent;
    refresh();
  }

  @override
  Future<void> close() {
    _eventsSubscription?.cancel();
    _userSubscription?.cancel();
    return super.close();
  }

  void refresh() {
    final events = _allEvents;
    if (events == null) {
      emit(null);
      return;
    }

    final List<EventModel> highlightedEvents = [];
    final List<EventModel> fillerEvents = [];
    for (final event in events) {
      if (event.adultOnly != true || _showAdultContent != false) {
        if (highlightedEventsType == HighlightedEventsType.current && _isCurrent(event)) {
          if (_isSpotlight(event)) {
            highlightedEvents.add(event);
          } else {
            fillerEvents.add(event);
          }
        } else if (highlightedEventsType == HighlightedEventsType.upcoming && _isUpcoming(event)) {
          if (_isSpotlight(event)) {
            highlightedEvents.add(event);
          } else {
            fillerEvents.add(event);
          }
        }
      }
    }

    for (int i = highlightedEvents.length; i < _minEvents; i++) {
      if (fillerEvents.isNotEmpty) {
        highlightedEvents.add(fillerEvents.removeAt(0));
      }
    }

    emit(highlightedEvents);
  }

  bool _isSpotlight(EventModel event) {
    return event.spotlight == true || event.type == EventType.type1 || _favorites.contains(event.id);
  }

  bool _isCurrent(EventModel event) {
    final startTime = event.startTime?.millisecondsSinceEpoch;
    final endTime = event.endTime?.millisecondsSinceEpoch;

    if (startTime == null || endTime == null) {
      return false;
    }

    final now = Timestamp.now().millisecondsSinceEpoch;
    final rangeStart = now - const Duration(minutes: 120).inMilliseconds;
    final rangeEnd = now + const Duration(minutes: 30).inMilliseconds;

    return rangeStart <= startTime && startTime <= now && rangeEnd <= endTime;
  }

  bool _isUpcoming(EventModel event) {
    final startTime = event.startTime?.millisecondsSinceEpoch;
    if (startTime == null) {
      return false;
    }

    final now = Timestamp.now().millisecondsSinceEpoch;
    final rangeEnd = now + const Duration(minutes: 60).inMilliseconds;

    return now <= startTime && startTime <= rangeEnd;
  }
}
