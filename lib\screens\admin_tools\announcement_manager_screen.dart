import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/announcement_view_model.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/models/announcement_model.dart';
import 'package:venvi/repositories/announcement_repository.dart';
import 'package:venvi/screens/admin_tools/make_announcement_bottom_sheet.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/bottom_sheets.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/snack_bars.dart';
import 'package:venvi/widgets/announcement_item.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/glass_tile_button.dart';

class AnnouncementManagerScreen extends StatelessWidget {
  const AnnouncementManagerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const GlassAppBar(title: Text('Announcement Manager')),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppTheme.screenPadding),
            child: ContentArea(
              padding: EdgeInsets.zero,
              child: GlassTileButton(
                text: 'Make Announcement',
                icon: Icons.campaign,
                isAccent: true,
                onTap: () {
                  if (context.read<ConViewModel>().isPremium()) {
                    BottomSheets.showBottomSheet(context: context, child: const MakeAnnouncementBottomSheet());
                  } else {
                    Dialogs.showPremiumFeatureDialog(context, PremiumUpgradeDialogType.feature);
                  }
                },
              ),
            ),
          ),
          Expanded(
            child: BlocBuilder<AnnouncementViewModel, List<AnnouncementModel>?>(
              builder: (context, state) {
                if (state?.isNotEmpty != true) {
                  const ContentArea(fillWidth: false, child: Text('No announcements'));
                }
                final reversedList = state!.reversed.toList();
                return ListView.separated(
                  padding: const EdgeInsets.all(AppTheme.screenPadding),
                  itemCount: reversedList.length,
                  separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                  itemBuilder:
                      (context, index) => ContentArea(
                        padding: EdgeInsets.zero,
                        child: AnnouncementItem(
                          announcement: reversedList[index],
                          leadingButton: IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () async {
                              final confirmation = await Dialogs.showConfirmationDialog(
                                context,
                                title: 'Delete Announcement?',
                                message: 'This will remove it from all users',
                              );
                              if (confirmation != true || !context.mounted) {
                                return;
                              }
                              Dialogs.showLoadingDialog(context);
                              final success = await context.read<AnnouncementRepository>().deleteAnnouncement(
                                context.read<ConData>(),
                                reversedList[index].id!,
                              );
                              if (!context.mounted) {
                                return;
                              }
                              Navigator.of(context, rootNavigator: true).pop();
                              if (success) {
                                SnackBars.showInfoSnackBar(context, 'Announcement deleted');
                              } else {
                                SnackBars.showInfoSnackBar(context, 'Failed to delete announcement');
                              }
                            },
                          ),
                          showCreator: true,
                        ),
                      ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
