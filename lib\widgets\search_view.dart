import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/surface_input_field.dart';

class SearchView extends HookWidget {
  final TextEditingController? controller;
  final bool autofocus;
  final FocusNode? focusNode;
  final Function(String searchText) onSearchChange;
  final Widget child;

  const SearchView({
    super.key,
    this.controller,
    this.autofocus = false,
    this.focusNode,
    required this.onSearchChange,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final controller = this.controller ?? useTextEditingController();

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.screenPadding,
            vertical: AppTheme.widgetPaddingSmall,
          ),
          child: ContentArea(
            padding: EdgeInsets.zero,
            child: SurfaceInputField(
              child: TextField(
                controller: controller,
                autofocus: autofocus,
                focusNode: focusNode,
                textInputAction: TextInputAction.search,
                decoration: InputDecoration(
                  hintText: 'Search',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      if (controller.text.isEmpty) {
                        FocusManager.instance.primaryFocus?.unfocus();
                      } else {
                        controller.clear();
                        onSearchChange('');
                      }
                    },
                  ),
                ),
                onChanged: (value) => onSearchChange(value),
              ),
            ),
          ),
        ),
        Expanded(
          child: child,
        ),
      ],
    );
  }
}
