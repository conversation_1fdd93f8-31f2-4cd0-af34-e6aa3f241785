import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/metadata_doc_status.dart';
import 'package:venvi/constants/metadata_field.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/metadata_model.dart';
import 'package:venvi/repositories/metadata_repository.dart';
import 'package:venvi/repositories/schedule_download_tracker_repository.dart';
import 'package:venvi/utils/logger.dart';

class MetadataViewModel extends Cubit<Map<MetadataField, Map<String, MetadataDocStatus>>?> {
  static const Duration _updateDebounceDuration = Duration(milliseconds: 1000);

  final ConData _conData;
  final MetadataRepository _metadataRepository;
  final ScheduleDownloadTrackerRepository _scheduleDownloadTrackerRepository;

  late final StreamSubscription _metadataSubscription;

  MetadataModel? _localMetadataModel; // Current local metadata
  MetadataModel? _latestServerMetadataModel; // Latest metadata from listening to the stream

  Timestamp _lastTimeEmitted = Timestamp.fromMillisecondsSinceEpoch(0);
  MetadataModel? _queuedStateUpdate;

  bool needsToRegisterScheduleDownload = false;

  final _loadingCompleter = Completer<void>();

  Future<void> waitForLoading() async {
    return await _loadingCompleter.future;
  }

  MetadataViewModel(this._conData, this._metadataRepository, this._scheduleDownloadTrackerRepository) : super(null) {
    _init();
  }

  Future<void> _init() async {
    // Gets currently saved metadata or creates a new one
    final savedMetadataModel = await _metadataRepository.getLocalMetadata(_conData);
    if (savedMetadataModel == null) {
      _localMetadataModel = MetadataModel();
      needsToRegisterScheduleDownload = true;
    } else {
      _localMetadataModel = savedMetadataModel;
    }

    // Listens for metadata document updates
    _metadataSubscription = _metadataRepository
        .getMetadataStream(_conData)
        .listen(
          (data) {
            if (data != null) {
              _queueUpdate(data);
            }
            _loadingCompleter.complete();
          },
          onError: (e) {
            Logger.error(exception: e, message: 'MetadataViewModel error');
            _loadingCompleter.complete();
          },
        );
  }

  @override
  Future<void> close() async {
    await _metadataSubscription.cancel();
    _queuedStateUpdate = null;
    return super.close();
  }

  Future<void> saveUpdateTimestamp(ConData conData, MetadataField metadataField, List<String> updatedIds) async {
    if (updatedIds.isEmpty) {
      return;
    }

    if (needsToRegisterScheduleDownload) {
      needsToRegisterScheduleDownload = false;
      _scheduleDownloadTrackerRepository.registerScheduleDownload(_conData);
    }

    // Updates the local metadata with the latest server timestamp for the successfully updated documents
    final MetadataModel newLocalMetadata = _localMetadataModel != null ? Map.from(_localMetadataModel!) : {};
    final Map<String, Timestamp> idTimestampMap = newLocalMetadata[metadataField] ?? {};
    for (final id in updatedIds) {
      final serverTimestamp = _latestServerMetadataModel?[metadataField]?[id];
      if (serverTimestamp != null) {
        idTimestampMap[id] = serverTimestamp;
      }
    }
    newLocalMetadata[metadataField] = idTimestampMap;

    // Saves the updated local metadata
    _localMetadataModel = newLocalMetadata;
    await _metadataRepository.saveLocalMetadata(conData, newLocalMetadata);
  }

  Future<void> deleteLocalMetadataId(ConData conData, MetadataField metadataField, List<String> deletedIds) async {
    if (deletedIds.isEmpty) {
      return;
    }

    final MetadataModel newLocalMetadata = _localMetadataModel != null ? Map.from(_localMetadataModel!) : {};
    final Map<String, Timestamp> idTimestampMap = newLocalMetadata[metadataField] ?? {};
    for (final id in deletedIds) {
      idTimestampMap.remove(id);
    }
    newLocalMetadata[metadataField] = idTimestampMap;

    // Saves the updated local metadata
    _localMetadataModel = newLocalMetadata;
    await _metadataRepository.saveLocalMetadata(conData, newLocalMetadata);
  }

  void _queueUpdate(MetadataModel metadataModel) {
    final now = Timestamp.now();
    if (now.millisecondsSinceEpoch - _lastTimeEmitted.millisecondsSinceEpoch > _updateDebounceDuration.inMilliseconds) {
      _queuedStateUpdate = null;
      _lastTimeEmitted = now;
      _checkMetadataForUpdates(metadataModel);
    } else {
      if (_queuedStateUpdate == null) {
        Future.delayed(
          Duration(
            milliseconds:
                _updateDebounceDuration.inMilliseconds -
                (now.millisecondsSinceEpoch - _lastTimeEmitted.millisecondsSinceEpoch),
          ),
        ).then((value) {
          final queuedUpdate = _queuedStateUpdate;
          if (queuedUpdate != null) {
            _queuedStateUpdate = null;
            _lastTimeEmitted = Timestamp.now();
            _checkMetadataForUpdates(queuedUpdate);
          }
        });
      }
      _queuedStateUpdate = metadataModel;
    }
  }

  void _checkMetadataForUpdates(MetadataModel metadataModel) {
    // Updates the latest server metadata
    _latestServerMetadataModel = metadataModel;

    // Compares the latest server metadata with the local metadata to figure out which documents need to be updated
    final Map<MetadataField, Map<String, MetadataDocStatus>> newState = {};
    for (final metadataField in MetadataField.values) {
      final Map<String, MetadataDocStatus> docStatusMap = {};
      metadataModel[metadataField]?.forEach((id, serverTimestamp) {
        final localTimestamp = _localMetadataModel?[metadataField]?[id];
        final isServerDataNewer = _isServerDataNewer(localTimestamp, serverTimestamp);
        docStatusMap[id] = isServerDataNewer ? MetadataDocStatus.updateNeeded : MetadataDocStatus.upToDate;
      });
      _localMetadataModel?[metadataField]?.forEach((id, localTimestamp) {
        if (metadataModel[metadataField]?[id] == null) {
          docStatusMap[id] = MetadataDocStatus.deleted;
        }
      });
      newState[metadataField] = docStatusMap;
    }

    if (!isClosed) {
      emit(newState);
    }
  }

  bool _isServerDataNewer(Timestamp? local, Timestamp? server) {
    return server != null && (local == null || server.millisecondsSinceEpoch > local.millisecondsSinceEpoch);
  }
}
