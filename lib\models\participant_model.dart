import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/social_media.dart';
import 'package:venvi/models/base_profile.dart';
import 'package:venvi/models/profile_model.dart';

part 'participant_model.freezed.dart';
part 'participant_model.g.dart';

@freezed
sealed class ParticipantModel with _$ParticipantModel implements BaseProfile {
  const factory ParticipantModel({
    String? id,
    String? username,
    String? displayName,
    String? searchUsername,
    String? searchDisplayName,
    String? photoUrl,
    String? headline,
    String? bio,
    Map<SocialMedia, String>? socialMediaLinks,
    String? conHeadline,
    String? conBio,
    bool? manualEntry,
    List<String>? allRoles,
    List<String>? spotlightRoles,
  }) = _ParticipantModel;

  factory ParticipantModel.fromProfileModel(ProfileModel profileModel, String? conHeadline, String? conBio) =>
      ParticipantModel.fromJson(profileModel.toJson());

  factory ParticipantModel.fromJson(Map<String, dynamic> json) => _$ParticipantModelFromJson(json);
}
