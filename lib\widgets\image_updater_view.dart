import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_network_image_platform_interface/cached_network_image_platform_interface.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/shimmer_square.dart';

class ImageUpdaterView extends StatelessWidget {
  final IconData icon;
  final double? aspectRatio;
  final String? imageUrl;
  final XFile? imageFile;
  final Function() onTap;

  const ImageUpdaterView({
    super.key,
    this.icon = Icons.edit,
    this.aspectRatio = 1,
    this.imageUrl,
    this.imageFile,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ContentArea(
      padding: EdgeInsets.zero,
      onTap: onTap,
      child: imageFile != null
          ? kIsWeb
              ? _createImageView(context, Image.network(imageFile!.path).image)
              : _createImageView(context, Image.file(File(imageFile!.path)).image)
          : imageUrl != null
              ? CachedNetworkImage(
                  imageRenderMethodForWeb: ImageRenderMethodForWeb.HttpGet,
                  imageUrl: imageUrl!,
                  imageBuilder: (context, imageProvider) => _createImageView(context, imageProvider),
                  placeholder: (context, url) => const ShimmerSquare(),
                  errorWidget: (context, url, error) => Padding(
                    padding: const EdgeInsets.all(AppTheme.widgetPadding),
                    child: AspectRatio(
                      aspectRatio: aspectRatio ?? 1,
                      child: Icon(
                        Icons.error,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.all(AppTheme.widgetPadding),
                  child: Icon(
                    Icons.add_photo_alternate,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
    );
  }

  Widget _createImageView(BuildContext context, ImageProvider<Object> imageProvider) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        aspectRatio == null
            ? Center(
                child: Image(
                  image: imageProvider,
                  fit: BoxFit.contain,
                ),
              )
            : AspectRatio(
                aspectRatio: aspectRatio!,
                child: Image(
                  image: imageProvider,
                  fit: BoxFit.cover,
                ),
              ),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 1),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(AppTheme.borderRadius),
            ),
          ),
          padding: const EdgeInsets.all(AppTheme.widgetPaddingSmall),
          child: Icon(
            icon,
            color:
                AppTheme.calculateForegroundColor(Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 1)),
          ),
        ),
      ],
    );
  }
}
