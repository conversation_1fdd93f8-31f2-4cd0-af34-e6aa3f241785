import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/screens/import_export_schedule/export_category.dart';

part 'import_export_state.freezed.dart';

@freezed
sealed class ImportExportState with _$ImportExportState {
  const factory ImportExportState({
    ExportCategory? category,
    List<MapEntry<String?, String?>>? filterOptions,
    MapEntry<String?, String?>? selectedFilter,
  }) = _ImportExportState;
}
