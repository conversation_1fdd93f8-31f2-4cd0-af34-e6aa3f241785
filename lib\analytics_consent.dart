import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:venvi/data_sources/device_variables_local_data_source.dart';
import 'package:venvi/utils/logger.dart';

class AnalyticsConsent {
  late final DeviceVariablesLocalDataSource _deviceVariablesLocalDataSource;

  bool _analyticsEnabled = false;

  bool get analyticsEnabled => _analyticsEnabled;

  AnalyticsConsent(this._deviceVariablesLocalDataSource);

  Future<void> initAnalyticsEnabled() async {
    _analyticsEnabled = await _deviceVariablesLocalDataSource.getAnalyticsConsent();
  }

  Future<void> initFirebaseAnalytics() async {
    try {
      await FirebaseAnalytics.instance
          .setAnalyticsCollectionEnabled(kReleaseMode && _analyticsEnabled)
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () => Future.error('Failed to set analytics collection $_analyticsEnabled'),
          );
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace);
    }
  }

  Future<bool> enableAnalytics() async {
    final success = await _deviceVariablesLocalDataSource.setAnalyticsConsent(true);
    if (success) {
      _analyticsEnabled = true;
      await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(kReleaseMode);
      return true;
    }
    return false;
  }

  Future<bool> disableAnalytics() async {
    final success = await _deviceVariablesLocalDataSource.setAnalyticsConsent(false);
    if (success) {
      _analyticsEnabled = false;
      await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(false);
      return true;
    }
    return false;
  }
}
