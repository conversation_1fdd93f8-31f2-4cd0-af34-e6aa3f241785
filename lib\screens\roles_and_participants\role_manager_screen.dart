import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:sticky_headers/sticky_headers.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/global_view_models/role_view_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/repositories/role_repository.dart';
import 'package:venvi/router.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_manual_participant_screen.dart';
import 'package:venvi/screen_dialogs/profile_search/profile_search_result.dart';
import 'package:venvi/screen_dialogs/profile_search/profile_search_screen.dart';
import 'package:venvi/screens/roles_and_participants/role_manager_state.dart';
import 'package:venvi/screens/roles_and_participants/role_manager_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/snack_bars.dart';
import 'package:venvi/widgets/accent_label.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/content_group.dart';
import 'package:venvi/widgets/editor_scaffold.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/glass_tile_button.dart';
import 'package:venvi/widgets/profile_item.dart';
import 'package:venvi/widgets/surface_input_field.dart';

class RoleManagerScreen extends HookWidget {
  final String? roleId;

  const RoleManagerScreen({super.key, required this.roleId});

  @override
  Widget build(BuildContext context) {
    final nameController = useTextEditingController();

    final roleId = this.roleId;
    if (roleId == null) {
      return const Scaffold(
        appBar: GlassAppBar(),
        body: ContentArea(fillWidth: false, child: Text('Role not found')),
      );
    }

    return EditorScaffold<RoleManagerViewModel, RoleManagerState, ParticipantViewModel, List<ParticipantModel>?>(
      checkForExternalConflicts: false,
      externalCubit: context.read<ParticipantViewModel>(),
      buildState: (externalCubitState) {
        final roleName = context.read<RoleViewModel>().getRoleById(roleId)?.name;
        nameController.text = roleName ?? '';

        final roleParticipants = context.read<ParticipantViewModel>().getParticipantsByRole(roleId);
        final spotlightParticipants = roleParticipants
            .where((element) => element.spotlightRoles?.contains(roleId) ?? false)
            .toList();
        return RoleManagerState(
          roleName: roleName,
          spotlightParticipants: spotlightParticipants,
          regularParticipants: roleParticipants.where((element) => !spotlightParticipants.contains(element)).toList(),
        );
      },
      initViewModel: (state) => RoleManagerViewModel(
        state,
        roleId,
        context.read<ConData>(),
        context.read<RoleRepository>(),
        context.read<ParticipantViewModel>(),
        context.read<ParticipantRepository>(),
      ),
      title: const Text('Role Editor'),
      successMessage: 'Role saved',
      buildContent: (context, viewModel, state) => Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppTheme.screenPadding),
            child: ContentGroup(
              title: 'Role Name',
              child: SurfaceInputField(
                child: TextField(
                  controller: nameController,
                  onChanged: (value) => viewModel.updateRoleName(value),
                  textInputAction: TextInputAction.done,
                  textCapitalization: TextCapitalization.words,
                  textAlign: TextAlign.center,
                  maxLength: InputConstants.maxRoleNameLength,
                  decoration: const InputDecoration(hintText: 'Role Name', counterText: ''),
                ),
              ),
            ),
          ),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(AppTheme.screenPadding),
              children: [
                StickyHeader(
                  header: const Padding(
                    padding: EdgeInsets.all(AppTheme.widgetPaddingSmall),
                    child: AccentLabel(icon: Icons.star, text: 'Spotlight', isOnContainer: false),
                  ),
                  content: ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: state.spotlightParticipants.length,
                    separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                    itemBuilder: (context, index) => buildParticipantItem(
                      context,
                      viewModel,
                      state.spotlightParticipants[index],
                      state.roleName,
                      true,
                    ),
                  ),
                ),
                const SizedBox(height: AppTheme.widgetPadding),
                StickyHeader(
                  header: const Padding(
                    padding: EdgeInsets.all(AppTheme.widgetPaddingSmall),
                    child: AccentLabel(icon: Icons.person, text: 'Regular', isOnContainer: false),
                  ),
                  content: ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: state.regularParticipants.length,
                    separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                    itemBuilder: (context, index) => buildParticipantItem(
                      context,
                      viewModel,
                      state.regularParticipants[index],
                      state.roleName,
                      false,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(
              AppTheme.screenPadding,
              AppTheme.widgetPaddingSmall,
              AppTheme.screenPadding,
              0,
            ),
            child: Column(
              children: [
                ContentArea(
                  padding: EdgeInsets.zero,
                  child: GlassTileButton(
                    text: 'Add Participant from Venvi Profile',
                    icon: Icons.add,
                    onTap: () async {
                      final profileSearchResult = await AppRouter.pushFullScreenDialog<ProfileSearchResult?>(
                        context,
                        ProfileSearchScreen(
                          excludedIds: [
                            for (final participant in state.spotlightParticipants)
                              if (participant.id != null) participant.id!,
                            for (final participant in state.regularParticipants)
                              if (participant.id != null) participant.id!,
                          ],
                          bottomWidget: ContentArea(
                            padding: EdgeInsets.zero,
                            child: GlassTileButton(
                              text: 'Add New Participant Manually',
                              icon: Icons.add,
                              onTap: () async {
                                final manualParticipant = await AppRouter.pushFullScreenDialog<ParticipantModel?>(
                                  context,
                                  const EditManualParticipantScreen(isNewParticipant: true),
                                );
                                if (manualParticipant != null && context.mounted) {
                                  context.pop();
                                  viewModel.addParticipant(manualParticipant);
                                }
                              },
                            ),
                          ),
                        ),
                      );
                      if (profileSearchResult?.participantModel != null) {
                        viewModel.addParticipant(profileSearchResult!.participantModel!);
                      } else if (profileSearchResult?.profileModel != null) {
                        viewModel.addParticipantFromProfile(profileSearchResult!.profileModel!);
                      }
                    },
                  ),
                ),
                const SizedBox(height: AppTheme.widgetPadding),
                ContentArea(
                  padding: EdgeInsets.zero,
                  child: GlassTileButton(
                    text: 'Delete Role',
                    icon: Icons.delete,
                    onTap: () async {
                      final confirmDelete = await Dialogs.showConfirmationDialog(
                        context,
                        title: 'Delete Role?',
                        message: state.roleName,
                      );
                      if (confirmDelete == true && context.mounted) {
                        final result = await context.read<RoleManagerViewModel>().deleteRole(
                          context.read<ConData>(),
                          context.read<RoleRepository>(),
                        );
                        if (context.mounted) {
                          if (result) {
                            SnackBars.showInfoSnackBar(context, 'Role deleted');
                            context.pop();
                            context.pop();
                          } else {
                            SnackBars.showInfoSnackBar(context, 'Failed to delete role');
                          }
                        }
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildParticipantItem(
    BuildContext context,
    RoleManagerViewModel viewModel,
    ParticipantModel participant,
    String? roleName,
    bool isSpotlight,
  ) {
    return ContentArea(
      padding: EdgeInsets.zero,
      child: ProfileItem(
        profile: participant,
        subtitleOverride: participant.manualEntry == true ? 'Manual Entry' : participant.username ?? '',
        onTapOverride: () =>
            context.go('${context.read<ConData>().conPath}/admin/roles/role-manager/$roleId/profile/${participant.id}'),
        leadingButton: IconButton(
          icon: Icon(isSpotlight ? Icons.star : Icons.star_border),
          onPressed: () => viewModel.setParticipantSpotlight(participant.id!, !isSpotlight),
        ),
        trailingButton: IconButton(
          icon: const Icon(Icons.delete),
          onPressed: () async {
            final id = participant.id;
            final confirm = await Dialogs.showConfirmationDialog(
              context,
              title: 'Remove Participant?',
              message:
                  '${participant.displayName} (${participant.manualEntry == true ? 'Manual Entry' : participant.username ?? ''}) will be removed from ${roleName ?? 'this role'}',
            );
            if (confirm == true && id != null) {
              viewModel.removeParticipant(id);
            }
          },
        ),
      ),
    );
  }
}
