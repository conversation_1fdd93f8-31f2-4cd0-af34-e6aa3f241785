import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/image_library_type.dart';
import 'package:venvi/constants/image_type.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/home_screen_gallery_view_model.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/repositories/image_library_repository.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/image_selector.dart';
import 'package:venvi/utils/snack_bars.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/image_library.dart';

class HomeScreenGalleryScreen extends StatelessWidget {
  const HomeScreenGalleryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: GlassAppBar(
        title: const Text('Home Screen Photo Gallery'),
        actions: [
          IconButton(
            tooltip: 'Add Photo',
            icon: const Icon(Icons.add),
            onPressed: () async {
              final file = await ImageSelector().selectImage(context, cropAspectRatio: 1);

              if (file != null && context.mounted) {
                Dialogs.showLoadingDialog(context);

                final conData = context.read<ConData>();
                final imageModel =
                    context.read<ImageRepository>().createImageModel(conData, ImageType.homeScreenGallery);
                final uploadedModel = await context
                    .read<ImageLibraryRepository>()
                    .createImage(conData, ImageLibraryType.homeScreenGallery, imageModel, file);

                // Waits for image to upload
                if (uploadedModel != null && context.mounted) {
                  await context.read<HomeScreenGalleryViewModel>().stream.first;
                }

                if (context.mounted) {
                  Navigator.of(context, rootNavigator: true).pop();
                  if (uploadedModel != null) {
                    SnackBars.showInfoSnackBar(context, 'Photo added');
                  } else {
                    SnackBars.showInfoSnackBar(context, 'Failed to add photo');
                  }
                }
              }
            },
          ),
        ],
      ),
      body: BlocBuilder<HomeScreenGalleryViewModel, List<ImageModel>?>(
        builder: (context, state) {
          if (state == null || state.isEmpty) {
            return const ContentArea(
              fillWidth: false,
              child: Text(
                'No photos found in\nhome screen photo gallery',
                textAlign: TextAlign.center,
              ),
            );
          }

          return ImageLibrary(
            icon: Icons.delete,
            images: state,
            onSelect: (image) async => await _showDeletePhotoConfirmation(context, image),
          );
        },
      ),
    );
  }

  Future<void> _showDeletePhotoConfirmation(BuildContext context, ImageModel imageModel) async {
    final id = imageModel.id;
    if (id == null) {
      return;
    }

    final confirmation = await Dialogs.showConfirmationDialog(
      context,
      title: 'Delete Photo?',
    );

    if (confirmation == true && context.mounted) {
      Dialogs.showLoadingDialog(context);

      final conData = context.read<ConData>();
      final imageLibraryRepository = context.read<ImageLibraryRepository>();

      bool success = await imageLibraryRepository.deleteImage(
        conData,
        ImageLibraryType.homeScreenGallery,
        id,
      );

      // Waits for image to delete
      if (success && context.mounted) {
        await context.read<HomeScreenGalleryViewModel>().stream.first;
      }

      if (context.mounted) {
        Navigator.of(context, rootNavigator: true).pop();
        if (success) {
          SnackBars.showInfoSnackBar(context, 'Photo deleted');
        } else {
          SnackBars.showInfoSnackBar(context, 'Failed to delete photo');
        }
      }
    }
  }
}
