import 'package:flutter/material.dart';
import 'package:venvi/theme.dart';

class GlassTileButton extends StatelessWidget {
  final String text;
  final IconData? icon;
  final String? subtitle;
  final void Function()? onTap;
  final IconData? trailingIcon;
  final bool isCentered;
  final Color? foregroundColor;
  final bool isAccent;
  final bool isCompact;

  const GlassTileButton({
    super.key,
    this.icon,
    required this.text,
    this.subtitle,
    this.onTap,
    this.trailingIcon,
    this.isCentered = true,
    this.foregroundColor,
    this.isAccent = false,
    this.isCompact = false,
  }) : assert(foregroundColor == null || !isAccent);

  @override
  Widget build(BuildContext context) {
    final subtleAlpha = (AppTheme.subtleWidgetOpacity * 255).round().clamp(0, 255);
    final foregroundAlpha = onTap != null ? 255 : subtleAlpha;
    final calculatedForegroundColor = (isAccent
            ? Theme.of(context).colorScheme.onSecondary
            : (foregroundColor ?? Theme.of(context).colorScheme.onPrimaryContainer))
        .withAlpha(foregroundAlpha);

    return isAccent
        ? Material(
          color:
              onTap != null
                  ? Theme.of(context).colorScheme.secondary
                  : Theme.of(context).colorScheme.secondary.withValues(alpha: AppTheme.subtleColoredContainerOpacity),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppTheme.borderRadius)),
          child: _buildTile(context, calculatedForegroundColor, subtleAlpha),
        )
        : _buildTile(context, calculatedForegroundColor, subtleAlpha);
  }

  Widget _buildTile(BuildContext context, Color calculatedForegroundColor, int subtleAlpha) {
    return ListTile(
      title:
          icon != null
              ? Row(
                mainAxisAlignment: isCentered ? MainAxisAlignment.center : MainAxisAlignment.start,
                children: [
                  Icon(icon, color: calculatedForegroundColor),
                  const SizedBox(width: AppTheme.widgetPadding),
                  _buildTextArea(context, calculatedForegroundColor, subtleAlpha),
                  if (trailingIcon != null) const Spacer(),
                  if (trailingIcon != null) Icon(trailingIcon, color: calculatedForegroundColor),
                ],
              )
              : _buildTextArea(context, calculatedForegroundColor, subtleAlpha),
      dense: isCompact,
      onTap: onTap,
    );
  }

  Widget _buildTextArea(BuildContext context, Color calculatedForegroundColor, int subtleAlpha) {
    if (subtitle != null) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(text, style: Theme.of(context).textTheme.labelLarge?.copyWith(color: calculatedForegroundColor)),
          Text(
            subtitle!,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: calculatedForegroundColor.withAlpha(subtleAlpha)),
          ),
        ],
      );
    }
    return Text(text, style: Theme.of(context).textTheme.labelLarge?.copyWith(color: calculatedForegroundColor));
  }
}
