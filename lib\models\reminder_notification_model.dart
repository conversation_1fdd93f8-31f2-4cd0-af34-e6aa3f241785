import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'reminder_notification_model.freezed.dart';

@freezed
sealed class ReminderNotificationModel with _$ReminderNotificationModel {
  const factory ReminderNotificationModel({
    required String eventId,
    required Timestamp reminderTime,
    required String? timeZoneId,
    required String title,
    String? body,
  }) = _ReminderNotificationModel;
}
