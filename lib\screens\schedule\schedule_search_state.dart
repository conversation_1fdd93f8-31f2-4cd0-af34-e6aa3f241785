import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/event_type.dart';
import 'package:venvi/custom_classes/date_fields.dart';
import 'package:venvi/models/event_model.dart';

part 'schedule_search_state.freezed.dart';

@freezed
sealed class ScheduleSearchState with _$ScheduleSearchState {
  const factory ScheduleSearchState({
    Map<DateFields, Map<TimeOfDay, List<EventModel>>>? events,
    required String searchTerm,
    required bool spotlightOnly,
    EventType? eventTypeFilter,
    String? venueFilter,
    required List<String> tagFilter,
  }) = _ScheduleSearchState;
}
