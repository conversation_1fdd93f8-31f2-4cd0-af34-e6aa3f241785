import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/image_library_type.dart';
import 'package:venvi/constants/image_type.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/repositories/image_library_repository.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/image_selector.dart';
import 'package:venvi/utils/snack_bars.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/image_library.dart';

class ReusableEventImageLibraryScreen extends StatelessWidget {
  const ReusableEventImageLibraryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: GlassAppBar(
        title: const Text('Reusable Event Image Library'),
        actions: [
          IconButton(
            tooltip: 'Add Image',
            icon: const Icon(Icons.add),
            onPressed: () async {
              final file = await ImageSelector().selectImage(context, cropAspectRatio: 1);

              if (file != null && context.mounted) {
                Dialogs.showLoadingDialog(context);

                final conData = context.read<ConData>();
                final imageModel = context.read<ImageRepository>().createImageModel(
                  conData,
                  ImageType.reusableEventPrimary,
                );
                final uploadedModel = await context.read<ImageLibraryRepository>().createImage(
                  conData,
                  ImageLibraryType.reusableEventPrimary,
                  imageModel,
                  file,
                );

                if (context.mounted) {
                  Navigator.of(context, rootNavigator: true).pop();
                  if (uploadedModel != null) {
                    SnackBars.showInfoSnackBar(context, 'Image added');
                  } else {
                    SnackBars.showInfoSnackBar(context, 'Failed to add image');
                  }
                }
              }
            },
          ),
        ],
      ),
      body: StreamBuilder(
        stream: context.read<ImageLibraryRepository>().getImagesStream(
          context.read<ConData>(),
          ImageLibraryType.reusableEventPrimary,
        ),
        builder: (context, snapshot) {
          final images = snapshot.data;
          if (images == null || images.isEmpty) {
            return const ContentArea(fillWidth: false, child: Text('No reusable event images found'));
          }

          return ImageLibrary(
            icon: Icons.delete,
            images: images,
            onSelect: (image) async => await _showDeleteImageConfirmation(context, image),
          );
        },
      ),
    );
  }

  Future<void> _showDeleteImageConfirmation(BuildContext context, ImageModel imageModel) async {
    final id = imageModel.id;
    if (id == null) {
      return;
    }

    final confirmation = await Dialogs.showConfirmationDialog(context, title: 'Delete Image?');

    if (confirmation == true && context.mounted) {
      Dialogs.showLoadingDialog(context);

      final conData = context.read<ConData>();
      final imageLibraryRepository = context.read<ImageLibraryRepository>();

      bool success = await imageLibraryRepository.deleteImage(conData, ImageLibraryType.reusableEventPrimary, id);

      if (context.mounted) {
        Navigator.of(context, rootNavigator: true).pop();
        if (success) {
          SnackBars.showInfoSnackBar(context, 'Image deleted');
        } else {
          SnackBars.showInfoSnackBar(context, 'Failed to delete image');
        }
      }
    }
  }
}
