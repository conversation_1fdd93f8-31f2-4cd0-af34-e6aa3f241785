import 'package:async/async.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:visibility_detector/visibility_detector.dart';

class AdScroller extends StatefulWidget {
  const AdScroller({super.key});

  @override
  State<AdScroller> createState() => _AdScrollerState();
}

class _AdScrollerState extends State<AdScroller> {
  static const Duration _scrollInterval = Duration(seconds: 5);
  static const int _scrollMilliseconds = 1000;
  static const int _reverseScrollMilliseconds = 150;
  static const int _numAds = 6;

  final _pageController = PageController();
  late final RestartableTimer _timer;

  bool _isFullPage = true;
  bool _isVisible = false;

  @override
  void initState() {
    super.initState();
    _timer = RestartableTimer(_scrollInterval, () {
      int targetPage = (_pageController.page?.round() ?? 0) + 1;
      int scrollMilliseconds = _scrollMilliseconds;
      if (targetPage == _numAds) {
        targetPage = 0;
        scrollMilliseconds = _numAds * _reverseScrollMilliseconds;
      }
      _pageController.animateToPage(
        targetPage,
        duration: Duration(milliseconds: scrollMilliseconds),
        curve: Curves.easeInOut,
      );
    });
    _pageController.addListener(() {
      _isFullPage = _pageController.page == _pageController.page?.round();
      _tryTimerStart();
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: ValueKey('${context.read<ConData>().conWidgetKey}-AdScroller'),
      onVisibilityChanged: (info) {
        _isVisible = info.visibleFraction > 0;
        _tryTimerStart();
      },
      child: SizedBox(
        height: 200,
        child: PageView(
          controller: _pageController,
          children: [
            _buildAdCard(context, '1'),
            _buildAdCard(context, '2'),
            _buildAdCard(context, '3'),
            _buildAdCard(context, '4'),
            _buildAdCard(context, '5'),
            _buildAdCard(context, '6'),
          ],
        ),
      ),
    );
  }

  void _tryTimerStart() {
    if (_isVisible && _isFullPage) {
      if (!_timer.isActive) {
        _timer.reset();
      }
    } else if (_timer.isActive) {
      _timer.cancel();
    }
  }

  Widget _buildAdCard(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.widgetPadding),
      child: ContentArea(
        onTap: () {},
        child: Column(
          children: [
            const Text('Ad here'),
            Center(child: Text(text)),
          ],
        ),
      ),
    );
  }
}
