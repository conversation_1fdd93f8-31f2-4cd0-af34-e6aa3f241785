import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:venvi/constants/device_platform.dart';
import 'package:venvi/data_sources/device_variables_local_data_source.dart';
import 'package:venvi/data_sources/push_token_data_source.dart';
import 'package:venvi/models/user_model.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/logger.dart';

class DeviceRepository {
  final DeviceVariablesLocalDataSource _deviceVariablesLocalDataSource;
  final PushTokenDataSource _pushTokenDataSource;
  final FirebaseFirestore _firestore;

  DeviceRepository(
    this._deviceVariablesLocalDataSource,
    this._pushTokenDataSource,
    this._firestore,
  );

  Stream<String> onTokenRefresh() {
    return _pushTokenDataSource.onTokenRefresh();
  }

  Future<bool> deviceNotificationsEnabled(String? userId, UserModel? userModel) async {
    if (kIsWeb || userModel == null || userModel.pushTokens == null || userModel.pushTokens!.isEmpty) {
      return false;
    }
    final devices = userModel.devices;
    if (devices == null) {
      return false;
    }
    final deviceId = await _deviceVariablesLocalDataSource.getDeviceId();
    if (deviceId == null) {
      return false;
    }

    return devices[deviceId]?.pushToken != null;
  }

  Future<bool> addDevice(String? userId, UserModel? userModel) async {
    final pushToken = await _pushTokenDataSource.getPushToken();
    return await _createDeviceProfile(userId, userModel, pushToken);
  }

  Future<bool> updateDeviceToken(String? userId, UserModel? userModel, String? pushToken) async {
    return await _createDeviceProfile(userId, userModel, pushToken);
  }

  Future<bool> enableUserInteractiveDeviceNotifications(
    AuthRepository authRepository,
    UserModel userModel, {
    BuildContext? errorMessageContext,
  }) async {
    if (kIsWeb) {
      return false;
    }

    // Attempts to get push token
    final requestPermissionSuccessful = await _pushTokenDataSource.requestPermission();
    if (requestPermissionSuccessful == true) {
      final pushToken = await _pushTokenDataSource.getPushToken();
      if (pushToken == null) {
        if (errorMessageContext != null && errorMessageContext.mounted) {
          await Dialogs.showErrorDialog(
            errorMessageContext,
            message: 'Could not connect to online service',
          );
        }
        return false;
      }
      return await _createDeviceProfile(authRepository.currentUserId, userModel, pushToken);
    } else {
      if (errorMessageContext != null && errorMessageContext.mounted) {
        await Dialogs.showPermissionDeniedDialog(errorMessageContext);
      }
      return false;
    }
  }

  Future<bool> disableDeviceNotifications(String? userId, UserModel? userModel) async {
    if (kIsWeb) {
      return false;
    }
    return await _createDeviceProfile(userId, userModel, null);
  }

  Future<bool> removeDevice(String? userId, UserModel? userModel) async {
    if (userId == null) {
      return false;
    }

    final deviceId = await _deviceVariablesLocalDataSource.getDeviceId();
    if (deviceId == null) {
      return false;
    }

    final pushToken = userModel?.devices?[deviceId]?.pushToken;

    final ref = _firestore.collection('users').doc(userId);
    try {
      if (pushToken == null) {
        await ref.update({
          'devices.$deviceId': FieldValue.delete(),
        });
      } else {
        await ref.update({
          'devices.$deviceId': FieldValue.delete(),
          'pushTokens': FieldValue.arrayRemove([pushToken]),
        });
      }
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to remove device: $deviceId',
        documentReference: ref,
      );
      return false;
    }
  }

  Future<bool> _createDeviceProfile(
    String? userId,
    UserModel? userModel,
    String? newToken,
  ) async {
    if (userId == null) {
      return false;
    }

    final deviceId = await _deviceVariablesLocalDataSource.getDeviceId();
    if (deviceId == null) {
      return false;
    }

    final device = userModel?.devices?[deviceId];
    final deviceUpdates = <String, dynamic>{};
    deviceUpdates['deviceId'] = deviceId;
    deviceUpdates['devicePlatform'] = DevicePlatform.currentPlatform?.name;
    deviceUpdates['deviceAdded'] = device == null ? FieldValue.serverTimestamp() : device.deviceAdded;

    final currentPushToken = device?.pushToken;
    if (newToken != null) {
      deviceUpdates['pushToken'] = newToken;
      return await _updateUserDoc(
        userId,
        deviceId,
        deviceUpdates,
        addedPushToken: newToken,
        removedPushToken: currentPushToken,
      );
    } else {
      deviceUpdates['pushToken'] = null;
      return await _updateUserDoc(
        userId,
        deviceId,
        deviceUpdates,
        removedPushToken: currentPushToken,
      );
    }
  }

  Future<bool> _updateUserDoc(
    String? userId,
    String deviceId,
    Map<String, dynamic> deviceUpdates, {
    String? addedPushToken,
    String? removedPushToken,
  }) async {
    if (userId == null) {
      return false;
    }

    if (addedPushToken == removedPushToken) {
      removedPushToken = null;
    }

    try {
      // pushTokens only needs one update
      if (addedPushToken == null || removedPushToken == null) {
        if (addedPushToken != null) {
          return await _updateUserFields(userId, deviceId, deviceUpdates, FieldValue.arrayUnion([addedPushToken]));
        } else if (removedPushToken != null) {
          return await _updateUserFields(userId, deviceId, deviceUpdates, FieldValue.arrayRemove([removedPushToken]));
        } else {
          return await _updateUserFields(userId, deviceId, deviceUpdates, null);
        }
      } else {
        final ref = _firestore.collection('users').doc(userId);
        try {
          final batchUpdates = _firestore.batch();
          batchUpdates.update(ref, {
            'devices.$deviceId': deviceUpdates,
            'pushTokens': FieldValue.arrayUnion([addedPushToken]),
          });
          batchUpdates.update(ref, {
            'pushTokens': FieldValue.arrayRemove([removedPushToken]),
          });
          await batchUpdates.commit();
          return true;
        } catch (e, stackTrace) {
          if (e is FirebaseException && e.code == 'not-found') {
            // New user
            return await _updateUserFields(userId, deviceId, deviceUpdates, FieldValue.arrayUnion([addedPushToken]));
          } else {
            Logger.firestoreError(
              exception: e,
              stackTrace: stackTrace,
              message: 'Failed to update user doc devices',
              documentReference: ref,
            );
            return false;
          }
        }
      }
    } catch (e, stackTrace) {
      Logger.error(exception: e, stackTrace: stackTrace);
      return false;
    }
  }

  Future<bool> _updateUserFields(
      String userId, String deviceId, Map<String, dynamic> deviceUpdates, dynamic pushTokenUpdate) async {
    final updateData = <String, dynamic>{};
    if (deviceUpdates.isNotEmpty) {
      updateData['devices.$deviceId'] = deviceUpdates;
    }
    if (pushTokenUpdate != null) {
      updateData['pushTokens'] = pushTokenUpdate;
    }

    final ref = _firestore.collection('users').doc(userId);
    try {
      await ref.update(updateData);
      return true;
    } catch (e, stackTrace) {
      if (e is FirebaseException && e.code == 'not-found') {
        // New user
        await _firestore.collection('users').doc(userId).set(
          {
            'devices': {
              deviceId: deviceUpdates,
            },
            'pushTokens': pushTokenUpdate,
          },
          SetOptions(merge: true),
        );
        return true;
      } else {
        Logger.firestoreError(
          exception: e,
          stackTrace: stackTrace,
          message: 'Failed to update user doc devices',
          documentReference: ref,
        );
      }
    }
    return false;
  }
}
