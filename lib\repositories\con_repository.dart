import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/tier.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/con_model.dart';
import 'package:venvi/utils/logger.dart';

class ConRepository {
  final FirebaseFirestore _firestore;

  ConRepository(this._firestore);

  Future<ConModel?> getCon(ConData conData, {DataLocation? dataLocation, Duration? timeout}) async {
    final ref = _firestore.collection('orgs').doc(conData.orgId).collection('cons').doc(conData.conId);
    try {
      final snapshotFuture = ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));
      final snapshot = timeout != null ? await snapshotFuture.timeout(timeout) : await snapshotFuture;

      if (snapshot.exists) {
        final data = snapshot.data();
        if (data != null) {
          return ConModel.fromJson(data);
        }
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        documentReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<List<ConModel>?> getAllCons(String orgId, bool includeUnpublished, {DataLocation? dataLocation}) async {
    final ref = _firestore.collection('orgs').doc(orgId).collection('cons');
    final query = includeUnpublished ? ref : ref.where('isPublished', isEqualTo: true);
    try {
      final snapshot = await query.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));

      return snapshot.docs
          .map((doc) {
            try {
              return ConModel.fromJson(doc.data());
            } catch (e, stackTrace) {
              Logger.error(exception: e, stackTrace: stackTrace);
              return null;
            }
          })
          .whereType<ConModel>()
          .toList();
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get docs',
        collectionReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<bool> updateCon(ConData conData, ConModel con) async {
    final ref = _firestore.collection('orgs').doc(conData.orgId).collection('cons').doc(conData.conId);
    try {
      await ref.update(con.toJson());
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to update doc',
        documentReference: ref,
      );
      return false;
    }
  }

  Future<bool> upgradeToPremium(ConData conData) async {
    final ref = _firestore.collection('orgs').doc(conData.orgId).collection('cons').doc(conData.conId);
    try {
      await ref.update({'tier': Tier.premium.name});
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to upgrade to premium',
        documentReference: ref,
      );
      return false;
    }
  }
}
