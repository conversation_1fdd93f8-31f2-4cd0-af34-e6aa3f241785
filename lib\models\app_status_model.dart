import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_status_model.freezed.dart';
part 'app_status_model.g.dart';

@freezed
sealed class AppStatusModel with _$AppStatusModel {
  const factory AppStatusModel({bool? isActive, String? statusMessage, int? latestVersion, int? minSupportedVersion}) =
      _AppStatusModel;

  factory AppStatusModel.fromJson(Map<String, dynamic> json) => _$AppStatusModelFromJson(json);
}
