import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_network_image_platform_interface/cached_network_image_platform_interface.dart';
import 'package:flutter/material.dart';
import 'package:venvi/models/base_profile.dart';

class ProfilePhotoView extends StatelessWidget {
  final BaseProfile? profile;
  final double size;

  const ProfilePhotoView({super.key, required this.profile, required this.size});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircleAvatar(
        radius: double.infinity,
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
        child:
            profile?.photoUrl != null
                ? ClipOval(
                  child: CachedNetworkImage(
                    imageRenderMethodForWeb: ImageRenderMethodForWeb.HttpGet,
                    fit: BoxFit.contain,
                    imageUrl: profile!.photoUrl!,
                    placeholder: (context, url) => _buildInitialWidget(context),
                    errorWidget: (context, url, error) => _buildInitialWidget(context),
                  ),
                )
                : _buildInitialWidget(context),
      ),
    );
  }

  Widget _buildInitialWidget(BuildContext context) {
    return LayoutBuilder(
      builder:
          (context, constraints) =>
              profile?.displayName?.isNotEmpty == true
                  ? Text(
                    profile!.displayName![0].toUpperCase(),
                    style: TextStyle(
                      fontSize: constraints.maxHeight * 0.6,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  )
                  : Icon(
                    Icons.person,
                    size: constraints.maxHeight * 0.6,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
    );
  }
}
