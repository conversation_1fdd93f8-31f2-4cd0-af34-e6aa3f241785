import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/role_model.dart';
import 'package:venvi/repositories/role_repository.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class RolesReorderViewModel extends EditorScaffoldViewModel<List<RoleModel>> {
  final ConData _conData;
  final RoleRepository _repository;

  RolesReorderViewModel(super.initialState, this._conData, this._repository);

  @override
  bool checkChanges(List<RoleModel> initialState, List<RoleModel> currentState) {
    return initialState != currentState;
  }

  @override
  Future<List<RoleModel>> applyChanges(List<RoleModel> initialState, List<RoleModel> state) async {
    final orderedIds = state.map((e) => e.id).whereType<String>().toList();
    final updateSuccess = await _repository.updateOrderIndexes(_conData, orderedIds);
    if (updateSuccess) {
      return state;
    } else {
      throw const EditorScaffoldException(['Failed to update order']);
    }
  }

  void moveTile(int oldIndex, int newIndex) {
    if (newIndex < 0 || newIndex >= state.length) {
      return;
    }
    final storedItem = state[oldIndex];
    final newList = state.toList();
    newList.removeAt(oldIndex);
    newList.insert(newIndex, storedItem);
    emit(newList);
  }
}
