import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/json_converters/timestamp_converter.dart';

part 'announcement_model.freezed.dart';
part 'announcement_model.g.dart';

@freezed
sealed class AnnouncementModel with _$AnnouncementModel {
  const factory AnnouncementModel({
    String? id,
    @TimestampConverter() Timestamp? timestamp,
    String? createdBy,
    String? headline,
    String? details,
  }) = _AnnouncementModel;

  factory AnnouncementModel.fromJson(Map<String, dynamic> json) => _$AnnouncementModelFromJson(json);
}
