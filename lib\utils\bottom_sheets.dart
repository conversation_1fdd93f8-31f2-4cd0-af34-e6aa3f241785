import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:venvi/constants/image_library_type.dart';
import 'package:venvi/constants/image_type.dart';
import 'package:venvi/constants/rules.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/repositories/image_library_repository.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/image_selector.dart';
import 'package:venvi/widgets/image_library.dart';
import 'package:venvi/widgets/restricted_view.dart';

class BottomSheets {
  static Future<T?> showBottomSheet<T>({
    required BuildContext context,
    bool darkenBackground = true,
    Widget? child,
  }) async {
    return await showModalBottomSheet<T>(
      context: context,
      isScrollControlled: true,
      barrierColor: darkenBackground ? null : Colors.transparent,
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
        maxWidth: AppTheme.desktopWidth,
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.fromLTRB(
          AppTheme.widgetPadding,
          0,
          AppTheme.widgetPadding,
          AppTheme.widgetPadding,
        ),
        child: child,
      ),
    );
  }

  static Future<void> showEventImageBottomSheet(BuildContext context, Function(ImageModel model) onReusedImageSelected,
      Function(XFile file) onFileSelected) async {
    return await showBottomSheet(
      context: context,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          RestrictedView(
            rule: Rules.useReusableEventImages,
            child: Flexible(
              child: StreamBuilder(
                stream: context
                    .read<ImageLibraryRepository>()
                    .getImagesStream(context.read<ConData>(), ImageLibraryType.reusableEventPrimary),
                builder: (context, snapshot) {
                  final galleryImages = snapshot.data;
                  if (galleryImages == null || galleryImages.isEmpty) {
                    return const SizedBox.shrink();
                  }

                  return ImageLibrary(
                    icon: Icons.check,
                    images: galleryImages,
                    onSelect: (image) {
                      context.pop();
                      return onReusedImageSelected(image);
                    },
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: AppTheme.widgetPaddingSmall),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('Add Single Use Image'),
            style: ElevatedButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.onTertiary,
              iconColor: Theme.of(context).colorScheme.onTertiary,
              backgroundColor: Theme.of(context).colorScheme.tertiary,
            ),
            onPressed: () async {
              final file = await ImageSelector().selectImage(context, cropAspectRatio: 1);
              if (file != null && context.mounted) {
                context.pop();
                onFileSelected(file);
              }
            },
          ),
          RestrictedView(
            rule: Rules.useReusableEventImages,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.upload),
              label: const Text('Upload Reusable Image'),
              style: ElevatedButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.onTertiary,
                iconColor: Theme.of(context).colorScheme.onTertiary,
                backgroundColor: Theme.of(context).colorScheme.tertiary,
              ),
              onPressed: () async {
                final file = await ImageSelector().selectImage(context, cropAspectRatio: 1);
                if (file != null && context.mounted) {
                  context.pop();
                  final conData = context.read<ConData>();
                  final imageModel =
                      context.read<ImageRepository>().createImageModel(conData, ImageType.reusableEventPrimary);
                  final uploadedImage = await context
                      .read<ImageLibraryRepository>()
                      .createImage(conData, ImageLibraryType.reusableEventPrimary, imageModel, file);
                  if (uploadedImage != null) {
                    onReusedImageSelected(uploadedImage);
                  }
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
