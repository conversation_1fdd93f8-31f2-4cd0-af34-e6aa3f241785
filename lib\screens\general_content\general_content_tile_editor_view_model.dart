import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:venvi/screens/general_content/general_content_tile_state.dart';

class GeneralContentTileEditorViewModel extends Cubit<GeneralContentTileState> {
  final GeneralContentTileState _initialTileState;
  late GeneralContentTileState _tileState;

  bool get hasChanges => _initialTileState != _tileState;

  GeneralContentTileEditorViewModel(this._initialTileState) : super(_initialTileState) {
    _tileState = _initialTileState;
  }

  void setImage(XFile file) {
    _tileState = _tileState.copyWith(imageFile: file);
    emit(_tileState);
  }

  void updateHeadlineText(String text) {
    final newModel = _tileState.model.copyWith(headlineText: text.trim());
    _tileState = _tileState.copyWith(model: newModel);
    emit(_tileState);
  }

  void updateDetailsText(String text) {
    final newModel = _tileState.model.copyWith(detailsText: text.trim());
    _tileState = _tileState.copyWith(model: newModel);
    emit(_tileState);
  }

  void updateExternalLink(String? text) {
    text = text?.trim();
    final newModel = _tileState.model.copyWith(externalLink: text);
    _tileState = _tileState.copyWith(model: newModel);
    emit(_tileState);
  }
}
