import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/json_converters/timestamp_converter.dart';
import 'package:venvi/models/location_model.dart';
import 'package:venvi/models/summary_event_model.dart';

part 'summary_model.freezed.dart';
part 'summary_model.g.dart';

@freezed
sealed class SummaryModel with _$SummaryModel {
  const factory SummaryModel({
    String? id,
    @TimestampConverter() Timestamp? metadataTimestamp,
    String? orgId,
    String? conId,
    String? name,
    String? logoUrl,
    LocationModel? location,
    @TimestampConverter() Timestamp? startDate,
    @TimestampConverter() Timestamp? endDate,
    bool? isPublished,
    List<SummaryEventModel>? events,
  }) = _SummaryModel;

  factory SummaryModel.fromJson(Map<String, dynamic> json) => _$SummaryModelFromJson(json);
}
