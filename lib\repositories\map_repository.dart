import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/map_model.dart';
import 'package:venvi/utils/logger.dart';

class MapRepository {
  final FirebaseFirestore _firestore;

  MapRepository(this._firestore);

  Future<MapModel?> getMap(ConData conData, String id, {DataLocation? dataLocation}) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('maps')
        .doc(id);
    try {
      final snapshot = await ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));

      if (snapshot.exists) {
        final data = snapshot.data();
        if (data != null) {
          return MapModel.fromJson(data);
        }
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        documentReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<bool> updateAllMaps(ConData conData, List<MapModel> newModels) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('maps');
    try {
      final currentSnapshots = await ref.get();
      final currentIds = currentSnapshots.docs.map((e) => e.id).toList();
      final deletedIds = currentIds.where((id) => !newModels.any((model) => model.id == id)).toList();

      final batch = _firestore.batch();
      for (final id in deletedIds) {
        final ref = _firestore
            .collection('orgs')
            .doc(conData.orgId)
            .collection('cons')
            .doc(conData.conId)
            .collection('maps')
            .doc(id);
        batch.delete(ref);
      }
      for (int i = 0; i < newModels.length; i++) {
        final model = newModels[i];
        final populatedModel = model.copyWith(orderIndex: i);

        final ref = _firestore
            .collection('orgs')
            .doc(conData.orgId)
            .collection('cons')
            .doc(conData.conId)
            .collection('maps')
            .doc(populatedModel.id);
        batch.set(ref, populatedModel.toJson());
      }
      await batch.commit();
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to update all maps',
        collectionReference: ref,
      );
      return false;
    }
  }
}
