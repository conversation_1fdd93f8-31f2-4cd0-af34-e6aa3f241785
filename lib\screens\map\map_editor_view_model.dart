import 'package:image_picker/image_picker.dart';
import 'package:uuid/uuid.dart';
import 'package:venvi/constants/image_type.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/models/map_model.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/repositories/map_repository.dart';
import 'package:venvi/screens/map/map_editor_state.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class MapEditorViewModel extends EditorScaffoldViewModel<List<MapEditorState>> {
  final ConData _conData;
  final MapRepository _mapRepository;
  final ImageRepository _imageRepository;

  MapEditorViewModel(super.initialState, this._conData, this._mapRepository, this._imageRepository);

  @override
  bool checkChanges(List<MapEditorState> initialState, List<MapEditorState> currentState) {
    return initialState != currentState;
  }

  @override
  Future<List<MapEditorState>> applyChanges(List<MapEditorState> initialState, List<MapEditorState> state) async {
    final newMaps = state.map((map) => map.model).toList();
    final List<ImageModel> imageUploadModels = [];
    final List<XFile> imageUploadFiles = [];

    for (int i = 0; i < state.length; i++) {
      final imageFile = state[i].imageFile;
      // Uploads image if new one exists
      if (imageFile != null) {
        final imageModel = _imageRepository.createImageModel(_conData, ImageType.map);
        newMaps[i] = newMaps[i].copyWith(image: imageModel);
        imageUploadModels.add(imageModel);
        imageUploadFiles.add(imageFile);
      }
    }

    final mapsUpdated = await _mapRepository.updateAllMaps(_conData, newMaps);

    if (!mapsUpdated) {
      throw const EditorScaffoldException(['Failed to update maps']);
    }

    final List<Future<bool>> imageUploadFutures = [];
    for (int i = 0; i < imageUploadModels.length; i++) {
      imageUploadFutures.add(_imageRepository.uploadImage(imageUploadModels[i], imageUploadFiles[i]));
    }

    final imageUploadResults = await Future.wait(imageUploadFutures);
    if (imageUploadResults.contains(false)) {
      throw const EditorScaffoldException(['Failed to upload images']);
    }
    return state;
  }

  void moveMap(int oldIndex, int newIndex) {
    if (newIndex < 0 || newIndex >= state.length) {
      return;
    }
    final map = state[oldIndex];
    final newMaps = state.toList();
    newMaps.removeAt(oldIndex);
    newMaps.insert(newIndex, map);
    emit(newMaps);
  }

  void updateMap(int index, MapEditorState mapState) {
    final newMaps = state.toList();
    newMaps[index] = mapState;
    emit(newMaps);
  }

  void addMap() {
    final newMaps = state.toList();
    newMaps.add(MapEditorState(key: const Uuid().v4(), model: const MapModel()));
    emit(newMaps);
  }

  void removeMap(int index) {
    final newMaps = state.toList();
    newMaps.removeAt(index);
    emit(newMaps);
  }
}
