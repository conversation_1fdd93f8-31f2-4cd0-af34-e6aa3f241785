// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/cloud_message_type.dart';

part 'cloud_message_event_notification_model.freezed.dart';
part 'cloud_message_event_notification_model.g.dart';

@freezed
sealed class CloudMessageEventNotificationModel with _$CloudMessageEventNotificationModel {
  const factory CloudMessageEventNotificationModel({
    @JsonKey(unknownEnumValue: JsonKey.nullForUndefinedEnumValue) CloudMessageType? messageType,
    String? orgId,
    String? conId,
    String? eventId,
  }) = _CloudMessageEventNotificationModel;

  factory CloudMessageEventNotificationModel.fromJson(Map<String, dynamic> json) =>
      _$CloudMessageEventNotificationModelFromJson(json);
}
