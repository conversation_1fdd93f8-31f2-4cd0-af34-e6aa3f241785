import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_network_image_platform_interface/cached_network_image_platform_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/models/con_model.dart';
import 'package:venvi/theme.dart';

class AppBarLogo extends StatelessWidget {
  const AppBarLogo({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ConViewModel, ConModel?>(
      builder: (context, state) => state?.logo?.downloadUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(AppTheme.borderRadius),
              child: CachedNetworkImage(
                imageRenderMethodForWeb: ImageRenderMethodForWeb.HttpGet,
                height: 50,
                fit: BoxFit.contain,
                imageUrl: state!.logo!.downloadUrl!,
                placeholder: (context, url) => Text(
                  state.name ?? '',
                  style: Theme.of(context).appBarTheme.titleTextStyle,
                ),
                errorWidget: (context, url, error) => Text(
                  state.name ?? '',
                  style: Theme.of(context).appBarTheme.titleTextStyle,
                ),
              ),
            )
          : Text(
              state?.name ?? '',
              style: Theme.of(context).appBarTheme.titleTextStyle,
            ),
    );
  }
}
