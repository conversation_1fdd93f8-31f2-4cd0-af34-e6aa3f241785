// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/cloud_message_type.dart';

part 'cloud_message_user_data_model.freezed.dart';
part 'cloud_message_user_data_model.g.dart';

@freezed
sealed class CloudMessageUserDataModel with _$CloudMessageUserDataModel {
  const factory CloudMessageUserDataModel({
    @JsonKey(unknownEnumValue: JsonKey.nullForUndefinedEnumValue) CloudMessageType? messageType,
  }) = _CloudMessageUserDataModel;

  factory CloudMessageUserDataModel.fromJson(Map<String, dynamic> json) => _$CloudMessageUserDataModelFromJson(json);
}
