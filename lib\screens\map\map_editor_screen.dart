import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:uuid/uuid.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/map_view_model.dart';
import 'package:venvi/models/map_model.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/repositories/map_repository.dart';
import 'package:venvi/screens/map/map_editor_state.dart';
import 'package:venvi/screens/map/map_editor_tile.dart';
import 'package:venvi/screens/map/map_editor_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/editor_scaffold.dart';
import 'package:venvi/widgets/glass_tile_button.dart';

class MapEditorScreen extends HookWidget {
  const MapEditorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final scrollController = useScrollController();

    return EditorScaffold<MapEditorViewModel, List<MapEditorState>, MapViewModel, List<MapModel>?>(
      externalCubit: context.read<MapViewModel>(),
      buildState: (externalCubitState) {
        final maps = List<MapModel>.from(externalCubitState ?? []);
        return maps.map((map) => MapEditorState(key: const Uuid().v4(), model: map)).toList();
      },
      initViewModel:
          (state) => MapEditorViewModel(
            state,
            context.read<ConData>(),
            context.read<MapRepository>(),
            context.read<ImageRepository>(),
          ),
      title: const Text('Map Editor'),
      successMessage: 'Maps Updated Successfully',
      buildContent:
          (context, viewModel, state) => ListView(
            padding: const EdgeInsets.all(AppTheme.screenPadding),
            controller: scrollController,
            children: [
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: state.length,
                separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
                itemBuilder:
                    (context, index) => MapEditorTile(
                      conData: context.read<ConData>(),
                      index: index,
                      mapState: state[index],
                      viewModel: viewModel,
                    ),
              ),
              if (state.length < InputConstants.maxMaps)
                Padding(
                  padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                  child: ContentArea(
                    padding: EdgeInsets.zero,
                    child: GlassTileButton(
                      text: 'Add Map',
                      icon: Icons.add,
                      onTap: () {
                        viewModel.addMap();
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          scrollController.animateTo(
                            scrollController.position.maxScrollExtent,
                            duration: const Duration(milliseconds: 500),
                            curve: Curves.easeInOut,
                          );
                        });
                      },
                    ),
                  ),
                ),
            ],
          ),
    );
  }
}
